@echo off
REM ==============================================
REM  Hitem3D Automation One-Click Startup Script
REM  This script launches both the email automation
REM  and registration automation components in
REM  separate console windows.
REM ==============================================

REM 确保当前工作目录为脚本所在目录
cd /d %~dp0

echo.
echo ==============================================
echo   正在启动 Hitem3D 自动化脚本...
echo ==============================================
echo.

REM 启动临时邮箱自动化脚本
echo 启动 Hitem3D 邮箱自动化脚本...
start "Hitem3D_Email_Automation" python hitem3d_email_automation.py

REM 稍作等待，避免端口/资源竞争
timeout /t 2 /nobreak >nul

REM 启动注册自动化脚本
echo 启动 Hitem3D 注册自动化脚本...
start "Hitem3D_Registration_Automation" python hitem3d_registration.py

echo.
echo 所有 Hitem3D 自动化脚本已启动！
echo 关闭此窗口并不会终止脚本，它们在独立窗口中运行。

echo.
pause 