@echo off
echo Starting BFL.AI Automation Projects...
echo.

echo Starting Email Automation...
start "Email_Automation" python email_automation.py
timeout /t 2 /nobreak >nul

echo Starting Register Automation...
start "Register_Automation" python open_register_new.py
timeout /t 2 /nobreak >nul

echo Starting BFL Login Automation...
start "BFL_Login_Automation" python bfl_login_automation.py --headless
timeout /t 2 /nobreak >nul

echo.
echo All projects started successfully!
echo To stop all projects, run: stop_simple.bat
echo.
pause
