{"timestamp": 1749504716.6899312, "session_id": "session_1749504716689_uipi8u6hk6c7ghtk", "version": "2.0.0", "config": {"user_agent": "Mozilla/5.0 (Windows NT 11.0; Win64; ARM64) AppleWebKit/537.42 (KHTML, like Gecko) Chrome/********* Safari/537.42", "screen_resolution": [1024, 768], "viewport_size": [1011, 667], "color_depth": 24, "pixel_ratio": 2.5, "hardware_concurrency": 6, "device_memory": 8, "max_touch_points": 5, "timezone_offset": 480, "timezone_name": "Europe/London", "languages": ["ru-RU", "ru", "en"], "locale": "zh-CN", "connection_info": {"type": "unknown", "effective_type": "2g", "downlink": 7.4, "rtt": 182, "save_data": false}, "media_devices": {"audio_inputs": 4, "audio_outputs": 4, "video_inputs": 1}, "canvas_fingerprint": {"noise_level": 0.0954, "text_metrics_variation": 1.63, "color_variation": 5, "font_rendering_variation": 0.95}, "webgl_info": {"vendor": "Google Inc. (Intel)", "renderer": "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)", "version": "OpenGL ES 2.0 (ANGLE 2.5.4742)", "shading_language_version": "OpenGL ES GLSL ES 1.00 (ANGLE 3.3.585)", "max_texture_size": 16384, "max_viewport_dims": [16384, 4096]}, "fonts_info": {"available_fonts": ["<PERSON><PERSON>", "Trebuchet MS", "Georgia", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lucida Sans Unicode", "Bookman", "<PERSON><PERSON><PERSON>", "Monaco", "Times New Roman", "<PERSON><PERSON><PERSON>", "Impact", "DejaVu Sans", "San Francisco", "Consolas", "Calib<PERSON>", "Noto Sans", "Segoe UI", "Roboto", "Cambria", "Helvetica Neue", "Ubuntu"], "font_count": 22, "font_fingerprint": "cac1b211323d58c2"}, "plugins_info": {"plugins": [{"name": "Chrome PDF Plugin", "filename": "internal-pdf-viewer", "description": "Portable Document Format"}, {"name": "Chrome PDF Viewer", "filename": "mhjfbmdgcfjbbpaeojofohoefgie<PERSON>i", "description": "PDF Viewer"}, {"name": "Widevine Content Decryption Module", "filename": "widevinecdmadapter.dll", "description": "Enables Widevine licenses for playback of HTML audio/video content"}], "plugin_count": 3}, "battery_info": {"charging": true, "level": 0.25, "charging_time": 5976, "discharging_time": Infinity}, "performance_info": {"used_js_heap_size": 647703026, "total_js_heap_size": **********, "js_heap_size_limit": **********, "memory_info": {"used": 647703026, "total": **********, "limit": **********}}, "audio_fingerprint": {"sample_rate": 44100, "buffer_size": 512, "channels": 6, "bit_depth": 16, "noise_level": 0.008962, "frequency_response": [-0.52, -1.63, -0.08, -1.16, 0.74, -2.48, -2.02, 0.53, 2.93, -1.14]}, "behavior_patterns": {"mouse_movement_speed": 1.75, "click_delay_range": [164, 503], "typing_speed": 253, "scroll_behavior": {"speed": 1.17, "smoothness": 0.11}, "interaction_patterns": {"double_click_threshold": 373, "hover_delay": 428, "focus_behavior": "fast"}}, "timing_info": {"performance_now_offset": 43.416, "date_now_offset": 326, "animation_frame_rate": 75, "timer_resolution": 1.7, "high_res_time_precision": 5.0}, "advanced_evasion": {"webdriver_evasion": {"remove_webdriver_property": true, "override_chrome_runtime": true, "mask_automation_extensions": true, "randomize_chrome_object": true}, "headless_evasion": {"fake_user_interaction": true, "simulate_real_viewport": true, "add_missing_properties": true, "randomize_missing_features": true}, "fingerprint_resistance": {"canvas_noise": true, "webgl_noise": true, "audio_noise": true, "font_randomization": true, "timezone_spoofing": true}, "behavioral_mimicking": {"human_like_delays": true, "natural_mouse_movement": true, "realistic_typing_patterns": true, "random_page_interactions": true}}}}