#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hitem3D临时邮箱自动化脚本
自动获取临时邮箱，等待邮件，提取验证码并保存
"""

import time
import re
import json
import pyperclip

import subprocess
import sys
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hitem3d_registration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Hitem3DEmailAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.results = []
        self.email_file = "current_email.txt"
        self.verification_code_file = "verification_code.txt"  # 新增：验证码文件
        self.request_file = "request_new_email.txt"
        self.monitoring_requests = False
        self.current_email = ""
        self.refresh_count = 0
        self.proxy_switch_count = 0
        self.switch_proxy_file = "switch_proxy_request.txt"
        self.reset_count_file = "reset_refresh_count.txt"
        self.monitoring_process = False
        self.process_monitor_thread = None
        self.monitoring_hitem3d_registration = False
        self.hitem3d_registration_monitor_thread = None
        self.registration_completed_file = "registration_completed.txt"  # 注册完成标记文件
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器"""
        chrome_options = Options()

        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 如果启用无头模式（推荐用于后台运行）
        if headless:
            chrome_options.add_argument('--headless=new')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            logger.info("启用无头浏览器模式（后台运行）")
        else:
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            logger.info("启用后台运行优化模式")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("浏览器启动成功")
            return True
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            return False
    
    def open_website(self):
        """打开临时邮箱网站"""
        try:
            self.driver.get("https://tempmail.la/zh-CN")
            logger.info("正在加载 tempmail.la...")
            
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Temp Mail')]"))
                )
                logger.info("tempmail.la 页面加载完成")
            except TimeoutException:
                logger.warning("等待页面加载超时，但继续执行")
            
            return True
        except Exception as e:
            logger.error(f"打开网站失败: {e}")
            return False
    
    def create_temp_email(self):
        """点击创建临时邮箱按钮并等待弹窗"""
        try:
            create_button = None
            
            try:
                create_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '创建临时邮箱')]"
                )
            except:
                pass
                
            if not create_button:
                try:
                    create_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-mail')]]"
                    )
                except:
                    pass
            
            if create_button:
                create_button.click()
                logger.info("点击创建临时邮箱按钮")
                
                try:
                    success_toast = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), '临时邮箱已创建')]"))
                    )
                    logger.info("检测到临时邮箱创建成功弹窗")
                    return True
                except TimeoutException:
                    logger.info("未检测到临时邮箱创建成功弹窗，但继续执行")
                    return True
            else:
                logger.info("未找到创建临时邮箱按钮，可能已经创建过邮箱")
                return False
                
        except Exception as e:
            logger.error(f"创建临时邮箱失败: {e}")
            return False
    
    def get_email_address(self):
        """获取当前邮箱地址并写入共享文件"""
        try:
            logger.info("正在获取邮箱地址...")
            time.sleep(2)
            
            email_address = None
            
            # 方法1: 从页面源码中提取邮箱地址
            try:
                page_source = self.driver.page_source
                email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
                matches = re.findall(email_pattern, page_source)
                if matches:
                    for match in matches:
                        if not match.endswith('.png') and not match.endswith('.jpg') and '@' in match:
                            email_address = match
                            logger.info(f"成功获取邮箱地址: {email_address}")
                            break
            except Exception as e:
                logger.debug(f"从页面源码提取邮箱地址失败: {e}")
            
            # 方法2: 从页面元素获取
            if not email_address:
                try:
                    email_elements = self.driver.find_elements(By.XPATH, "//p[contains(@class, 'font-bold') and contains(text(), '@')]")
                    if email_elements:
                        text = email_elements[0].text.strip()
                        if '@' in text:
                            email_address = text
                            logger.info(f"成功获取邮箱地址: {email_address}")
                except Exception as e:
                    logger.debug(f"从页面元素获取邮箱地址失败: {e}")
            
            # 方法3: 从输入框获取
            if not email_address:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, 'input[readonly]')
                    email_address = email_input.get_attribute('value')
                    if email_address:
                        logger.info(f"成功获取邮箱地址: {email_address}")
                except Exception as e:
                    logger.debug(f"从输入框获取邮箱地址失败: {e}")
            
            if not email_address:
                logger.error("无法获取邮箱地址")
                try:
                    screenshot_path = f"email_error_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.error(f"已保存页面截图: {screenshot_path}")
                except:
                    pass
                return None
            
            self.current_email = email_address
            self.write_email_to_file(email_address)
            self.copy_email_and_update_file(email_address)
            
            logger.info(f"邮箱地址已写入文件并复制到剪贴板: {email_address} (刷新计数: {self.refresh_count})")
            return email_address
        except Exception as e:
            logger.error(f"获取邮箱地址失败: {e}")
            return None

    def write_email_to_file(self, email_address):
        """将邮箱地址写入共享文件"""
        try:
            with open(self.email_file, 'w', encoding='utf-8') as f:
                f.write(email_address)
            logger.info(f"邮箱地址已写入文件: {email_address}")
        except Exception as e:
            logger.error(f"写入邮箱文件失败: {e}")

    def copy_email_and_update_file(self, email_address):
        """复制邮箱地址到剪切板并更新文件"""
        try:
            copy_success = False

            # 方法1: 使用pyperclip
            try:
                pyperclip.copy(email_address)
                logger.info(f"方法1: 通过pyperclip复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.warning(f"pyperclip复制失败: {e}")

            # 方法2: 尝试使用JavaScript通过Clipboard API复制
            try:
                copy_script = f"""
                    (async () => {{
                        try {{
                            await navigator.clipboard.writeText('{email_address}');
                            console.log('通过Clipboard API复制成功');
                            return true;
                        }} catch (e) {{
                            console.error('Clipboard API复制失败:', e);
                            return false;
                        }}
                    }})();
                """
                self.driver.execute_script(copy_script)
                logger.info(f"方法2: 通过浏览器Clipboard API复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.debug(f"通过JavaScript复制失败: {e}")

            if copy_success:
                logger.info(f"✅ 邮箱地址已成功复制到剪切板: {email_address}")
            else:
                logger.warning(f"⚠️ 所有复制方法都失败了: {email_address}")

            self.write_email_to_file(email_address)
            self.current_email = email_address

        except Exception as e:
            logger.error(f"复制邮箱地址并更新文件失败: {e}")

    def monitor_new_email_requests(self):
        """监控新邮箱请求"""
        import threading
        import time

        def monitor_thread():
            logger.info("开始监控新邮箱请求...")
            self.monitoring_requests = True

            while self.monitoring_requests:
                try:
                    if os.path.exists(self.request_file):
                        logger.info("检测到新邮箱请求，生成新邮箱...")
                        os.remove(self.request_file)

                        if self.generate_new_address():
                            new_email = self.get_email_address()
                            if new_email:
                                self.current_email = new_email
                                logger.info(f"新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                                logger.info(f"刷新计数器已重置为0")
                                # 验证码已在generate_new_address中清空
                            else:
                                logger.error("生成新邮箱后无法获取地址")
                        else:
                            logger.error("生成新邮箱失败")

                    time.sleep(2)

                except Exception as e:
                    logger.error(f"监控新邮箱请求时出错: {e}")
                    time.sleep(2)

        monitor_thread_obj = threading.Thread(target=monitor_thread, daemon=True)
        monitor_thread_obj.start()

    def monitor_registration_status(self):
        """监控注册状态，等待注册完成"""
        import threading
        import time

        def monitor_thread():
            logger.info("开始监控注册状态...")
            self.monitoring_hitem3d_registration = True

            while self.monitoring_hitem3d_registration:
                try:
                    # 注册完成检测现在由主循环处理，这里不再处理
                    # if os.path.exists(self.registration_completed_file):
                    #     logger.info("检测到注册完成标记，可以切换邮箱")
                    #     os.remove(self.registration_completed_file)
                    #     break
                    
                    # 检查邮箱文件是否有变化（新邮箱地址）
                    if os.path.exists(self.email_file):
                        with open(self.email_file, 'r', encoding='utf-8') as f:
                            new_email = f.read().strip()
                        
                        if new_email and new_email != self.current_email:
                            logger.info(f"检测到注册脚本获取了新邮箱: {new_email}")
                            logger.info("等待注册完成后再切换邮箱...")
                            self.current_email = new_email
                    
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"监控注册状态时出错: {e}")
                    time.sleep(2)

        self.hitem3d_registration_monitor_thread = threading.Thread(target=monitor_thread, daemon=True)
        self.hitem3d_registration_monitor_thread.start()

    def click_refresh_button(self):
        """点击刷新邮件按钮"""
        try:
            try:
                self.driver.execute_script("""
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以进行刷新操作")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            refresh_button = None

            # 方法1: 查找包含刷新邮件文本和refresh-cw图标的按钮
            try:
                refresh_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '刷新邮件') and .//svg[contains(@class, 'lucide-refresh-cw')]]"
                )
                logger.debug("通过方法1找到刷新按钮")
            except:
                pass

            # 方法2: 只查找包含"刷新邮件"文本的按钮
            if not refresh_button:
                try:
                    refresh_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '刷新邮件')]"
                    )
                    logger.debug("通过方法2找到刷新按钮")
                except:
                    pass

            # 方法3: 通过refresh-cw图标查找
            if not refresh_button:
                try:
                    refresh_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-refresh-cw')]]"
                    )
                    logger.debug("通过方法3找到刷新按钮")
                except:
                    pass

            if refresh_button:
                try:
                    refresh_button.click()
                    logger.debug("成功点击刷新按钮")
                except Exception as click_error:
                    try:
                        self.driver.execute_script("arguments[0].click();", refresh_button)
                        logger.debug("通过JavaScript成功点击刷新按钮")
                    except Exception as js_error:
                        logger.error(f"点击刷新按钮失败: 普通点击错误={click_error}, JS点击错误={js_error}")
                        return False

                self.refresh_count += 1
                self.proxy_switch_count += 1
                logger.info(f"🔄 点击刷新邮件按钮 (总计第{self.refresh_count}次，代理切换计数第{self.proxy_switch_count}次) - 当前邮箱: {self.current_email}")

                # 每10次刷新复制一遍邮箱地址（注意：不换邮箱，只复制地址）
                if self.refresh_count % 10 == 0 and self.current_email:
                    logger.info(f"已刷新{self.refresh_count}次，重新复制邮箱地址到剪贴板并更新文件")
                    self.copy_email_and_update_file(self.current_email)

                time.sleep(2)
                return True
            else:
                logger.error("所有方法都无法找到刷新按钮")
                return False

        except Exception as e:
            logger.error(f"点击刷新按钮失败: {e}")
            return False
    
    def check_for_emails(self):
        """检查是否有新邮件"""
        try:
            email_items = self.driver.find_elements(
                By.XPATH, "//li[contains(@class, 'cursor-pointer')]"
            )
            
            if email_items:
                logger.info(f"发现 {len(email_items)} 封邮件")
                return email_items
            else:
                try:
                    inbox_div = self.driver.find_element(
                        By.XPATH, "//div[.//h3[contains(text(), '收件箱')]]"
                    )
                    email_items = inbox_div.find_elements(
                        By.XPATH, ".//li[contains(@class, 'cursor-pointer')]"
                    )
                    if email_items:
                        logger.info(f"备用方法发现 {len(email_items)} 封邮件")
                        return email_items
                except:
                    pass
                
                logger.info("暂无邮件")
                return []
        except Exception as e:
            logger.error(f"检查邮件失败: {e}")
            return []
    
    def click_email_and_extract_verification_code(self, email_element):
        """点击邮件并提取验证码"""
        try:
            # 滚动到元素位置确保可见
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", email_element)
                logger.info("滚动到邮件位置")
                time.sleep(1)
            except Exception as e:
                logger.debug(f"滚动到邮件位置失败: {e}")

            # 尝试点击邮件
            clicked = False
            
            # 方法1: 使用JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", email_element)
                logger.info("通过JavaScript点击邮件")
                clicked = True
            except Exception as e:
                logger.debug(f"JavaScript点击邮件失败: {e}")
            
            # 方法2: 常规点击
            if not clicked:
                try:
                    self.wait.until(EC.element_to_be_clickable((By.XPATH, "//li[contains(@class, 'cursor-pointer')]")))
                    email_element.click()
                    logger.info("通过普通方式点击邮件")
                    clicked = True
                except Exception as e:
                    logger.warning(f"普通点击邮件失败: {e}")
            
            if not clicked:
                logger.error("所有点击方法都失败")
                return None
            
            logger.info("点击邮件")

            # 触发页面活动，确保内容加载
            try:
                self.driver.execute_script("""
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    if (document.hidden) {
                        document.hidden = false;
                    }
                """)
                logger.debug("触发页面活动事件以确保后台加载")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            time.sleep(5)

            # 查找iframe中的验证码
            iframe = None
            for attempt in range(5):
                try:
                    logger.info(f"第{attempt + 1}次尝试查找iframe...")

                    self.driver.execute_script("document.body.offsetHeight;")

                    iframe = self.wait.until(
                        EC.presence_of_element_located((By.TAG_NAME, 'iframe'))
                    )
                    logger.info("成功找到iframe")
                    break
                except TimeoutException:
                    logger.warning(f"第{attempt + 1}次查找iframe超时")
                    if attempt < 4:
                        try:
                            self.driver.execute_script("""
                                window.dispatchEvent(new Event('resize'));
                                document.dispatchEvent(new Event('DOMContentLoaded'));
                                window.scrollTo(0, 100);
                                window.scrollTo(0, 0);
                            """)
                        except:
                            pass
                        time.sleep(3)
                        continue
                    else:
                        logger.warning("所有尝试都失败，未找到iframe")
                        self.click_back_button()
                        return None

            if iframe:
                # 获取iframe的srcdoc内容
                srcdoc = iframe.get_attribute('srcdoc')
                if srcdoc:
                    # 提取6位验证码
                    verification_code = self.extract_verification_code(srcdoc)
                    
                    if verification_code:
                        logger.info(f"提取到验证码: {verification_code}")
                        
                        # 保存验证码到文件
                        self.save_verification_code(verification_code)
                        
                        # 点击返回按钮
                        self.click_back_button()
                        
                        return verification_code
                    else:
                        logger.warning("未找到验证码")
                        # 记录iframe内容用于调试
                        debug_content = srcdoc[:200] + "..." if len(srcdoc) > 200 else srcdoc
                        logger.debug(f"iframe内容片段: {debug_content}")
                        
                        self.click_back_button()
                        return None

        except Exception as e:
            logger.error(f"点击邮件并提取验证码失败: {e}")
            return None

    def extract_verification_code(self, html_content):
        """从HTML内容中提取6位验证码"""
        try:
            logger.info("开始提取验证码...")
            
            # 方法1: 查找带有特定样式的6位数字
            # 基于用户提供的HTML结构：<font style="...">619844</font>
            pattern1 = r'<font[^>]*>(\d{6})</font>'
            matches1 = re.findall(pattern1, html_content)
            if matches1:
                verification_code = matches1[0]
                logger.info(f"方法1成功提取验证码: {verification_code}")
                return verification_code
            
            # 方法2: 查找任何6位连续数字
            pattern2 = r'\b(\d{6})\b'
            matches2 = re.findall(pattern2, html_content)
            if matches2:
                verification_code = matches2[0]
                logger.info(f"方法2成功提取验证码: {verification_code}")
                return verification_code
            
            # 方法3: 查找包含"verification code"或类似文本附近的数字
            pattern3 = r'(?i)(?:verification\s*code|code|验证码)[^0-9]*(\d{6})'
            matches3 = re.findall(pattern3, html_content)
            if matches3:
                verification_code = matches3[0]
                logger.info(f"方法3成功提取验证码: {verification_code}")
                return verification_code
            
            # 方法4: 在整个文本中查找所有6位数字，选择第一个
            all_six_digit_numbers = re.findall(r'\d{6}', html_content)
            if all_six_digit_numbers:
                verification_code = all_six_digit_numbers[0]
                logger.info(f"方法4成功提取验证码: {verification_code}")
                return verification_code
            
            logger.warning("所有方法都未能提取到验证码")
            return None
            
        except Exception as e:
            logger.error(f"提取验证码时出错: {e}")
            return None

    def clear_verification_code(self):
        """清空验证码文件"""
        try:
            # 清空验证码文件
            with open(self.verification_code_file, 'w', encoding='utf-8') as f:
                f.write("")
            logger.info("已清空验证码文件")
            
            # 也清空剪贴板中的验证码
            try:
                pyperclip.copy("")
                logger.debug("已清空剪贴板中的验证码")
            except:
                pass
                
        except Exception as e:
            logger.error(f"清空验证码文件失败: {e}")

    def save_verification_code(self, verification_code):
        """保存验证码到文件"""
        try:
            with open(self.verification_code_file, 'w', encoding='utf-8') as f:
                f.write(verification_code)
            logger.info(f"验证码已保存到文件: {verification_code}")
            
            # 也复制到剪贴板方便使用
            try:
                pyperclip.copy(verification_code)
                logger.info(f"验证码已复制到剪贴板: {verification_code}")
            except:
                pass
                
        except Exception as e:
            logger.error(f"保存验证码失败: {e}")

    def click_back_button(self):
        """点击返回按钮"""
        try:
            try:
                self.driver.execute_script("""
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以查找返回按钮")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            back_button = None
            for attempt in range(5):
                logger.info(f"第{attempt + 1}次尝试查找返回按钮...")

                try:
                    self.driver.execute_script("document.body.offsetHeight;")
                except:
                    pass

                # 方法1: 通过文本和图标查找
                try:
                    back_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '返回') and .//svg[contains(@class, 'lucide-chevron-left')]]"
                    )
                    logger.debug("通过方法1找到返回按钮")
                    break
                except:
                    pass

                # 方法2: 只通过文本查找
                if not back_button:
                    try:
                        back_button = self.driver.find_element(
                            By.XPATH, "//button[contains(., '返回')]"
                        )
                        logger.debug("通过方法2找到返回按钮")
                        break
                    except:
                        pass

                # 方法3: 通过图标查找
                if not back_button:
                    try:
                        back_button = self.driver.find_element(
                            By.XPATH, "//button[.//svg[contains(@class, 'lucide-chevron-left')]]"
                        )
                        logger.debug("通过方法3找到返回按钮")
                        break
                    except:
                        pass

                if not back_button and attempt < 4:
                    logger.warning(f"第{attempt + 1}次未找到返回按钮，等待2秒后重试...")
                    try:
                        self.driver.execute_script("""
                            window.dispatchEvent(new Event('resize'));
                            document.dispatchEvent(new Event('DOMContentLoaded'));
                        """)
                    except:
                        pass
                    time.sleep(2)

            if back_button:
                try:
                    back_button.click()
                    logger.info("成功点击返回按钮")
                except Exception as click_error:
                    try:
                        self.driver.execute_script("arguments[0].click();", back_button)
                        logger.info("通过JavaScript成功点击返回按钮")
                    except Exception as js_error:
                        logger.error(f"点击返回按钮失败: 普通点击错误={click_error}, JS点击错误={js_error}")
                        return False

                time.sleep(2)
                return True
            else:
                logger.warning("未找到返回按钮")
                return False

        except Exception as e:
            logger.error(f"点击返回按钮失败: {e}")
            return False
    
    def generate_new_address(self):
        """生成新的邮箱地址"""
        try:
            logger.info("🔄 正在切换邮箱...")
            try:
                self.driver.execute_script("""
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以生成新邮箱")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            new_address_button = None

            # 方法1: 查找包含"换邮箱"文本和pen-line图标的按钮
            try:
                new_address_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '换邮箱') and .//svg[contains(@class, 'lucide-pen-line')]]"
                )
                logger.info("✅ 通过方法1找到换邮箱按钮")
            except Exception as e1:
                logger.debug(f"方法1失败: {e1}")

            # 方法2: 只通过"换邮箱"文本查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '换邮箱')]"
                    )
                    logger.info("✅ 通过方法2找到换邮箱按钮")
                except Exception as e2:
                    logger.debug(f"方法2失败: {e2}")

            # 方法3: 通过pen-line图标查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-pen-line')]]"
                    )
                    logger.info("✅ 通过方法3找到换邮箱按钮")
                except Exception as e3:
                    logger.debug(f"方法3失败: {e3}")

            # 方法4: 通过更宽泛的CSS选择器查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.CSS_SELECTOR, "button:has(svg.lucide-pen-line)"
                    )
                    logger.info("✅ 通过方法4找到换邮箱按钮")
                except Exception as e4:
                    logger.debug(f"方法4失败: {e4}")

            # 方法5: 通过文本内容查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.XPATH, "//button[text()='换邮箱']"
                    )
                    logger.info("✅ 通过方法5找到换邮箱按钮")
                except Exception as e5:
                    logger.debug(f"方法5失败: {e5}")
            
            if new_address_button:
                # 在生成新邮箱前，清空验证码文件
                self.clear_verification_code()

                try:
                    new_address_button.click()
                    logger.info("✅ 成功点击换邮箱按钮")
                except Exception as click_error:
                    try:
                        self.driver.execute_script("arguments[0].click();", new_address_button)
                        logger.info("✅ 通过JavaScript成功点击换邮箱按钮")
                    except Exception as js_error:
                        logger.error(f"❌ 点击换邮箱按钮失败: {click_error}, JS也失败: {js_error}")
                        return False

                # 等待页面响应和新邮箱生成
                time.sleep(3)

                new_email = self.get_email_address()
                if new_email:
                    logger.info(f"✅ 新邮箱地址已生成: {new_email}")
                else:
                    logger.warning("⚠️ 无法获取新生成的邮箱地址")

                return True
            else:
                logger.error("❌ 所有方法都无法找到换邮箱按钮")
                logger.info("🔍 当前页面源码片段:")
                try:
                    page_source = self.driver.page_source
                    if "换邮箱" in page_source:
                        logger.info("页面中包含'换邮箱'文本")
                    else:
                        logger.warning("页面中不包含'换邮箱'文本")
                except:
                    logger.error("无法获取页面源码")
                return False

        except Exception as e:
            logger.error(f"生成新地址失败: {e}")
            return False
    
    def save_result(self, email, verification_code):
        """保存结果到文件"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'email': email,
            'verification_code': verification_code
        }
        self.results.append(result)
        
        # 保存到JSON文件
        with open('hitem3d_email_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 也保存到文本文件，方便查看
        with open('hitem3d_email_results.txt', 'a', encoding='utf-8') as f:
            f.write(f"{result['timestamp']} | {email} | {verification_code}\n")
        
        logger.info(f"结果已保存: {email} -> {verification_code}")
    
    def wait_for_email_continuously(self, email_address):
        """持续刷新直到获取到邮件"""
        logger.info(f"📧 开始持续刷新等待邮件: {email_address}")
        logger.info(f"🔄 当前刷新计数: {self.refresh_count}, 代理切换计数: {self.proxy_switch_count}")

        while True:
            try:
                # 点击刷新
                self.click_refresh_button()

                # 检查邮件
                emails = self.check_for_emails()

                if emails:
                    logger.info(f"收到邮件！当前总刷新次数: {self.refresh_count}")

                    # 点击第一封邮件并提取验证码
                    verification_code = self.click_email_and_extract_verification_code(emails[0])
                    if verification_code:
                        self.save_result(email_address, verification_code)
                        
                        # 收到邮件后重置所有计数器
                        logger.info("收到邮件，重置所有计数器")
                        self.refresh_count = 0
                        self.proxy_switch_count = 0
                        logger.info("所有计数器已重置为0")

                        return True
                    else:
                        logger.warning("未能提取到验证码，继续等待")

                if self.refresh_count % 10 == 0:
                    logger.info(f"已刷新 {self.refresh_count} 次，继续等待邮件...")

                time.sleep(3)

            except KeyboardInterrupt:
                logger.info("用户中断等待")
                return False
            except Exception as e:
                logger.error(f"等待邮件过程中出错: {e}")
                time.sleep(5)

    def run_automation(self, max_emails=10, headless=False):
        """运行自动化流程"""
        if not self.setup_driver(headless=headless):
            return False

        if not self.open_website():
            return False

        try:
            # 首次启动时点击创建临时邮箱按钮
            self.create_temp_email()
            
            # 启动新邮箱请求监控
            self.monitor_new_email_requests()
            
            # 启动注册状态监控
            self.monitor_registration_status()

            # 启动时换一次新邮箱
            logger.info("启动时换一次新邮箱...")
            if self.generate_new_address():
                email_address = self.get_email_address()
                if not email_address:
                    logger.error("无法获取初始邮箱地址")
                    return False
                logger.info(f"启动时已换新邮箱: {email_address}")
            else:
                # 如果换邮箱失败，使用当前邮箱
                email_address = self.get_email_address()
                if not email_address:
                    logger.error("无法获取邮箱地址")
                    return False

            generated_count = 0

            while generated_count < max_emails:
                logger.info(f"📍 主循环开始 - 第 {generated_count + 1}/{max_emails} 个邮箱处理，当前邮箱: {email_address}")

                # 持续刷新直到获取到邮件
                logger.info(f"🔄 开始等待邮件: {email_address}")
                if self.wait_for_email_continuously(email_address):
                    generated_count += 1
                    logger.info(f"第 {generated_count} 个邮箱完成，成功获取邮件和验证码 ({generated_count}/{max_emails})")

                    # 检查是否已达到目标数量
                    if generated_count >= max_emails:
                        logger.info(f"✅ 已完成目标数量 {max_emails} 个邮箱的处理，停止脚本")
                        break

                    # 获取到验证码后，等待注册完成再切换邮箱
                    logger.info("✅ 获取到验证码，等待注册完成后再切换邮箱...")

                    # 等待注册完成标记文件出现
                    wait_start_time = time.time()
                    max_wait_time = 300  # 最多等待5分钟

                    while time.time() - wait_start_time < max_wait_time:
                        if os.path.exists(self.registration_completed_file):
                            logger.info("✅ 检测到注册完成，现在可以切换邮箱")
                            try:
                                os.remove(self.registration_completed_file)
                                logger.info("✅ 成功删除注册完成标记文件")
                            except Exception as remove_error:
                                logger.warning(f"⚠️ 删除注册完成标记文件失败: {remove_error}")
                            break

                        elapsed_time = time.time() - wait_start_time
                        if int(elapsed_time) % 30 == 0:  # 每30秒输出一次进度
                            logger.info(f"⏳ 等待注册完成中... 已等待 {elapsed_time:.0f}s / {max_wait_time}s")

                        time.sleep(2)
                    else:
                        logger.warning("⚠️ 等待注册完成超时，继续切换邮箱")

                    logger.info("🚀 开始切换到下一个邮箱...")

                    # 生成新邮箱地址
                    if self.generate_new_address():
                        new_email_address = self.get_email_address()
                        if not new_email_address:
                            logger.error("❌ 生成新地址后无法获取邮箱地址，停止循环")
                            break
                        self.current_email = new_email_address
                        email_address = new_email_address  # 更新循环变量
                        logger.info(f"✅ 已切换到新邮箱: {email_address}")
                        logger.info(f"🔄 开始第 {generated_count + 1}/{max_emails} 个邮箱的邮件等待...")
                        # 继续循环，开始新邮箱的邮件等待
                        continue
                    else:
                        logger.error("❌ 切换邮箱失败，停止循环")
                        break
                else:
                    logger.warning(f"第 {generated_count + 1} 个邮箱处理失败或被中断")
                    break

            logger.info(f"🎉 自动化完成！共成功处理 {len(self.results)} 个邮箱，目标: {max_emails} 个")

        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")

def main():
    """主函数"""
    automation = Hitem3DEmailAutomation()

    print("Hitem3D临时邮箱自动化脚本")
    print("=" * 50)
    print("功能说明：")
    print("- 自动获取临时邮箱地址并写入共享文件")
    print("- 持续刷新直到收到邮件")
    print("- 提取邮件中的6位验证码并保存")
    print("- 自动生成新邮箱地址继续循环")
    print("=" * 50)

    # 获取用户输入的邮箱数量
    try:
        max_emails = int(input("请输入要处理的邮箱数量 (默认10个): ") or "10")
        if max_emails <= 0:
            max_emails = 10
            print("输入无效，使用默认值10个邮箱")
    except ValueError:
        max_emails = 10
        print("输入无效，使用默认值10个邮箱")

    # 获取运行模式选择
    print("\n运行模式选择:")
    print("1. 普通模式 (有界面，可以看到浏览器)")
    print("2. 无头模式 (无界面，完全后台运行，推荐)")

    try:
        mode_choice = input("请选择运行模式 (1或2，默认2): ").strip() or "2"
        headless = mode_choice == "2"
        mode_name = "无头模式" if headless else "普通模式"
        print(f"已选择: {mode_name}")
    except:
        headless = True
        print("输入无效，使用默认无头模式")

    print(f"\n将生成 {max_emails} 个邮箱，运行模式: {'无头模式' if headless else '普通模式'}")
    print("每次获取邮箱地址都会自动写入共享文件传递给注册脚本")
    print("获取到验证码后会保存到verification_code.txt文件")
    print("按 Ctrl+C 可随时停止")
    print("=" * 50)

    automation.run_automation(max_emails=max_emails, headless=headless)

if __name__ == "__main__":
    main() 