#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BFL.AI 自动登录脚本
读取 email_results 文件中的链接地址，打开对应页面并自动登录
"""

import time
import json
import os
import sys
import argparse
import threading
import re
import subprocess
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bfl_login.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BFLLoginAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.password = "MySecurePassword123!"
        self.is_monitoring = False
        self.monitor_thread = None
        # 添加email_automation进程监控相关属性
        self.monitoring_email_automation = False
        self.email_automation_monitor_thread = None
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器（无痕模式）"""
        chrome_options = Options()
        # 启用无痕模式
        chrome_options.add_argument('--incognito')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 如果是后台模式，添加相关参数
        if headless:
            chrome_options.add_argument('--headless')  # 无头模式
        else:
            # 最小化窗口，不抢夺焦点
            chrome_options.add_argument('--window-position=-2000,-2000')  # 窗口位置移到屏幕外
            chrome_options.add_argument('--window-size=800,600')  # 设置较小窗口
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--disable-features=VizDisplayCompositor')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)

            # 如果不是无头模式，最小化窗口
            if not headless:
                try:
                    self.driver.minimize_window()
                    logger.info("浏览器已最小化")
                except:
                    logger.warning("无法最小化浏览器窗口")

            logger.info("浏览器启动成功（无痕模式）")
            return True
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            return False
    
    def open_link(self, link):
        """打开指定的链接"""
        try:
            self.driver.get(link)
            logger.info(f"已打开链接: {link}")
            time.sleep(3)  # 等待页面加载
            return True
        except Exception as e:
            logger.error(f"打开链接失败: {e}")
            return False
    
    def read_email_results(self):
        """读取邮箱结果文件，返回邮箱和链接的对应关系"""
        email_link_pairs = []

        # 优先尝试读取文本文件（因为它有最新最全的数据）
        if os.path.exists('email_results.txt'):
            try:
                with open('email_results.txt', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    logger.info(f"文本文件共有 {len(lines)} 行")

                    for line_num, line in enumerate(lines, 1):
                        line = line.strip()
                        if not line:  # 跳过空行
                            continue

                        parts = line.split(' | ')
                        logger.debug(f"第{line_num}行解析: {len(parts)} 个部分")

                        if len(parts) >= 3:  # 时间戳 | 邮箱 | 链接
                            email = parts[1].strip()
                            link = parts[2].strip()
                            email_link_pairs.append({
                                'email': email,
                                'link': link
                            })
                            logger.debug(f"添加邮箱: {email}")
                        else:
                            logger.warning(f"第{line_num}行格式不正确: {line}")

                logger.info(f"从 email_results.txt 读取到 {len(email_link_pairs)} 个邮箱-链接对")
            except Exception as e:
                logger.error(f"读取文本文件失败: {e}")

        # 如果文本文件不存在或读取失败，尝试读取 JSON 文件
        if not email_link_pairs and os.path.exists('email_results.json'):
            try:
                with open('email_results.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        if 'email' in item and 'link' in item:
                            email_link_pairs.append({
                                'email': item['email'],
                                'link': item['link']
                            })
                logger.info(f"从 email_results.json 读取到 {len(email_link_pairs)} 个邮箱-链接对")
            except Exception as e:
                logger.error(f"读取 JSON 文件失败: {e}")

        if not email_link_pairs:
            logger.error("未找到邮箱地址文件或文件为空")
            return []

        logger.info(f"共读取到 {len(email_link_pairs)} 个邮箱-链接对")
        return email_link_pairs

    def get_processed_emails(self):
        """获取已经处理过的邮箱列表"""
        processed_emails = set()

        if os.path.exists('api_keys.txt'):
            try:
                with open('api_keys.txt', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines:
                        line = line.strip()
                        if line and ' | ' in line:
                            parts = line.split(' | ')
                            if len(parts) >= 2:
                                email = parts[1].strip()
                                processed_emails.add(email)

                logger.info(f"发现 {len(processed_emails)} 个已处理的邮箱")
                for email in processed_emails:
                    logger.debug(f"已处理: {email}")

            except Exception as e:
                logger.error(f"读取已处理邮箱列表失败: {e}")
        else:
            logger.info("未找到 api_keys.txt 文件，将处理所有邮箱")

        return processed_emails

    def get_error_emails(self):
        """获取已经标记为错误的邮箱列表"""
        error_emails = set()

        if os.path.exists('error_emails.txt'):
            try:
                with open('error_emails.txt', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    for line in lines:
                        line = line.strip()
                        if line and ' | ' in line:
                            parts = line.split(' | ')
                            if len(parts) >= 2:
                                email = parts[1].strip()
                                error_emails.add(email)

                logger.info(f"发现 {len(error_emails)} 个错误邮箱")
                for email in error_emails:
                    logger.debug(f"错误邮箱: {email}")

            except Exception as e:
                logger.error(f"读取错误邮箱列表失败: {e}")
        else:
            logger.info("未找到 error_emails.txt 文件")

        return error_emails

    def validate_and_clean_api_keys(self):
        """验证并清理 api_keys.txt 文件中的格式不正确的行"""
        if not os.path.exists('api_keys.txt'):
            logger.info("api_keys.txt 文件不存在，跳过格式检测")
            return

        try:
            # 定义正确的格式：时间戳 | 邮箱 | UUID格式的API key
            # 例如：2025-06-05 16:34:44 | <EMAIL> | d3461efa-6a58-4fac-8cf1-5c3d21c9b29b
            correct_pattern = re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2} \| [^\s@]+@[^\s@]+\.[^\s@]+ \| [a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$')

            valid_lines = []
            invalid_lines = []

            # 读取所有行
            with open('api_keys.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()

            logger.info(f"开始检测 api_keys.txt 文件格式，共 {len(lines)} 行")

            # 检查每一行的格式
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:  # 跳过空行
                    continue

                if correct_pattern.match(line):
                    valid_lines.append(line)
                    logger.debug(f"第{line_num}行格式正确: {line}")
                else:
                    invalid_lines.append(line)
                    logger.warning(f"第{line_num}行格式不正确: {line}")

            # 如果有无效行，重写文件
            if invalid_lines:
                logger.info(f"发现 {len(invalid_lines)} 行格式不正确的记录，将被删除")
                logger.info(f"保留 {len(valid_lines)} 行格式正确的记录")

                # 备份原文件
                backup_filename = f'api_keys_backup_{int(time.time())}.txt'
                with open(backup_filename, 'w', encoding='utf-8') as f:
                    f.writelines(lines)
                logger.info(f"原文件已备份为: {backup_filename}")

                # 重写文件，只保留格式正确的行
                with open('api_keys.txt', 'w', encoding='utf-8') as f:
                    for line in valid_lines:
                        f.write(line + '\n')

                logger.info("api_keys.txt 文件已清理完成")

                # 显示被删除的无效行
                if invalid_lines:
                    logger.info("被删除的无效行:")
                    for invalid_line in invalid_lines:
                        logger.info(f"  - {invalid_line}")
            else:
                logger.info("所有行格式都正确，无需清理")

        except Exception as e:
            logger.error(f"验证和清理 api_keys.txt 文件时出错: {e}")

    def record_error_email(self, email, error_reason):
        """记录错误邮箱"""
        try:
            with open('error_emails.txt', 'a', encoding='utf-8') as f:
                timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{timestamp} | {email} | {error_reason}\n")

            logger.info(f"已记录错误邮箱: {email} - {error_reason}")
        except Exception as e:
            logger.error(f"记录错误邮箱失败: {e}")

    def filter_unprocessed_emails(self, email_link_pairs):
        """过滤出未处理的邮箱"""
        processed_emails = self.get_processed_emails()
        error_emails = self.get_error_emails()
        unprocessed_pairs = []

        for pair in email_link_pairs:
            email = pair['email']
            if email in processed_emails:
                logger.info(f"跳过已处理的邮箱: {email}")
            elif email in error_emails:
                logger.info(f"跳过错误邮箱: {email}")
            else:
                unprocessed_pairs.append(pair)

        logger.info(f"需要处理的邮箱数量: {len(unprocessed_pairs)}")
        return unprocessed_pairs
    
    def fill_login_form(self, email):
        """填写登录表单"""
        try:
            # 查找邮箱输入框
            email_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="email"][name="email"]'))
            )
            email_input.clear()
            email_input.send_keys(email)
            logger.info(f"已输入邮箱: {email}")
            
            # 查找密码输入框
            password_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[type="password"][name="password"]'))
            )
            password_input.clear()
            password_input.send_keys(self.password)
            logger.info("已输入密码")
            
            return True
            
        except Exception as e:
            logger.error(f"填写表单失败: {e}")
            return False
    
    def click_login_button(self):
        """点击登录按钮"""
        try:
            login_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]'))
            )
            login_button.click()
            logger.info("已点击登录按钮")
            time.sleep(3)  # 等待登录处理
            return True
        except Exception as e:
            logger.error(f"点击登录按钮失败: {e}")
            return False
    
    def check_login_result(self):
        """检查登录结果"""
        try:
            # 等待页面变化，检查是否登录成功
            time.sleep(3)
            current_url = self.driver.current_url

            # 检查是否有特定的登录失败错误弹窗
            try:
                # 查找"Login failed"错误弹窗
                login_failed_elements = self.driver.find_elements(
                    By.XPATH, "//div[contains(text(), 'Login failed')]"
                )
                if login_failed_elements:
                    # 查找详细错误信息
                    error_detail_elements = self.driver.find_elements(
                        By.XPATH, "//div[contains(text(), 'Please verify your email address')]"
                    )
                    if error_detail_elements:
                        logger.warning("检测到邮箱验证错误：Please verify your email address before signing in")
                        return "email_verification_error"
                    else:
                        logger.warning("检测到登录失败错误")
                        return "login_failed_error"
            except:
                pass

            # 检查是否跳转到其他页面（登录成功）
            if current_url != "https://auth.bfl.ai/":
                logger.info(f"登录成功，跳转到: {current_url}")
                return "success"

            # 检查是否有其他错误信息
            try:
                error_elements = self.driver.find_elements(By.CSS_SELECTOR, '[role="alert"], .error, .text-red-500, .text-destructive')
                if error_elements:
                    error_text = error_elements[0].text
                    logger.warning(f"登录失败，错误信息: {error_text}")
                    return "error"
            except:
                pass

            # 如果没有明显的错误信息，可能是其他问题
            logger.warning("登录状态不明确")
            return "unknown"

        except Exception as e:
            logger.error(f"检查登录结果失败: {e}")
            return "error"

    def click_try_demo_button(self):
        """点击Try Demo按钮"""
        try:
            # 方法1: 通过href属性查找
            try:
                demo_button = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[href="https://playground.bfl.ai/image/edit"]'))
                )
                demo_button.click()
                logger.info("已点击Try Demo按钮")
                time.sleep(3)  # 等待新页面加载
                return True
            except Exception as e:
                logger.warning(f"通过href未找到Try Demo按钮: {e}")

            # 方法2: 通过文本内容查找
            try:
                demo_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Try Demo')]"))
                )
                demo_button.click()
                logger.info("通过文本找到Try Demo按钮并点击")
                time.sleep(3)
                return True
            except Exception as e:
                logger.warning(f"通过文本未找到Try Demo按钮: {e}")

            logger.error("未找到Try Demo按钮")
            return False

        except Exception as e:
            logger.error(f"点击Try Demo按钮失败: {e}")
            return False

    def handle_welcome_popup(self):
        """处理欢迎弹窗"""
        try:
            # 等待弹窗出现
            time.sleep(3)

            # 查找并点击复选框
            try:
                checkbox = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[role="checkbox"][id="terms"]'))
                )
                checkbox.click()
                logger.info("已勾选年龄确认复选框")
                time.sleep(1)
            except Exception as e:
                logger.error(f"勾选复选框失败: {e}")
                return False

            # 查找并点击Get Started按钮
            try:
                get_started_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Get Started')]"))
                )
                get_started_button.click()
                logger.info("已点击Get Started按钮")
                time.sleep(3)
                return True
            except Exception as e:
                logger.error(f"点击Get Started按钮失败: {e}")
                return False

        except Exception as e:
            logger.error(f"处理欢迎弹窗失败: {e}")
            return False

    def handle_terms_page(self):
        """处理条款页面（Developer Terms 或 API Terms）"""
        try:
            # 获取页面标题
            try:
                page_title = self.driver.find_element(By.TAG_NAME, 'h1').text
                logger.info(f"开始处理条款页面: {page_title}")
            except:
                logger.info("开始处理条款页面")
            
            # 等待页面完全加载
            time.sleep(3)

            # 查找并点击条款复选框
            try:
                # 方法1：通过ID查找
                terms_checkbox = self.wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[role="checkbox"][id="terms"]'))
                )
                
                # 检查复选框是否已经勾选
                aria_checked = terms_checkbox.get_attribute('aria-checked')
                data_state = terms_checkbox.get_attribute('data-state')
                
                if aria_checked == 'false' or data_state == 'unchecked':
                    terms_checkbox.click()
                    logger.info("已勾选'I agree to the terms and conditions'复选框")
                    time.sleep(1)
                else:
                    logger.info("条款复选框已经勾选")
                    
            except Exception as e:
                logger.error(f"勾选条款复选框失败: {e}")
                
                # 方法2：尝试通过label查找
                try:
                    label = self.driver.find_element(By.XPATH, "//label[contains(text(), 'I agree to the terms and conditions')]")
                    label.click()
                    logger.info("通过label勾选条款复选框")
                    time.sleep(1)
                except Exception as e2:
                    logger.error(f"通过label勾选复选框也失败: {e2}")
                    return False

            # 查找并点击Accept Terms按钮
            try:
                # 等待按钮变为可点击状态（复选框勾选后按钮应该会启用）
                time.sleep(2)
                
                # 方法1：通过文本内容查找
                accept_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'Accept Terms')]"))
                )
                accept_button.click()
                logger.info("已点击Accept Terms按钮")
                time.sleep(3)  # 等待页面跳转
                return True
                
            except Exception as e:
                logger.error(f"点击Accept Terms按钮失败: {e}")
                
                # 方法2：尝试通过更通用的选择器
                try:
                    accept_buttons = self.driver.find_elements(
                        By.XPATH, "//button[contains(text(), 'Accept') or contains(., 'Accept')]"
                    )
                    for button in accept_buttons:
                        if 'terms' in button.text.lower() or 'accept' in button.text.lower():
                            # 检查按钮是否可点击
                            if not button.get_attribute('disabled'):
                                button.click()
                                logger.info(f"通过备用方法点击按钮: {button.text}")
                                time.sleep(3)
                                return True
                    
                    logger.error("未找到可点击的Accept Terms按钮")
                    return False
                    
                except Exception as e2:
                    logger.error(f"备用方法也失败: {e2}")
                    return False

        except Exception as e:
            logger.error(f"处理条款页面失败: {e}")
            return False

    def navigate_to_api_keys(self):
        """跳转到API keys页面"""
        try:
            # 等待弹窗完全消失
            time.sleep(3)

            # 直接跳转到API keys页面
            self.driver.get("https://dashboard.bfl.ai/api/keys")
            logger.info("已跳转到API keys页面")
            time.sleep(3)  # 等待页面加载

            # 处理可能出现的多个条款页面
            max_attempts = 3  # 最多处理3个条款页面
            for attempt in range(max_attempts):
                current_url = self.driver.current_url
                logger.debug(f"第{attempt + 1}次检查，当前页面URL: {current_url}")

                # 查找是否有terms复选框，如果有说明需要接受条款
                try:
                    terms_checkbox = self.driver.find_element(By.CSS_SELECTOR, 'button[role="checkbox"][id="terms"]')
                    
                    # 检查页面标题来判断是哪种条款页面
                    try:
                        page_title = self.driver.find_element(By.TAG_NAME, 'h1').text
                        logger.info(f"检测到条款页面: {page_title}")
                    except:
                        logger.info("检测到条款页面，需要接受条款")
                    
                    # 处理条款接受流程
                    if self.handle_terms_page():
                        logger.info("已成功接受条款")
                        time.sleep(3)  # 等待页面跳转
                        
                        # 检查是否已经到达API keys页面
                        current_url_after = self.driver.current_url
                        if 'api/keys' in current_url_after:
                            logger.info("已成功到达API keys页面")
                            return True
                        else:
                            logger.info("条款接受后继续检查是否有更多条款页面...")
                            continue
                    else:
                        logger.error("接受条款失败")
                        return False
                        
                except Exception as e:
                    # 没有找到terms复选框，说明已经在API keys页面或其他页面
                    logger.debug(f"未检测到条款页面: {e}")
                    
                    # 检查是否在正确的API keys页面
                    current_url = self.driver.current_url
                    if 'api/keys' in current_url:
                        logger.info("已在API keys页面")
                        return True
                    else:
                        logger.warning(f"不在API keys页面，当前URL: {current_url}")
                        return False

            logger.warning(f"处理了{max_attempts}次条款页面仍未到达API keys页面")
            return False

        except Exception as e:
            logger.error(f"跳转到API keys页面失败: {e}")
            return False

    def get_api_key(self, email):
        """点击查看按钮获取API key并保存到文件"""
        try:
            # 查找查看按钮（eye图标）
            view_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[class*="inline-flex"] svg.lucide-eye'))
            )

            # 点击查看按钮
            view_button.click()
            logger.info("已点击查看API key按钮")
            time.sleep(2)  # 等待API key显示

            # 查找API key文本元素
            # 根据HTML结构，API key应该在表格的第二列（Key列）中
            try:
                # 方法1：通过表格结构查找API key
                api_key_element = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'td.font-mono'))
                )
                api_key = api_key_element.text.strip()
                logger.info("通过表格结构找到API key")
            except Exception as e:
                logger.warning(f"通过表格结构查找API key失败: {e}")

                # 方法2：通过更通用的选择器查找
                try:
                    api_key_elements = self.driver.find_elements(By.CSS_SELECTOR, 'td')
                    api_key = None
                    for element in api_key_elements:
                        text = element.text.strip()
                        # 检查是否是UUID格式的API key
                        if re.match(r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', text):
                            api_key = text
                            break

                    if api_key:
                        logger.info("通过UUID格式匹配找到API key")
                    else:
                        raise Exception("未找到符合UUID格式的API key")

                except Exception as e2:
                    logger.warning(f"通过UUID格式匹配查找API key失败: {e2}")

                    # 方法3：查找包含连字符的长字符串
                    try:
                        all_elements = self.driver.find_elements(By.CSS_SELECTOR, '*')
                        api_key = None
                        for element in all_elements:
                            text = element.text.strip()
                            # 查找包含多个连字符且长度合适的字符串
                            if len(text) > 30 and text.count('-') >= 4 and not ' ' in text:
                                api_key = text
                                break

                        if api_key:
                            logger.info("通过连字符模式匹配找到API key")
                        else:
                            raise Exception("未找到符合连字符模式的API key")

                    except Exception as e3:
                        logger.error(f"所有方法都失败: {e3}")
                        return False

            if api_key and len(api_key) > 10:  # 简单验证API key长度
                # 保存到文件
                with open('api_keys.txt', 'a', encoding='utf-8') as f:
                    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
                    f.write(f"{timestamp} | {email} | {api_key}\n")

                logger.info(f"API key已保存: {email} -> {api_key[:20]}...")
                return True
            else:
                logger.error("未能获取有效的API key")
                return False

        except Exception as e:
            logger.error(f"获取API key失败: {e}")

            # 备用方法：尝试通过父元素查找查看按钮
            try:
                view_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'inline-flex')]//svg[contains(@class, 'lucide-eye')]/.."))
                )
                view_button.click()
                logger.info("通过备用方法点击查看按钮")
                time.sleep(2)

                # 重复API key查找逻辑
                try:
                    api_key_element = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'td.font-mono'))
                    )
                    api_key = api_key_element.text.strip()
                except:
                    # 使用UUID格式匹配
                    api_key_elements = self.driver.find_elements(By.CSS_SELECTOR, 'td')
                    api_key = None
                    for element in api_key_elements:
                        text = element.text.strip()
                        if re.match(r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$', text):
                            api_key = text
                            break

                if api_key and len(api_key) > 10:
                    with open('api_keys.txt', 'a', encoding='utf-8') as f:
                        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
                        f.write(f"{timestamp} | {email} | {api_key}\n")

                    logger.info(f"API key已保存: {email} -> {api_key[:20]}...")
                    return True
                else:
                    logger.error("备用方法也未能获取有效的API key")
                    return False

            except Exception as e2:
                logger.error(f"备用方法也失败: {e2}")
                return False

    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.wait = None
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
    
    def process_single_email(self, email, link, background_mode=False):
        """处理单个邮箱的完整流程"""
        logger.info(f"开始处理邮箱: {email}")
        logger.info(f"使用链接: {link}")

        # 设置浏览器（后台模式）
        if not self.setup_driver(headless=background_mode):
            return False

        try:
            # 打开对应的链接
            if not self.open_link(link):
                return False

            # 填写表单
            if not self.fill_login_form(email):
                return False

            # 点击登录按钮
            if not self.click_login_button():
                return False

            # 检查登录结果
            result = self.check_login_result()

            if result == "success":
                logger.info(f"邮箱 {email} 登录成功！")

                # 登录成功后点击Try Demo按钮
                if self.click_try_demo_button():
                    # 处理欢迎弹窗
                    if self.handle_welcome_popup():
                        logger.info("已完成Try Demo流程")

                        # 跳转到API keys页面
                        if self.navigate_to_api_keys():
                            # 获取API key
                            if self.get_api_key(email):
                                logger.info("已完成API key获取流程")
                                return True
                            else:
                                logger.warning("获取API key失败")
                        else:
                            logger.warning("跳转到API keys页面失败")
                    else:
                        logger.warning("处理欢迎弹窗失败")
                else:
                    logger.warning("点击Try Demo按钮失败")

                return False
            elif result == "email_verification_error":
                logger.warning(f"邮箱 {email} 需要验证，记录为错误邮箱")
                self.record_error_email(email, "Email verification required")
                return False
            elif result == "login_failed_error":
                logger.warning(f"邮箱 {email} 登录失败，记录为错误邮箱")
                self.record_error_email(email, "Login failed")
                return False
            elif result == "error":
                logger.warning(f"邮箱 {email} 登录失败")
                return False
            else:
                logger.warning(f"邮箱 {email} 登录状态不明确")
                return False

        finally:
            # 无论成功失败都关闭浏览器
            self.close_browser()
    
    def run_automation(self):
        """运行自动化登录流程"""
        # 首先验证和清理 api_keys.txt 文件格式
        logger.info("运行前检测 api_keys.txt 文件格式...")
        self.validate_and_clean_api_keys()

        # 读取邮箱-链接对
        email_link_pairs = self.read_email_results()
        if not email_link_pairs:
            return False

        # 过滤出未处理的邮箱
        unprocessed_pairs = self.filter_unprocessed_emails(email_link_pairs)
        if not unprocessed_pairs:
            logger.info("所有邮箱都已处理完成！")
            return True

        successful_logins = []
        failed_logins = []

        try:
            for i, pair in enumerate(unprocessed_pairs, 1):
                email = pair['email']
                link = pair['link']
                logger.info(f"处理第 {i}/{len(unprocessed_pairs)} 个未处理的邮箱-链接对")

                # 处理单个邮箱（包括打开和关闭浏览器）
                if self.process_single_email(email, link):
                    successful_logins.append(f"{email} -> API key已获取")
                    logger.info(f"成功获取API key的邮箱: {email}")
                else:
                    failed_logins.append(f"{email} -> 处理失败")
                    logger.warning(f"处理失败的邮箱: {email}")

                # 如果不是最后一个邮箱，等待一下再继续
                if i < len(unprocessed_pairs):
                    logger.info(f"等待3秒后处理下一个邮箱...")
                    time.sleep(3)
            
            # 输出结果统计
            logger.info("=" * 50)
            logger.info("登录结果统计:")
            logger.info(f"成功登录: {len(successful_logins)} 个")
            logger.info(f"登录失败: {len(failed_logins)} 个")
            
            if successful_logins:
                logger.info("成功获取API key的邮箱:")
                for item in successful_logins:
                    logger.info(f"  - {item}")

            if failed_logins:
                logger.info("处理失败的邮箱:")
                for item in failed_logins:
                    logger.info(f"  - {item}")

            logger.info("=" * 50)
            logger.info("所有邮箱处理完成！")

        except KeyboardInterrupt:
            logger.info("用户中断程序")
            # 确保浏览器被关闭
            self.close_browser()
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
            # 确保浏览器被关闭
            self.close_browser()

    def run_automation_background(self, headless=False):
        """后台模式运行自动化登录流程"""
        # 首先验证和清理 api_keys.txt 文件格式
        logger.info("后台运行前检测 api_keys.txt 文件格式...")
        self.validate_and_clean_api_keys()

        # 读取邮箱-链接对
        email_link_pairs = self.read_email_results()
        if not email_link_pairs:
            return False

        # 过滤出未处理的邮箱
        unprocessed_pairs = self.filter_unprocessed_emails(email_link_pairs)
        if not unprocessed_pairs:
            logger.info("所有邮箱都已处理完成！")
            return True

        successful_logins = []
        failed_logins = []

        try:
            for i, pair in enumerate(unprocessed_pairs, 1):
                email = pair['email']
                link = pair['link']
                logger.info(f"处理第 {i}/{len(unprocessed_pairs)} 个未处理的邮箱-链接对")

                # 处理单个邮箱（使用后台模式）
                if self.process_single_email(email, link, background_mode=headless):
                    successful_logins.append(f"{email} -> API key已获取")
                    logger.info(f"成功获取API key的邮箱: {email}")
                else:
                    failed_logins.append(f"{email} -> 处理失败")
                    logger.warning(f"处理失败的邮箱: {email}")

                # 如果不是最后一个邮箱，等待一下再继续
                if i < len(unprocessed_pairs):
                    logger.info(f"等待3秒后处理下一个邮箱...")
                    time.sleep(3)

            # 输出结果统计
            logger.info("=" * 50)
            logger.info("登录结果统计:")
            logger.info(f"成功登录: {len(successful_logins)} 个")
            logger.info(f"登录失败: {len(failed_logins)} 个")

            if successful_logins:
                logger.info("成功获取API key的邮箱:")
                for item in successful_logins:
                    logger.info(f"  - {item}")

            if failed_logins:
                logger.info("处理失败的邮箱:")
                for item in failed_logins:
                    logger.info(f"  - {item}")

            logger.info("=" * 50)
            logger.info("所有邮箱处理完成！")

        except KeyboardInterrupt:
            logger.info("用户中断程序")
            # 确保浏览器被关闭
            self.close_browser()
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
            # 确保浏览器被关闭
            self.close_browser()

    def check_for_new_emails(self):
        """检查是否有新的邮箱需要处理"""
        try:
            # 读取邮箱-链接对
            email_link_pairs = self.read_email_results()
            if not email_link_pairs:
                logger.info("未找到邮箱文件或文件为空")
                return False

            # 过滤出未处理的邮箱
            unprocessed_pairs = self.filter_unprocessed_emails(email_link_pairs)

            if unprocessed_pairs:
                logger.info(f"发现 {len(unprocessed_pairs)} 个新的未处理邮箱")
                return True
            else:
                logger.info("没有发现新的未处理邮箱")
                return False

        except Exception as e:
            logger.error(f"检查新邮箱时出错: {e}")
            return False

    def process_new_emails(self, headless=False):
        """处理新发现的邮箱"""
        try:
            logger.info("开始处理新发现的邮箱...")

            # 读取邮箱-链接对
            email_link_pairs = self.read_email_results()
            if not email_link_pairs:
                return False

            # 过滤出未处理的邮箱
            unprocessed_pairs = self.filter_unprocessed_emails(email_link_pairs)
            if not unprocessed_pairs:
                logger.info("没有需要处理的新邮箱")
                return True

            successful_logins = []
            failed_logins = []

            for i, pair in enumerate(unprocessed_pairs, 1):
                email = pair['email']
                link = pair['link']
                logger.info(f"处理第 {i}/{len(unprocessed_pairs)} 个新邮箱")

                # 处理单个邮箱
                if self.process_single_email(email, link, background_mode=headless):
                    successful_logins.append(f"{email} -> API key已获取")
                    logger.info(f"成功获取API key的邮箱: {email}")
                else:
                    failed_logins.append(f"{email} -> 处理失败")
                    logger.warning(f"处理失败的邮箱: {email}")

                # 如果不是最后一个邮箱，等待一下再继续
                if i < len(unprocessed_pairs):
                    logger.info(f"等待3秒后处理下一个邮箱...")
                    time.sleep(3)

            # 输出结果统计
            logger.info("=" * 50)
            logger.info("本次处理结果统计:")
            logger.info(f"成功处理: {len(successful_logins)} 个")
            logger.info(f"处理失败: {len(failed_logins)} 个")

            if successful_logins:
                logger.info("成功获取API key的邮箱:")
                for item in successful_logins:
                    logger.info(f"  - {item}")

            if failed_logins:
                logger.info("处理失败的邮箱:")
                for item in failed_logins:
                    logger.info(f"  - {item}")

            logger.info("=" * 50)
            return True

        except Exception as e:
            logger.error(f"处理新邮箱时出错: {e}")
            return False

    def monitor_loop(self, headless=False):
        """监控循环，每1分30秒检查一次"""
        logger.info("开始监控模式，每1分30秒检查一次新邮箱...")

        while self.is_monitoring:
            try:
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"[{current_time}] 开始检查新邮箱...")

                # 首先验证和清理 api_keys.txt 文件格式
                logger.info("检测 api_keys.txt 文件格式...")
                self.validate_and_clean_api_keys()

                # 检查是否有新邮箱
                if self.check_for_new_emails():
                    logger.info("发现新邮箱，开始处理...")
                    self.process_new_emails(headless=headless)
                else:
                    logger.info("没有新邮箱，继续等待...")

                # 等待1分钟（60秒）
                logger.info("等待60秒后进行下一次检查...")
                for i in range(60):  # 1分钟 = 60秒
                    if not self.is_monitoring:  # 检查是否需要停止监控
                        break
                    time.sleep(1)

            except KeyboardInterrupt:
                logger.info("用户中断监控")
                break
            except Exception as e:
                logger.error(f"监控循环中出错: {e}")
                logger.info("等待5分钟后重试...")
                for i in range(300):  # 5分钟 = 300秒
                    if not self.is_monitoring:
                        break
                    time.sleep(1)

        logger.info("监控已停止")

    def start_monitoring(self, headless=False):
        """启动监控模式"""
        if self.is_monitoring:
            logger.warning("监控已经在运行中")
            return False

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, args=(headless,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()

        logger.info("监控模式已启动")
        return True

    def stop_monitoring(self):
        """停止监控模式"""
        if not self.is_monitoring:
            logger.warning("监控未在运行")
            return False

        self.is_monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)

        logger.info("监控模式已停止")
        return True

    def check_email_automation_running(self):
        """检测email_automation.py进程是否正在运行"""
        try:
            import psutil
            logger.debug("检查email_automation.py进程是否正在运行...")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查进程命令行是否包含email_automation.py
                    if proc.info['cmdline'] and any('email_automation.py' in arg for arg in proc.info['cmdline']):
                        logger.debug(f"发现运行中的email_automation.py进程 PID: {proc.info['pid']}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            logger.debug("未发现运行中的email_automation.py进程")
            return False

        except ImportError:
            # 如果没有psutil库，使用系统命令
            logger.warning("未安装psutil库，使用系统命令检查email_automation.py进程...")
            return self.check_email_automation_with_system_command()
        except Exception as e:
            logger.error(f"检查email_automation.py进程时出错: {e}")
            return False

    def check_email_automation_with_system_command(self):
        """使用系统命令检查email_automation.py进程是否运行"""
        try:
            import subprocess
            if os.name == 'nt':
                # Windows系统使用wmic命令
                result = subprocess.run([
                    'wmic', 'process', 'where',
                    'CommandLine like "%email_automation.py%"',
                    'get', 'ProcessId,CommandLine', '/format:csv'
                ], capture_output=True, text=True, encoding='gbk')

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    found_processes = []

                    for line in lines:
                        if line.strip() and 'email_automation.py' in line:
                            # 解析CSV格式的输出
                            parts = line.split(',')
                            if len(parts) >= 3:
                                try:
                                    pid = parts[2].strip()  # ProcessId通常在第3列
                                    if pid and pid.isdigit():
                                        found_processes.append(pid)
                                except (ValueError, IndexError):
                                    continue

                    if found_processes:
                        logger.debug(f"通过系统命令发现运行中的email_automation.py进程: {found_processes}")
                        return True

                logger.debug("通过系统命令未发现运行中的email_automation.py进程")
                return False
            else:
                # Linux/Mac系统使用pgrep命令
                result = subprocess.run(
                    ['pgrep', '-f', 'email_automation.py'],
                    capture_output=True, text=True
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.debug(f"通过系统命令发现运行中的email_automation.py进程: {pids}")
                    return True
                else:
                    logger.debug("通过系统命令未发现运行中的email_automation.py进程")
                    return False

        except Exception as e:
            logger.error(f"使用系统命令检查email_automation.py进程失败: {e}")
            return False

    def start_email_automation(self):
        """启动email_automation.py进程"""
        try:
            # 检查email_automation.py文件是否存在
            if not os.path.exists('email_automation.py'):
                logger.error("未找到 email_automation.py 文件")
                return False

            logger.info("正在启动email_automation.py进程...")

            # 在Windows系统上使用CREATE_NEW_CONSOLE标志在新窗口中启动进程
            if os.name == 'nt':
                # Windows系统
                subprocess.Popen(
                    [sys.executable, 'email_automation.py'],
                    creationflags=subprocess.CREATE_NEW_CONSOLE,
                    cwd=os.getcwd()
                )
            else:
                # Linux/Mac系统
                subprocess.Popen(
                    [sys.executable, 'email_automation.py'],
                    cwd=os.getcwd()
                )

            # 等待一下让进程启动
            time.sleep(3)

            # 检查进程是否成功启动
            if self.check_email_automation_running():
                logger.info("✅ email_automation.py进程启动成功")
                return True
            else:
                logger.error("❌ email_automation.py进程启动失败")
                return False

        except Exception as e:
            logger.error(f"启动email_automation.py进程失败: {e}")
            return False

    def start_email_automation_monitoring(self):
        """启动email_automation.py进程监控线程"""
        import threading

        def monitor_email_automation():
            logger.info("开始监控email_automation.py进程状态...")
            self.monitoring_email_automation = True

            while self.monitoring_email_automation:
                try:
                    # 每60秒检查一次进程状态
                    time.sleep(60)

                    if not self.monitoring_email_automation:
                        break

                    # 检查进程是否在运行
                    logger.debug("开始检查email_automation.py进程状态...")
                    is_running = self.check_email_automation_running()

                    if not is_running:
                        logger.warning("⚠️ 检测到email_automation.py进程已停止，准备自动重启...")

                        # 再次确认进程确实不存在（双重检查）
                        time.sleep(5)
                        is_running_double_check = self.check_email_automation_running()

                        if not is_running_double_check:
                            logger.warning("双重检查确认：email_automation.py进程确实已停止")

                            # 尝试重启进程
                            restart_success = False
                            for attempt in range(2):  # 最多尝试2次
                                logger.info(f"第{attempt + 1}次尝试自动重启email_automation.py")
                                if self.start_email_automation():
                                    restart_success = True
                                    logger.info("✅ email_automation.py自动重启成功")
                                    break
                                else:
                                    if attempt == 0:
                                        logger.warning("第1次自动重启失败，等待5秒后再次尝试...")
                                        time.sleep(5)
                                    else:
                                        logger.error("第2次自动重启也失败")

                            if not restart_success:
                                logger.error("❌ email_automation.py自动重启失败，已尝试2次")
                        else:
                            logger.info("双重检查发现email_automation.py进程实际在运行，可能是临时检测错误")
                    else:
                        logger.debug("email_automation.py进程运行正常")

                except Exception as e:
                    logger.error(f"email_automation.py进程监控过程中出错: {e}")
                    time.sleep(10)  # 出错后等待10秒再继续

        # 在后台线程中启动监控
        self.email_automation_monitor_thread = threading.Thread(target=monitor_email_automation, daemon=True)
        self.email_automation_monitor_thread.start()
        logger.info("email_automation.py进程监控线程已启动")

    def stop_email_automation_monitoring(self):
        """停止email_automation.py进程监控"""
        self.monitoring_email_automation = False
        logger.info("已停止email_automation.py进程监控")

    def run_monitoring_mode(self, headless=False):
        """运行监控模式（阻塞式）"""
        logger.info("启动监控模式...")

        # 首先验证和清理 api_keys.txt 文件格式
        logger.info("启动前检测 api_keys.txt 文件格式...")
        self.validate_and_clean_api_keys()

        # 首先处理一次现有的邮箱
        logger.info("首次检查现有邮箱...")
        if self.check_for_new_emails():
            logger.info("发现现有未处理邮箱，开始处理...")
            self.process_new_emails(headless=headless)
        else:
            logger.info("没有现有未处理邮箱")

        # 启动监控
        self.is_monitoring = True

        # 启动email_automation.py进程监控
        logger.info("启动email_automation.py进程监控...")
        self.start_email_automation_monitoring()

        try:
            self.monitor_loop(headless=headless)
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        finally:
            self.is_monitoring = False
            # 停止email_automation.py进程监控
            self.stop_email_automation_monitoring()
            # 确保浏览器被关闭
            self.close_browser()

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='BFL.AI 自动登录脚本 - 默认启动监控模式')
    parser.add_argument('--background', action='store_true', help='后台模式运行（最小化浏览器窗口）')
    parser.add_argument('--headless', action='store_true', help='无头模式运行（完全隐藏浏览器）')
    parser.add_argument('--once', action='store_true', help='一次性处理模式（处理完现有邮箱后退出，不监控）')
    args = parser.parse_args()

    automation = BFLLoginAutomation()

    # 如果不是后台模式，显示说明信息
    if not args.background and not args.headless:
        print("BFL.AI 自动登录脚本")
        print("=" * 50)
        print("功能说明：")
        print("- 读取 email_results 文件中的邮箱地址和对应链接")
        print("- 使用无痕模式打开浏览器")
        print("- 自动打开每个邮箱对应的链接")
        print("- 自动填写登录表单并尝试登录")
        print("- 登录成功后自动点击Try Demo按钮")
        print("- 自动处理欢迎弹窗（勾选复选框并点击Get Started）")
        print("- 自动跳转到API keys页面")
        print("- 自动获取API key并保存到api_keys.txt文件")
        print("- 每个邮箱处理完成后自动关闭浏览器")
        print("- 自动跳过已经获取过API key的邮箱")
        print("- 自动检测并跳过错误邮箱（如需要验证的邮箱）")
        print("- 自动记录错误邮箱到error_emails.txt文件")
        print("- 自动处理所有未处理的邮箱直到完成")
        print("- 统计处理成功和失败的邮箱")
        print("- 启动前自动检测并清理api_keys.txt文件中格式不正确的行")
        if args.once:
            print("- 一次性处理模式：处理完现有邮箱后退出")
        else:
            print("- 监控模式：每1分30秒检查一次新邮箱（默认模式）")
            print("- 后台监控email_automation.py进程，自动检测并重启崩溃的进程")
        print("=" * 50)

    # 检查邮箱文件是否存在（监控模式下可以稍后再检查）
    if args.once and not (os.path.exists('email_results.json') or os.path.exists('email_results.txt')):
        logger.error("未找到 email_results.json 或 email_results.txt 文件")
        logger.error("请先运行邮箱获取脚本生成邮箱地址文件")
        return

    # 根据参数决定运行模式
    if args.once:
        # 一次性处理模式
        logger.info("开始一次性处理模式...")
        if args.headless:
            logger.info("使用无头模式运行")
            automation.run_automation_background(headless=True)
        elif args.background:
            logger.info("使用后台模式运行")
            automation.run_automation_background(headless=False)
        else:
            automation.run_automation()
    else:
        # 默认启动监控模式
        if args.headless:
            logger.info("启动监控模式（无头模式）- 每1分30秒检查一次新邮箱...")
            automation.run_monitoring_mode(headless=True)
        elif args.background:
            logger.info("启动监控模式（后台模式）- 每1分30秒检查一次新邮箱...")
            automation.run_monitoring_mode(headless=False)
        else:
            logger.info("启动监控模式 - 每1分30秒检查一次新邮箱...")
            automation.run_monitoring_mode(headless=False)

if __name__ == "__main__":
    main()
