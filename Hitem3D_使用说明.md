# Hitem3D 自动注册系统使用说明

## 概述

本系统包含两个主要脚本，协同工作完成Hitem3D的自动注册流程：
- `hitem3d_email_automation.py` - 自动获取临时邮箱和提取验证码
- `hitem3d_registration.py` - 自动化浏览器操作完成注册

## 使用流程

### 1. 准备工作

1. **确保依赖安装完成**
   ```bash
   pip install -r requirements.txt
   ```

2. **检查Chrome浏览器**
   - 确保已安装Chrome浏览器
   - 脚本默认使用Profile 3配置文件

### 2. 启动注册流程

1. **首先启动注册主脚本**
   ```bash
   python hitem3d_registration.py
   ```
   
   脚本将自动执行以下操作：
   - 启动Chrome浏览器（使用Profile 3）
   - 打开proxy302代理页面
   - 选择Canada (CA)作为代理位置
   - 点击"打开"按钮访问Hitem3D
   - 等待页面加载后点击"Sign up"按钮
   - 处理登录弹窗，点击其中的Sign up链接
   - 进入注册表单页面

2. **当看到"请启动 hitem3d_email_automation.py 脚本"提示时**，在新的终端窗口运行：
   ```bash
   python hitem3d_email_automation.py
   ```
   
   邮箱脚本将自动：
   - 获取临时邮箱地址
   - 将邮箱保存到`current_email.txt`
   - 监控接收的邮件
   - 提取6位验证码并保存到`verification_code.txt`

3. **自动填写流程**
   - 注册脚本会自动检测邮箱文件变化
   - 自动填写邮箱地址
   - 自动点击发送验证码
   - 自动填写接收到的验证码
   - 自动填写预设的密码和昵称
   - 自动提交注册表单

## 配置说明

### 默认配置（可在`hitem3d_registration.py`中修改）
```python
self.password = "MySecurePassword123!"       # 注册密码
self.confirm_password = "MySecurePassword123!"  # 确认密码
self.nickname = "TestUser"                   # 昵称前缀（会自动添加随机数字）
```

### 代理设置
- 默认选择：Canada (CA)
- 如需修改，可在代码中更改`select_country_canada()`方法

## 文件说明

### 生成的文件
- `current_email.txt` - 当前使用的邮箱地址
- `verification_code.txt` - 接收到的验证码
- `hitem3d_registration.log` - 注册脚本日志
- `hitem3d_email.log` - 邮箱脚本日志

### 监控文件
- `request_new_email.txt` - 请求新邮箱的触发文件
- `switch_proxy_request.txt` - 切换代理的触发文件

## 注意事项

1. **运行顺序**：必须先启动`hitem3d_registration.py`，等待提示后再启动`hitem3d_email_automation.py`

2. **浏览器配置**：脚本使用Chrome的Profile 3，确保该配置文件存在且未被其他程序占用

3. **代理要求**：需要proxy302代理扩展正常工作

4. **网络要求**：确保网络连接稳定，能访问tempmail.la

5. **验证码有效期**：验证码通常有时效性，脚本会自动快速处理

## 常见问题

### Q: 找不到Sign up按钮
A: 可能是页面加载未完成，脚本会尝试多种方法查找按钮

### Q: 验证码未自动填写
A: 检查`verification_code.txt`文件是否正确生成，确保邮箱脚本正在运行

### Q: 注册失败
A: 查看日志文件了解详细错误信息，可能是：
- 邮箱已被使用
- 密码不符合要求
- 网络连接问题

## 调试技巧

1. **查看日志**
   - `hitem3d_registration.log` - 详细的注册流程日志
   - `hitem3d_email.log` - 邮箱获取和验证码提取日志

2. **手动干预**
   - 脚本会保持浏览器开启，可随时手动操作
   - 如自动化失败，可手动完成剩余步骤

3. **截图功能**
   - 脚本在关键步骤失败时会自动保存截图
   - 截图文件名包含时间戳，便于问题定位 