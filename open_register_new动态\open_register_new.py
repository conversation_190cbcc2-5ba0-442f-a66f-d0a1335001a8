import time
import os
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException

class RegistrationAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.current_email = ""
        self.last_email_content = ""
        self.registration_tab = None
        self.monitoring_email_file = False
        self.email_file = "current_email.txt"
        self.request_file = "request_new_email.txt"
        self.switch_proxy_file = "switch_proxy_request.txt"  # 代理切换请求文件
        self.monitoring_proxy_switch = False  # 是否正在监控代理切换请求
        self.reset_count_file = "reset_refresh_count.txt"  # 重置刷新计数通知文件
        
        # 配置参数
        self.password = "MySecurePassword123!"
        self.confirm_password = "MySecurePassword123!"
        self.auto_submit = True  # 是否自动提交表单
        self.auto_check_terms = True  # 是否自动勾选条款复选框
        self.auto_refresh_on_success = True  # 是否在注册成功后自动刷新页面

    def setup_browser(self):
        """设置并启动浏览器"""
        # 直接使用Selenium启动浏览器，使用Profile 3
        chrome_options = Options()
        chrome_options.binary_location = "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"

        # 创建临时用户数据目录来避免冲突
        import os
        import tempfile
        # 使用环境变量中的时间戳创建唯一目录，避免多进程冲突
        suffix = os.environ.get('CHROME_TEMP_SUFFIX', str(int(time.time() * 1000)))
        temp_user_data_dir = os.path.join(tempfile.gettempdir(), f"chrome_selenium_profile3_{suffix}")

        # 如果临时目录存在，先删除
        if os.path.exists(temp_user_data_dir):
            import shutil
            try:
                shutil.rmtree(temp_user_data_dir)
            except:
                pass

        # 复制Profile 3的数据到临时目录
        original_profile_dir = os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 3")
        temp_profile_dir = os.path.join(temp_user_data_dir, "Profile 3")

        if os.path.exists(original_profile_dir):
            print("正在复制Profile 3数据...")
            import shutil
            try:
                shutil.copytree(original_profile_dir, temp_profile_dir)
                print("Profile 3数据复制完成")
            except Exception as e:
                print(f"复制Profile数据失败: {e}")
                print("将使用默认配置启动...")

        chrome_options.add_argument(f"--user-data-dir={temp_user_data_dir}")
        chrome_options.add_argument("--profile-directory=Profile 3")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")

        # 确保扩展能够加载
        chrome_options.add_argument("--enable-extensions")
        # 如果你有特定的扩展需要加载，可以取消注释下面的行并指定扩展路径
        # chrome_options.add_argument("--load-extension=C:\\path\\to\\your\\extension")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-translate")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 地理位置和语言伪装设置
        chrome_options.add_argument("--lang=en-US")  # 设置语言为英语
        chrome_options.add_argument("--disable-geolocation")  # 禁用地理位置
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI")  # 禁用翻译UI
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")

        # WebRTC设置 - 防止IP泄露
        chrome_options.add_argument("--force-webrtc-ip-handling-policy=disable_non_proxied_udp")
        chrome_options.add_argument("--enforce-webrtc-ip-permission-check")

        # DNS设置 - 防止DNS泄露
        chrome_options.add_argument("--dns-prefetch-disable")
        chrome_options.add_argument("--disable-dns-prefetch")

        # 时区设置为美国东部时间
        chrome_options.add_argument("--timezone=America/New_York")

        # 强制设置Accept-Language头
        chrome_options.add_argument("--accept-lang=en-US,en;q=0.9")

        # 添加用户代理，使其看起来更像美国的正常浏览器
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # 添加反检测参数（保留扩展功能）
        chrome_options.add_argument("--disable-client-side-phishing-detection")
        chrome_options.add_argument("--disable-component-update")
        chrome_options.add_argument("--disable-domain-reliability")
        chrome_options.add_argument("--disable-hang-monitor")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-prompt-on-repost")
        chrome_options.add_argument("--metrics-recording-only")
        chrome_options.add_argument("--safebrowsing-disable-auto-update")
        chrome_options.add_argument("--password-store=basic")
        chrome_options.add_argument("--use-mock-keychain")

        # 剪切板权限相关参数 - 强制启用
        chrome_options.add_argument("--enable-clipboard-api")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--enable-experimental-web-platform-features")
        chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")
        chrome_options.add_argument("--disable-web-security")  # 禁用网络安全限制
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI,BlinkGenPropertyTrees")
        chrome_options.add_argument("--enable-features=ClipboardAPI")  # 强制启用剪贴板API
        chrome_options.add_argument("--allow-running-insecure-content")  # 允许不安全内容
        chrome_options.add_argument("--disable-site-isolation-trials")  # 禁用站点隔离
        chrome_options.add_argument("--disable-permissions-api")  # 禁用权限API检查

        # 设置首选语言和权限
        prefs = {
            "intl.accept_languages": "en-US,en",
            "profile.default_content_setting_values.geolocation": 2,  # 阻止地理位置请求
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            # 剪切板权限设置 - 自动授权所有网站
            "profile.default_content_setting_values.clipboard": 1,  # 1=允许, 2=阻止
            "profile.content_settings.exceptions.clipboard": {
                "https://auth.bfl.ai,*": {
                    "setting": 1
                },
                "https://dash.proxy302.com,*": {
                    "setting": 1
                },
                "[*.]bfl.ai,*": {
                    "setting": 1
                },
                "[*.]proxy302.com,*": {
                    "setting": 1
                },
                "*": {
                    "setting": 1
                }
            },
            # 强制允许剪贴板访问
            "profile.managed_default_content_settings.clipboard": 1,
            # 其他权限设置
            "profile.default_content_setting_values.notifications": 2,  # 阻止通知
            "profile.default_content_setting_values.media_stream": 2,  # 阻止摄像头/麦克风
        }
        chrome_options.add_experimental_option("prefs", prefs)

        print("正在启动浏览器...")
        print(f"临时目录: {temp_user_data_dir}")
        print(f"进程ID: {os.getpid()}")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("浏览器启动成功！")

            # 打开一个自定义的启动页面，而不是在地址栏输入内容
            startup_html = """
            <html>
            <head>
                <title>注册自动化工具 - 已启动</title>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        margin: 0;
                        padding: 40px;
                        min-height: 100vh;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                    }
                    .container {
                        background: rgba(255, 255, 255, 0.1);
                        backdrop-filter: blur(10px);
                        border-radius: 20px;
                        padding: 40px;
                        text-align: center;
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                        max-width: 600px;
                    }
                    h1 {
                        font-size: 2.5em;
                        margin-bottom: 20px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                    }
                    .status {
                        font-size: 1.2em;
                        margin: 20px 0;
                        padding: 15px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                    }
                    .info {
                        font-size: 1em;
                        margin: 10px 0;
                        opacity: 0.9;
                    }
                    .emoji {
                        font-size: 1.5em;
                        margin-right: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1><span class="emoji">🚀</span>注册自动化工具</h1>
                    <div class="status">
                        <span class="emoji">✅</span>浏览器已成功启动
                    </div>
                    <div class="info">
                        <span class="emoji">🔧</span>正在初始化设置...
                    </div>
                    <div class="info">
                        <span class="emoji">🌐</span>配置地理位置伪装
                    </div>
                    <div class="info">
                        <span class="emoji">📋</span>设置剪贴板权限
                    </div>
                    <div class="info">
                        <span class="emoji">🛡️</span>应用反检测措施
                    </div>
                    <div class="status" style="margin-top: 30px;">
                        <span class="emoji">⏳</span>准备访问代理页面...
                    </div>
                </div>
            </body>
            </html>
            """
            self.driver.get("data:text/html," + startup_html.replace('\n', '').replace('  ', ''))
            time.sleep(2)  # 等待页面加载和用户查看

            # 首先设置剪切板权限
            self.setup_clipboard_permissions()

            # 隐藏自动化特征并伪装地理位置信息
            self.driver.execute_script("""
                // 隐藏webdriver特征
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});

                // 删除自动化相关属性
                delete navigator.__proto__.webdriver;

                // 伪装Chrome对象
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });

                // 伪装权限API并自动授权剪切板
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => {
                    if (parameters.name === 'notifications') {
                        return Promise.resolve({ state: Notification.permission });
                    } else if (parameters.name === 'clipboard-read' || parameters.name === 'clipboard-write') {
                        // 自动授权剪切板权限
                        return Promise.resolve({ state: 'granted' });
                    }
                    return originalQuery(parameters);
                };

                // 确保剪切板API可用
                if (navigator.clipboard) {
                    console.log('剪切板API已可用');
                } else {
                    console.log('剪切板API不可用，尝试启用');
                }

                // 强制伪装地理位置和语言
                Object.defineProperty(navigator, 'language', {
                    get: () => 'en-US',
                    configurable: false,
                    enumerable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: false,
                    enumerable: true
                });

                // 伪装时区
                Date.prototype.getTimezoneOffset = function() { return 300; }; // UTC-5 (美国东部时间)

                // 伪装硬件信息
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 4
                });

                // 伪装内存信息
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8
                });

                // 全局拦截所有网络请求
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                    // 强制修改reCAPTCHA语言
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('全局拦截reCAPTCHA请求:', url);
                    }
                    const result = originalOpen.apply(this, [method, url, async, user, password]);
                    this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                    return result;
                };

                // 全局拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(input, init) {
                    let url = typeof input === 'string' ? input : input.url;
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('全局拦截fetch reCAPTCHA请求:', url);
                        if (typeof input === 'string') {
                            input = url;
                        } else {
                            input = new Request(url, input);
                        }
                    }
                    init = init || {};
                    init.headers = init.headers || {};
                    init.headers['Accept-Language'] = 'en-US,en;q=0.9';
                    return originalFetch(input, init);
                };

                // 禁用WebRTC
                if (window.RTCPeerConnection) {
                    window.RTCPeerConnection = undefined;
                }
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = undefined;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = undefined;
                }

                // 伪装屏幕信息（美国常见分辨率）
                Object.defineProperty(screen, 'width', {get: () => 1920});
                Object.defineProperty(screen, 'height', {get: () => 1080});
                Object.defineProperty(screen, 'availWidth', {get: () => 1920});
                Object.defineProperty(screen, 'availHeight', {get: () => 1040});

                // 移除自动化检测标记
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                console.log('全局地理位置和reCAPTCHA伪装设置完成');
            """)
            return True
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            return False

    def setup_clipboard_permissions(self):
        """设置剪切板权限，确保自动授权"""
        try:
            print("正在设置剪切板权限...")
            self.driver.execute_script("""
                // 立即设置剪切板权限
                if (navigator.permissions && navigator.permissions.query) {
                    // 重写权限查询方法，自动授权剪切板
                    const originalQuery = navigator.permissions.query;
                    navigator.permissions.query = function(parameters) {
                        if (parameters.name === 'clipboard-read' || parameters.name === 'clipboard-write') {
                            console.log('自动授权剪切板权限:', parameters.name);
                            return Promise.resolve({
                                state: 'granted',
                                onchange: null
                            });
                        }
                        return originalQuery.call(this, parameters);
                    };
                }

                // 确保剪切板API可用
                if (navigator.clipboard) {
                    console.log('✅ 剪切板API已可用');

                    // 测试剪切板权限
                    navigator.permissions.query({name: 'clipboard-read'}).then(result => {
                        console.log('剪切板读取权限状态:', result.state);
                    }).catch(err => {
                        console.log('剪切板权限查询失败:', err);
                    });

                    navigator.permissions.query({name: 'clipboard-write'}).then(result => {
                        console.log('剪切板写入权限状态:', result.state);
                    }).catch(err => {
                        console.log('剪切板权限查询失败:', err);
                    });
                } else {
                    console.log('❌ 剪切板API不可用');
                }

                console.log('剪切板权限设置完成');
            """)
            print("✅ 剪切板权限设置完成")
            return True
        except Exception as e:
            print(f"设置剪切板权限失败: {e}")
            return False

    def apply_geolocation_spoofing(self):
        """在每个页面加载后应用地理位置伪装"""
        try:
            self.driver.execute_script("""
                // 重新应用地理位置伪装（防止页面刷新后失效）
                Object.defineProperty(navigator, 'language', {
                    get: () => 'en-US',
                    configurable: false,
                    enumerable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: false,
                    enumerable: true
                });

                // 强制伪装Accept-Language
                if (window.XMLHttpRequest) {
                    const originalOpen = XMLHttpRequest.prototype.open;
                    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                        const result = originalOpen.apply(this, arguments);
                        this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                        return result;
                    };
                }

                // 伪装fetch请求的Accept-Language
                if (window.fetch) {
                    const originalFetch = window.fetch;
                    window.fetch = function(input, init) {
                        init = init || {};
                        init.headers = init.headers || {};
                        if (!init.headers['Accept-Language']) {
                            init.headers['Accept-Language'] = 'en-US,en;q=0.9';
                        }
                        return originalFetch(input, init);
                    };
                }

                // 伪装时区
                Date.prototype.getTimezoneOffset = function() { return 300; }; // UTC-5

                // 伪装Intl对象
                if (window.Intl && window.Intl.DateTimeFormat) {
                    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                    Intl.DateTimeFormat.prototype.resolvedOptions = function() {
                        const options = originalResolvedOptions.call(this);
                        options.locale = 'en-US';
                        options.timeZone = 'America/New_York';
                        return options;
                    };
                }

                // 禁用地理位置API
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition = function(success, error) {
                        if (error) {
                            error({code: 1, message: 'User denied Geolocation'});
                        }
                    };
                    navigator.geolocation.watchPosition = function(success, error) {
                        if (error) {
                            error({code: 1, message: 'User denied Geolocation'});
                        }
                    };
                }

                // 禁用WebRTC
                if (window.RTCPeerConnection) {
                    window.RTCPeerConnection = undefined;
                }
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = undefined;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = undefined;
                }

                console.log('地理位置伪装已应用');
            """)
        except Exception as e:
            print(f"应用地理位置伪装失败: {e}")

    def setup_recaptcha_interception(self):
        """设置reCAPTCHA请求拦截，强制使用英文版本"""
        try:
            self.driver.execute_script("""
                // 拦截所有网络请求，特别是reCAPTCHA相关的
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                    // 如果是reCAPTCHA请求，强制修改为英文版本
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('拦截并修改reCAPTCHA请求为英文版本:', url);
                    }

                    const result = originalOpen.apply(this, [method, url, async, user, password]);

                    // 设置请求头
                    this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');

                    return result;
                };

                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(input, init) {
                    let url = typeof input === 'string' ? input : input.url;

                    // 修改reCAPTCHA URL
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('拦截并修改fetch reCAPTCHA请求为英文版本:', url);
                        if (typeof input === 'string') {
                            input = url;
                        } else {
                            input = new Request(url, input);
                        }
                    }

                    init = init || {};
                    init.headers = init.headers || {};
                    init.headers['Accept-Language'] = 'en-US,en;q=0.9';

                    return originalFetch(input, init);
                };

                console.log('reCAPTCHA请求拦截器已设置');
            """)
        except Exception as e:
            print(f"设置reCAPTCHA拦截失败: {e}")

    def open_proxy_page(self):
        """打开代理页面并先选择代理，然后再打开新标签"""
        try:
            print("正在访问代理页面...")
            self.driver.get("https://dash.proxy302.com/quick-access")
            time.sleep(5)  # 增加等待时间，确保页面完全加载
            print(f"代理页面加载完成，当前URL: {self.driver.current_url}")

            # 应用地理位置伪装和reCAPTCHA拦截
            self.apply_geolocation_spoofing()
            self.setup_recaptcha_interception()

            # 等待表格加载完成
            print("等待表格内容加载...")
            time.sleep(3)

            # 检查表格是否加载
            try:
                self.driver.find_element(By.CSS_SELECTOR, '.arco-table')
                print("表格已加载")
            except:
                print("表格未找到，但继续尝试操作")

            # 先选择代理
            print("🔄 开始选择代理流程...")
            if self.select_proxy_first():
                print("✅ 代理选择完成，现在点击打开按钮...")
                # 代理选择完成后，再点击打开按钮
                return self.click_open_button()
            else:
                print("❌ 代理选择失败")
                return False

        except TimeoutException:
            print("代理页面操作超时")
            return False

        except Exception as e:
            print(f"访问代理页面失败: {e}")
            return False

    def navigate_to_register_page(self):
        """导航到注册页面"""
        max_retries = 3  # 最多重试3次

        for attempt in range(max_retries):
            try:
                print(f"正在访问注册页面... (尝试 {attempt + 1}/{max_retries})")

                # 记录开始时间
                start_time = time.time()

                # 访问注册页面
                self.driver.get("https://auth.bfl.ai/register")

                # 应用地理位置伪装和reCAPTCHA拦截
                time.sleep(2)  # 等待页面开始加载
                self.apply_geolocation_spoofing()
                self.setup_recaptcha_interception()

                # 监控页面加载状态，最多等待30秒（减少等待时间）
                loading_timeout = 30
                check_interval = 2

                while time.time() - start_time < loading_timeout:
                    try:
                        # 检查页面是否加载完成
                        ready_state = self.driver.execute_script("return document.readyState")
                        current_url = self.driver.current_url

                        print(f"页面状态: {ready_state}, URL: {current_url}")

                        # 检查是否有错误信息出现（如Chrome错误页面）
                        page_source = self.driver.page_source
                        error_indicators = [
                            "Registration URL fetching failed",
                            "Failed to decrypt",
                            "ERR_",
                            "This site can't be reached",
                            "无法访问此网站",
                            "连接超时",
                            "网络错误"
                        ]

                        # 检查是否出现了错误信息
                        has_error = any(error in page_source for error in error_indicators)
                        if has_error:
                            print("❌ 检测到页面加载错误，尝试重新打开标签页")
                            # 如果有多个标签页，关闭当前标签页并重新打开
                            if len(self.driver.window_handles) > 1:
                                print("关闭出现错误的标签页...")
                                self.driver.close()
                                # 切换回主标签页
                                self.driver.switch_to.window(self.driver.window_handles[0])
                                time.sleep(2)

                                # 尝试重新打开注册页面
                                print("尝试重新访问注册页面...")
                                try:
                                    self.driver.get("https://auth.bfl.ai/register")
                                    print("已重新访问注册页面")
                                    # 重置计时器，重新开始等待
                                    start_time = time.time()
                                    continue
                                except Exception as reopen_error:
                                    print(f"重新访问注册页面失败: {reopen_error}")
                                    break
                            else:
                                print("只有一个标签页，直接跳出循环进入切换代理流程")
                                break

                        # 检查是否成功加载到注册页面
                        if ready_state == "complete" and "auth.bfl.ai/register" in current_url:
                            # 再等待一下确保页面元素加载完成
                            time.sleep(3)

                            # 检查是否有表单元素
                            try:
                                self.driver.find_element(By.CSS_SELECTOR, 'input[name="email"], input[type="email"]')
                                print(f"✅ 注册页面加载完成，当前URL: {current_url}")
                                return True
                            except:
                                print("页面已加载但未找到邮箱输入框，继续等待...")

                        time.sleep(check_interval)

                    except Exception as check_error:
                        print(f"检查页面状态时出错: {check_error}")
                        time.sleep(check_interval)

                # 超时处理
                print(f"⚠ 页面加载超时 ({loading_timeout}秒)")

                # 如果有多个标签页，关闭当前标签页
                if len(self.driver.window_handles) > 1:
                    print("关闭当前加载超时的标签页...")
                    self.driver.close()
                    # 切换回主标签页
                    self.driver.switch_to.window(self.driver.window_handles[0])
                    time.sleep(2)

                # 直接中断当前重试循环，进入切换代理流程
                break

            except Exception as e:
                print(f"访问注册页面失败 (尝试 {attempt + 1}): {e}")
                # 如果有多个标签页，关闭当前标签页
                if len(self.driver.window_handles) > 1:
                    print("关闭出现异常的标签页...")
                    self.driver.close()
                    # 切换回主标签页
                    self.driver.switch_to.window(self.driver.window_handles[0])
                    time.sleep(2)

                # 直接跳出循环，不再重试，进入切换代理流程
                break

        print("❌ 无法正常加载注册页面，尝试切换代理...")
        # 尝试切换代理后再试一次
        if self.switch_proxy_and_retry():
            # 切换代理成功后，直接返回True，无需重新调用navigate_to_register_page
            # 因为switch_proxy_and_retry方法中已经包含了重新打开注册页面的操作
            return True
        else:
            print("❌ 切换代理失败，无法加载注册页面")
            return False

    def select_proxy_first(self):
        """在页面加载后立即选择代理"""
        try:
            print("正在执行代理选择流程...")

            # 查找"选择代理"按钮
            print("正在查找选择代理按钮...")
            select_proxy_button = None

            try:
                # 查找包含"Select Proxy"文本的按钮
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "Select Proxy" in button.text:
                        select_proxy_button = button
                        print("找到选择代理按钮")
                        break

                if not select_proxy_button:
                    # 尝试通过XPath查找
                    select_proxy_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Select Proxy')]")
                    print("通过XPath找到选择代理按钮")

            except Exception as e:
                print(f"查找选择代理按钮失败: {e}")
                return False

            if select_proxy_button:
                # 点击选择代理按钮
                print("点击选择代理按钮...")
                select_proxy_button.click()
                time.sleep(3)

                # 等待弹窗出现
                try:
                    modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                    print("代理选择弹窗已打开")

                    # 查找"Rotating IP Pay by Traffic"标签页
                    print("正在查找轮换IP按流量扣费标签...")
                    rotating_ip_tab = None

                    try:
                        # 查找包含"Rotating IP Pay by Traffic"的标签
                        tab_titles = modal_container.find_elements(By.CSS_SELECTOR, '.arco-tabs-tab-title')
                        for tab in tab_titles:
                            if "Rotating IP Pay by Traffic" in tab.text:
                                rotating_ip_tab = tab
                                print("找到轮换IP按流量扣费标签")
                                break

                    except Exception as e:
                        print(f"查找轮换IP标签失败: {e}")

                    if rotating_ip_tab:
                        # 点击轮换IP标签
                        print("点击轮换IP按流量扣费标签...")
                        rotating_ip_tab.click()
                        time.sleep(2)

                        # 先选择国家，然后点击生成按钮
                        print("正在选择国家...")
                        if self.select_country_afghanistan():
                            print("✅ 已选择Afghanistan (AF)")
                        else:
                            print("⚠ 选择国家失败，继续生成...")

                        # 点击生成按钮
                        print("正在点击生成按钮...")
                        generate_button = None

                        try:
                            # 重新获取弹窗容器
                            modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')

                            # 查找包含"Generate Now"文本的按钮
                            buttons = modal_container.find_elements(By.TAG_NAME, "button")
                            for button in buttons:
                                if "Generate Now" in button.text:
                                    generate_button = button
                                    print("找到生成按钮")
                                    break

                            if not generate_button:
                                # 尝试通过XPath查找
                                generate_button = modal_container.find_element(By.XPATH, ".//button[contains(text(), 'Generate Now')]")
                                print("通过XPath找到生成按钮")

                        except Exception as e:
                            print(f"查找生成按钮失败: {e}")
                            print("跳过生成，直接选择现有代理...")

                        if generate_button:
                            # 检查按钮是否可点击
                            if generate_button.is_enabled() and generate_button.is_displayed():
                                # 滚动到按钮位置
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", generate_button)
                                time.sleep(1)

                                # 点击生成按钮
                                try:
                                    generate_button.click()
                                    print("✅ 已点击生成按钮")

                                    # 等待代理生成成功
                                    print("等待代理生成完成...")
                                    if self.check_proxy_generation_success(max_wait_seconds=30):
                                        print("✅ 代理生成成功")
                                    else:
                                        print("⚠ 代理生成可能失败，继续选择代理...")

                                except Exception as click_error:
                                    print(f"点击生成按钮失败: {click_error}")
                                    print("继续选择现有代理...")
                            else:
                                print("生成按钮不可点击，选择现有代理...")
                        else:
                            print("未找到生成按钮，选择现有代理...")

                        # 等待一下确保代理列表更新
                        time.sleep(3)

                        # 选择代理
                        if self.select_proxy_in_modal():
                            print("✅ 代理选择完成")
                            return True
                        else:
                            print("新方法失败，尝试原方法...")
                            if self.select_first_proxy():
                                print("✅ 通过原方法成功选择代理")
                                return True
                            else:
                                print("❌ 代理选择失败")
                                return False
                    else:
                        print("❌ 未找到轮换IP按流量扣费标签")
                        return False

                except Exception as e:
                    print(f"处理代理选择弹窗失败: {e}")
                    return False
            else:
                print("❌ 未找到选择代理按钮")
                return False

        except Exception as e:
            print(f"选择代理流程失败: {e}")
            return False

    def click_open_button(self):
        """点击打开按钮的独立方法"""
        max_retries = 3  # 最多重试3次

        for retry in range(max_retries):
            try:
                print(f"正在查找打开按钮... (尝试 {retry + 1}/{max_retries})")

                # 等待加载遮罩层消失
                print("等待页面加载完成...")
                try:
                    # 等待遮罩层消失
                    WebDriverWait(self.driver, 10).until_not(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.arco-spin-mask'))
                    )
                    print("✓ 加载遮罩层已消失")
                except:
                    print("未检测到加载遮罩层或已消失")

                # 额外等待确保页面稳定
                time.sleep(2)

                # 查找所有按钮
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                print(f"页面上找到 {len(buttons)} 个按钮:")

                open_button = None
                for i, btn in enumerate(buttons):
                    btn_text = btn.text.strip()
                    btn_class = btn.get_attribute('class')
                    print(f"  按钮 {i+1}: 文本='{btn_text}', 类名='{btn_class}'")

                    # 检查是否是正确的Open按钮（主要的蓝色按钮）
                    if btn_text == 'Open':
                        # 检查按钮类名，确保是主要按钮（通常包含primary）
                        if 'arco-btn-primary' in btn_class:
                            print(f"找到主要的打开按钮 (按钮 {i+1})，正在点击...")
                            open_button = btn
                            break
                        else:
                            print(f"找到打开按钮但不是主要按钮 (按钮 {i+1})，继续查找...")

                # 如果没找到主要的Open按钮，尝试找任何Open按钮
                if not open_button:
                    for i, btn in enumerate(buttons):
                        btn_text = btn.text.strip()
                        if btn_text == 'Open':
                            print(f"找到备用打开按钮 (按钮 {i+1})，正在点击...")
                            open_button = btn
                            break

                if open_button:
                    # 检查按钮是否可点击
                    if not open_button.is_enabled() or not open_button.is_displayed():
                        print("按钮不可用或不可见，等待后重试...")
                        time.sleep(3)
                        continue

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", open_button)
                    time.sleep(1)

                    # 尝试点击按钮
                    try:
                        open_button.click()
                        print("✅ 成功点击打开按钮")
                        # 点击Open按钮时清空代理切换计数
                        self.notify_reset_proxy_switch_count()
                    except Exception as click_error:
                        print(f"普通点击失败: {click_error}")
                        # 尝试JavaScript点击
                        try:
                            self.driver.execute_script("arguments[0].click();", open_button)
                            print("✅ 通过JavaScript成功点击打开按钮")
                            # 点击Open按钮时清空代理切换计数
                            self.notify_reset_proxy_switch_count()
                        except Exception as js_error:
                            print(f"JavaScript点击也失败: {js_error}")
                            if retry < max_retries - 1:
                                print("等待后重试...")
                                time.sleep(3)
                                continue
                            else:
                                return False

                    # 等待新标签页打开
                    time.sleep(3)

                    # 切换到新打开的标签页
                    if len(self.driver.window_handles) > 1:
                        self.registration_tab = self.driver.window_handles[-1]  # 最新的标签页
                        self.driver.switch_to.window(self.registration_tab)
                        print("已切换到新标签页")
                        time.sleep(2)  # 等待页面加载

                        print("✅ 注册页面已打开，开始检测页面加载状态...")

                        # 检测注册页面是否正常加载
                        if self.check_registration_page_loading():
                            print("✅ 注册页面加载成功")
                            print("💡 如果遇到429错误，请手动关闭注册标签页")
                            print("🔔 脚本已完成任务，不进行任何监控")
                            return True
                        else:
                            print("❌ 注册页面加载失败，重新进入代理选择流程")
                            # 关闭失败的注册标签页
                            self.driver.close()
                            # 切换回代理标签页
                            self.driver.switch_to.window(self.driver.window_handles[0])
                            print("已关闭失败的注册标签页，切换回代理标签页")

                            # 重新进入代理选择流程
                            print("🔄 重新开始代理选择流程...")
                            if self.select_proxy_first():
                                print("✅ 代理重新选择完成，再次点击打开按钮...")
                                # 递归调用，但要避免无限循环
                                if retry < max_retries - 1:
                                    continue  # 继续重试循环
                                else:
                                    print("❌ 已达到最大重试次数")
                                    return False
                            else:
                                print("❌ 重新选择代理失败")
                                return False
                    else:
                        print("未检测到新标签页")
                        if retry < max_retries - 1:
                            print("等待后重试...")
                            time.sleep(3)
                            continue
                        else:
                            return False


                else:
                    print("未找到打开按钮")
                    if retry < max_retries - 1:
                        print("等待后重试...")
                        time.sleep(3)
                        continue
                    else:
                        return False

            except Exception as e:
                print(f"点击打开按钮失败: {e}")
                if retry < max_retries - 1:
                    print("等待后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

        print(f"❌ 经过{max_retries}次尝试后仍然失败")
        return False



    def generate_new_proxy(self):
        """生成新代理"""
        try:
            print("🔄 开始生成新代理...")

            # 确保在代理页面
            current_url = self.driver.current_url
            if "dash.proxy302.com" not in current_url:
                print("不在代理页面，先导航到代理页面...")
                self.driver.get("https://dash.proxy302.com/quick-access")
                time.sleep(3)

            # 点击选择代理按钮打开弹窗
            print("正在打开代理选择弹窗...")
            if not self.open_proxy_selection_modal():
                print("❌ 无法打开代理选择弹窗")
                return False

            # 切换到轮换IP标签页
            print("正在切换到轮换IP标签页...")
            if not self.switch_to_rotating_ip_tab():
                print("❌ 无法切换到轮换IP标签页")
                return False

            # 点击生成按钮
            print("正在点击生成按钮...")
            if not self.click_generate_button():
                print("❌ 无法点击生成按钮")
                return False

            # 等待代理生成成功
            print("等待代理生成完成...")
            if not self.check_proxy_generation_success(max_wait_seconds=30):
                print("❌ 代理生成失败或超时")
                return False

            # 选择新生成的代理
            print("正在选择新生成的代理...")
            if not self.select_newly_generated_proxy():
                print("❌ 无法选择新生成的代理")
                return False

            print("✅ 新代理生成并选择成功")
            return True

        except Exception as e:
            print(f"生成新代理时出错: {e}")
            return False

    def open_proxy_selection_modal(self):
        """打开代理选择弹窗"""
        try:
            # 查找"选择代理"按钮
            select_proxy_button = None

            try:
                # 查找包含"Select Proxy"文本的按钮
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "Select Proxy" in button.text:
                        select_proxy_button = button
                        print("找到选择代理按钮")
                        break

                if not select_proxy_button:
                    # 尝试通过XPath查找
                    select_proxy_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Select Proxy')]")
                    print("通过XPath找到选择代理按钮")

            except Exception as e:
                print(f"查找选择代理按钮失败: {e}")
                return False

            if select_proxy_button:
                # 点击选择代理按钮
                print("点击选择代理按钮...")
                select_proxy_button.click()
                time.sleep(3)

                # 等待弹窗出现
                try:
                    modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                    print("代理选择弹窗已打开")
                    return True
                except Exception as e:
                    print(f"等待弹窗出现失败: {e}")
                    return False
            else:
                print("❌ 未找到选择代理按钮")
                return False

        except Exception as e:
            print(f"打开代理选择弹窗时出错: {e}")
            return False

    def switch_to_rotating_ip_tab(self):
        """切换到轮换IP标签页"""
        try:
            # 查找"Rotating IP Pay by Traffic"标签页
            print("正在查找轮换IP按流量扣费标签...")
            rotating_ip_tab = None

            try:
                modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                # 查找包含"Rotating IP Pay by Traffic"的标签
                tab_titles = modal_container.find_elements(By.CSS_SELECTOR, '.arco-tabs-tab-title')
                for tab in tab_titles:
                    if "Rotating IP Pay by Traffic" in tab.text:
                        rotating_ip_tab = tab
                        print("找到轮换IP按流量扣费标签")
                        break

            except Exception as e:
                print(f"查找轮换IP标签失败: {e}")
                return False

            if rotating_ip_tab:
                # 点击轮换IP标签
                print("点击轮换IP按流量扣费标签...")
                rotating_ip_tab.click()
                time.sleep(2)
                print("✅ 已切换到轮换IP标签页")
                return True
            else:
                print("❌ 未找到轮换IP按流量扣费标签")
                return False

        except Exception as e:
            print(f"切换到轮换IP标签页时出错: {e}")
            return False

    def select_country_afghanistan(self):
        """点击选择器激活搜索模式，然后输入Afghanistan (AF)"""
        try:
            print("正在查找国家选择器...")

            # 查找国家选择器容器
            country_selector = None

            try:
                modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')

                # 查找arco-select容器
                selector_selectors = [
                    '.arco-select-view-single.arco-select',
                    '.arco-select.custom-form-control',
                    '.arco-select-view-single',
                    '.arco-select'
                ]

                for selector in selector_selectors:
                    try:
                        country_selector = modal_container.find_element(By.CSS_SELECTOR, selector)
                        print(f"通过选择器 '{selector}' 找到国家选择器")
                        break
                    except:
                        continue

            except Exception as e:
                print(f"查找国家选择器失败: {e}")
                return False

            if country_selector:
                # 首先检查当前显示的值
                try:
                    # 查找显示当前值的元素
                    value_display = country_selector.find_element(By.CSS_SELECTOR, '.arco-select-view-value')
                    current_text = value_display.text.strip()

                    print(f"当前选中值: '{current_text}'")

                    # 检查是否已经是Afghanistan
                    if "Afghanistan (AF)" in current_text:
                        print("✅ 检测到已经选中Afghanistan (AF)，无需重新选择")
                        return True
                    else:
                        print(f"当前选中的不是Afghanistan，需要重新选择。当前值: {current_text}")

                except Exception as check_error:
                    print(f"检查当前值时出错: {check_error}，继续选择流程")

                # 滚动到选择器位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", country_selector)
                time.sleep(1)

                # 点击选择器激活搜索模式
                try:
                    country_selector.click()
                    print("已点击国家选择器，激活搜索模式")
                    time.sleep(1)

                    # 现在查找输入框（应该已经激活）
                    country_input = None
                    input_selectors = [
                        '.arco-select-view-input',
                        'input.arco-select-view-input',
                        'input[placeholder*="Random"]',
                        'input[placeholder*="Afghanistan"]'
                    ]

                    for selector in input_selectors:
                        try:
                            country_input = modal_container.find_element(By.CSS_SELECTOR, selector)
                            print(f"通过选择器 '{selector}' 找到激活的输入框")
                            break
                        except:
                            continue

                    if country_input:
                        # 清空输入框并输入Afghanistan
                        try:
                            # 先清空
                            country_input.clear()
                            print("已清空输入框")
                            time.sleep(0.5)

                            # 输入Afghanistan (AF)
                            country_input.send_keys("Afghanistan (AF)")
                            print("✅ 已输入Afghanistan (AF)")
                            time.sleep(1)

                            # 等待一下让搜索结果出现，然后按回车确认
                            time.sleep(1)
                            country_input.send_keys("\n")  # 按回车键确认选择
                            print("已按回车确认选择")
                            time.sleep(1)

                            return True

                        except Exception as input_error:
                            print(f"输入Afghanistan失败: {input_error}")
                            return False
                    else:
                        print("❌ 激活搜索模式后未找到输入框")
                        return False

                except Exception as click_error:
                    print(f"点击选择器失败: {click_error}")
                    return False
            else:
                print("❌ 未找到国家选择器")
                return False

        except Exception as e:
            print(f"选择Afghanistan国家时出错: {e}")
            return False

    def check_registration_page_loading(self):
        """等待30秒后检测网站是否可访问（检测错误页面）"""
        try:
            print("🔍 等待30秒后检测网站可访问性...")

            # 等待30秒
            time.sleep(30)

            print("🔍 30秒检查点：检测网站是否可访问...")

            try:
                # 检查当前URL
                current_url = self.driver.current_url
                print(f"📋 当前页面URL: {current_url}")

                # 检查是否跳转到了错误页面或其他域名
                if "auth.bfl.ai" not in current_url:
                    print(f"❌ 页面已跳转到其他域名: {current_url}")
                    return False

                # 检查页面标题和内容，查找网站不可访问的错误信息
                page_source = self.driver.page_source.lower()
                page_title = self.driver.title.lower()

                print(f"📋 页面标题: {self.driver.title}")

                # 网站不可访问的错误指示器
                error_indicators = [
                    # Chrome错误页面
                    "this site can't be reached",
                    "无法访问此网站",
                    "err_name_not_resolved",
                    "err_connection_refused",
                    "err_connection_timed_out",
                    "err_network_changed",

                    # 代理相关错误
                    "err_proxy_connection_failed",
                    "err_proxy_auth_failed",
                    "err_proxy_certificate_invalid",
                    "no internet",
                    "proxy server",
                    "代理服务器",
                    "proxy connection failed",
                    "代理连接失败",
                    "something wrong with the proxy server",
                    "address is incorrect",
                    "contacting the system admin",
                    "checking the proxy address",
                    "running windows network diagnostics",

                    # 网站转移/关闭相关
                    "site has been moved",
                    "website has been moved",
                    "site has moved",
                    "网站已被转移",
                    "网站已迁移",
                    "域名已过期",
                    "domain expired",
                    "site not found",
                    "website not found",
                    "404 not found",

                    # 服务器错误
                    "500 internal server error",
                    "502 bad gateway",
                    "503 service unavailable",
                    "504 gateway timeout",
                    "服务器错误",
                    "服务不可用",

                    # 网络连接错误
                    "connection timed out",
                    "connection refused",
                    "network error",
                    "连接超时",
                    "连接被拒绝",
                    "网络错误",

                    # DNS错误
                    "dns_probe_finished_nxdomain",
                    "dns error",
                    "dns解析错误"
                ]

                # 检查页面内容是否包含错误信息
                for error in error_indicators:
                    if error in page_source or error in page_title:
                        print(f"❌ 检测到网站不可访问错误: {error}")
                        return False

                # 如果没有检测到错误，说明网站可以访问
                print("✅ 网站可正常访问，未检测到错误页面")
                return True

            except Exception as check_error:
                print(f"检查网站可访问性时出错: {check_error}")
                return False

        except Exception as e:
            print(f"检测网站可访问性时出错: {e}")
            return False

    def click_generate_button(self):
        """点击生成按钮（在此方法中也添加国家选择）"""
        try:
            # 先选择国家
            print("正在选择国家...")
            if self.select_country_afghanistan():
                print("✅ 已选择Afghanistan (AF)")
            else:
                print("⚠ 选择国家失败，继续生成...")

            print("正在查找生成按钮...")

            # 查找"Generate Now"按钮
            generate_button = None

            try:
                modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')

                # 查找包含"Generate Now"文本的按钮
                buttons = modal_container.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "Generate Now" in button.text:
                        generate_button = button
                        print("找到生成按钮")
                        break

                if not generate_button:
                    # 尝试通过XPath查找
                    generate_button = modal_container.find_element(By.XPATH, ".//button[contains(text(), 'Generate Now')]")
                    print("通过XPath找到生成按钮")

            except Exception as e:
                print(f"查找生成按钮失败: {e}")
                return False

            if generate_button:
                # 检查按钮是否可点击
                if generate_button.is_enabled() and generate_button.is_displayed():
                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", generate_button)
                    time.sleep(1)

                    # 点击生成按钮
                    try:
                        generate_button.click()
                        print("✅ 已点击生成按钮")
                        return True
                    except Exception as click_error:
                        print(f"普通点击失败: {click_error}")
                        try:
                            # 尝试JavaScript点击
                            self.driver.execute_script("arguments[0].click();", generate_button)
                            print("✅ 通过JavaScript点击生成按钮")
                            return True
                        except Exception as js_error:
                            print(f"JavaScript点击也失败: {js_error}")
                            return False
                else:
                    print("❌ 生成按钮不可点击或不可见")
                    return False
            else:
                print("❌ 未找到生成按钮")
                return False

        except Exception as e:
            print(f"点击生成按钮时出错: {e}")
            return False

    def select_newly_generated_proxy(self):
        """选择新生成的代理"""
        try:
            print("正在选择新生成的代理...")

            # 等待一下确保代理列表更新
            time.sleep(3)

            # 使用现有的代理选择方法
            if self.select_proxy_in_modal():
                print("✅ 新生成的代理选择成功")
                return True
            else:
                print("新方法失败，尝试原方法...")
                if self.select_first_proxy():
                    print("✅ 通过原方法成功选择新代理")
                    return True
                else:
                    print("❌ 代理选择失败")
                    return False

        except Exception as e:
            print(f"选择新生成的代理时出错: {e}")
            return False

    def switch_proxy_and_retry(self):
        """切换代理并重试（简化版，用于重试场景）"""
        try:
            print("正在切换代理...")

            # 切换回第一个标签页（代理标签）
            print("切换回代理标签页...")
            self.driver.switch_to.window(self.driver.window_handles[0])
            time.sleep(2)

            # 重新执行代理选择流程
            if self.select_proxy_first():
                print("✅ 代理重新选择完成，现在点击打开按钮...")
                # 代理选择完成后，再点击打开按钮
                if self.click_open_button():
                    print("✅ 已再次点击打开按钮，代理切换流程完成")

                    # 确保注册页面加载成功
                    print("等待注册页面加载...")
                    start_time = time.time()
                    loading_timeout = 30
                    check_interval = 2
                    page_loaded = False

                    while time.time() - start_time < loading_timeout:
                        try:
                            # 检查页面是否加载完成
                            ready_state = self.driver.execute_script("return document.readyState")
                            current_url = self.driver.current_url

                            print(f"页面状态: {ready_state}, URL: {current_url}")

                            # 检查是否有错误信息出现或打开了错误页面
                            page_source = self.driver.page_source
                            error_indicators = [
                                "Registration URL fetching failed",
                                "Failed to decrypt",
                                "ERR_",
                                "This site can't be reached",
                                "无法访问此网站",
                                "连接超时",
                                "网络错误"
                            ]

                            # 检查是否出现了错误信息或打开了开发者工具
                            has_error = any(error in page_source for error in error_indicators)
                            is_devtools = "devtools://" in current_url or "chrome://" in current_url
                            if has_error or is_devtools:
                                if is_devtools:
                                    print("❌ 检测到打开了开发者工具页面，关闭标签页并重新打开")
                                else:
                                    print("❌ 检测到页面加载错误，关闭标签页并重新打开")
                                # 关闭当前标签页并重新打开
                                if len(self.driver.window_handles) > 1:
                                    self.driver.close()
                                    print("已关闭错误的注册标签页")

                                    # 切换回代理标签页
                                    self.driver.switch_to.window(self.driver.window_handles[0])
                                    print("已切换回代理标签页")
                                    time.sleep(2)

                                    # 重新点击打开按钮
                                    print("正在重新打开注册标签页...")
                                    if self.click_open_button():
                                        print("✅ 成功重新打开注册标签页，继续等待页面加载...")
                                        # 重置计时器，重新开始等待
                                        start_time = time.time()
                                        continue
                                    else:
                                        print("❌ 重新打开注册标签页失败")
                                        return False
                                else:
                                    print("❌ 只有一个标签页，无法重新打开")
                                    return False

                            # 检查是否成功加载到注册页面
                            if ready_state == "complete" and "auth.bfl.ai/register" in current_url:
                                # 等待确保页面元素加载完成
                                time.sleep(3)

                                # 检查是否有表单元素
                                try:
                                    self.driver.find_element(By.CSS_SELECTOR, 'input[name="email"], input[type="email"]')
                                    print(f"✅ 注册页面加载完成，当前URL: {current_url}")
                                    page_loaded = True
                                    break
                                except:
                                    print("页面已加载但未找到邮箱输入框，继续等待...")

                            time.sleep(check_interval)

                        except Exception as check_error:
                            print(f"检查页面状态时出错: {check_error}")
                            time.sleep(check_interval)

                    if not page_loaded:
                        print(f"⚠ 页面加载超时 ({loading_timeout}秒)，代理切换可能失败")

                    # 页面加载成功后，自动获取邮箱并填写表单
                    if page_loaded:
                        print("✅ 代理切换成功，页面加载完成，开始自动获取邮箱并填写表单...")
                        # 等待一下确保页面完全稳定
                        time.sleep(2)
                        # 触发邮箱获取和表单填写
                        self.auto_fill_form_after_proxy_switch()

                    return page_loaded
                else:
                    print("❌ 再次点击打开按钮失败")
                    return False
            else:
                print("❌ 代理重新选择失败")
                return False

        except Exception as e:
            print(f"切换代理失败: {e}")
            return False

    def auto_fill_form_after_proxy_switch(self):
        """代理切换后自动获取邮箱并填写表单"""
        try:
            print("🔄 代理切换完成，开始自动获取邮箱并填写表单...")

            # 检查是否有可用的邮箱
            if os.path.exists(self.email_file):
                with open(self.email_file, 'r', encoding='utf-8') as f:
                    email_content = f.read().strip()

                # 检查邮箱格式
                import re
                email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                is_valid_email = re.match(email_regex, email_content) and '@' in email_content

                if is_valid_email:
                    print(f"📧 找到可用邮箱: {email_content}")
                    self.current_email = email_content

                    # 自动填写邮箱和完成表单
                    if self.fill_email(email_content):
                        print("✅ 邮箱填写完成，开始填写密码和提交表单...")
                        time.sleep(2)

                        # 完成表单填写
                        if self.complete_form_filling():
                            print("✅ 表单自动填写和提交完成！")
                            return True
                        else:
                            print("❌ 表单填写失败，但会继续监控新邮箱")
                            return False
                    else:
                        print("❌ 邮箱填写失败，但会继续监控新邮箱")
                        return False
                else:
                    print("📧 邮箱文件中没有有效的邮箱格式，等待新邮箱...")
                    return False
            else:
                print("📧 邮箱文件不存在，等待邮箱生成...")
                return False

        except Exception as e:
            print(f"自动填写表单时出错: {e}")
            return False

    def reload_page(self):
        """使用JavaScript重新加载当前页面（页面内刷新）"""
        try:
            print("正在重新加载页面...")
            # 使用JavaScript的location.reload()进行页面内刷新
            self.driver.execute_script("location.reload();")

            # 等待页面重新加载完成
            time.sleep(3)

            # 等待页面元素加载完成
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="email"], input[type="email"]'))
                )
                print("✅ 页面重新加载完成")
                return True
            except Exception as e:
                print(f"等待页面元素加载时出错: {e}")
                return False

        except Exception as e:
            print(f"重新加载页面时出错: {e}")
            return False

    def reopen_registration_tab(self):
        """关闭当前注册标签页并重新打开新的注册标签页"""
        try:
            print("正在关闭当前注册标签页...")

            # 如果有多个标签页，关闭当前注册标签页
            if len(self.driver.window_handles) > 1:
                self.driver.close()
                print("已关闭注册标签页")

                # 切换回代理标签页（第一个标签页）
                self.driver.switch_to.window(self.driver.window_handles[0])
                print("已切换回代理标签页")
                time.sleep(2)

                # 重新点击打开按钮来打开新的注册标签页
                print("正在重新打开注册标签页...")
                if self.click_open_button():
                    print("✅ 成功重新打开注册标签页")

                    # 等待新页面加载完成
                    print("等待新注册页面加载...")
                    time.sleep(3)

                    # 检查页面是否正确加载
                    try:
                        current_url = self.driver.current_url
                        if "auth.bfl.ai/register" in current_url:
                            # 等待邮箱输入框出现
                            self.wait.until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="email"], input[type="email"]'))
                            )
                            print(f"✅ 新注册页面加载完成，URL: {current_url}")
                            return True
                        else:
                            print(f"❌ 页面URL不正确: {current_url}")
                            return False
                    except Exception as e:
                        print(f"等待页面元素时出错: {e}")
                        return False
                else:
                    print("❌ 重新打开注册标签页失败")
                    return False
            else:
                print("只有一个标签页，无法关闭，使用页面刷新")
                return self.reload_page()

        except Exception as e:
            print(f"重新打开注册标签页时出错: {e}")
            return False

    def click_refresh_button(self):
        """点击刷新按钮来刷新代理列表"""
        try:
            print("正在查找刷新按钮...")

            # 查找刷新按钮的多种方式
            refresh_button = None

            # 方法1：通过SVG图标查找刷新按钮
            try:
                # 查找包含refresh图标的按钮
                refresh_buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button svg.arco-icon-refresh')
                if refresh_buttons:
                    # 获取包含SVG的按钮元素
                    refresh_button = refresh_buttons[0].find_element(By.XPATH, '..')
                    print("通过SVG图标找到刷新按钮")
            except Exception as e:
                print(f"通过SVG图标查找刷新按钮失败: {e}")

            # 方法2：通过按钮类名查找
            if not refresh_button:
                try:
                    refresh_selectors = [
                        'button.arco-btn.arco-btn-secondary.arco-btn-shape-square.arco-btn-size-small',
                        'button[class*="arco-btn-secondary"][class*="arco-btn-shape-square"]',
                        'button svg[class*="arco-icon-refresh"]',
                    ]

                    for selector in refresh_selectors:
                        try:
                            if 'svg' in selector:
                                # 如果是SVG选择器，需要找到父级按钮
                                svg_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                                refresh_button = svg_element.find_element(By.XPATH, '..')
                            else:
                                refresh_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                            print(f"通过选择器找到刷新按钮: {selector}")
                            break
                        except:
                            continue
                except Exception as e:
                    print(f"通过选择器查找刷新按钮失败: {e}")

            # 方法3：查找所有小按钮并检查是否包含刷新图标
            if not refresh_button:
                try:
                    small_buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button.arco-btn-size-small')
                    print(f"找到 {len(small_buttons)} 个小按钮")

                    for i, btn in enumerate(small_buttons):
                        try:
                            # 检查按钮内是否包含刷新图标
                            refresh_icons = btn.find_elements(By.CSS_SELECTOR, 'svg.arco-icon-refresh')
                            if refresh_icons:
                                refresh_button = btn
                                print(f"在第 {i+1} 个小按钮中找到刷新图标")
                                break
                        except:
                            continue
                except Exception as e:
                    print(f"查找小按钮失败: {e}")

            # 方法4：通过XPath查找包含refresh的SVG
            if not refresh_button:
                try:
                    xpath_refresh = "//button[.//svg[contains(@class, 'arco-icon-refresh')]]"
                    refresh_button = self.driver.find_element(By.XPATH, xpath_refresh)
                    print("通过XPath找到刷新按钮")
                except Exception as e:
                    print(f"通过XPath查找刷新按钮失败: {e}")

            if refresh_button:
                try:
                    # 检查按钮是否可见和可用
                    if refresh_button.is_displayed() and refresh_button.is_enabled():
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", refresh_button)
                        time.sleep(0.5)

                        # 点击刷新按钮
                        refresh_button.click()
                        print("✅ 已点击刷新按钮")
                        return True
                    else:
                        print("❌ 刷新按钮不可见或不可用")
                        return False
                except Exception as click_error:
                    print(f"点击刷新按钮失败: {click_error}")
                    try:
                        # 尝试JavaScript点击
                        self.driver.execute_script("arguments[0].click();", refresh_button)
                        print("✅ 已通过JavaScript点击刷新按钮")
                        return True
                    except Exception as js_error:
                        print(f"JavaScript点击刷新按钮也失败: {js_error}")
                        return False
            else:
                print("❌ 未找到刷新按钮")
                return False

        except Exception as e:
            print(f"点击刷新按钮时出错: {e}")
            return False

    def check_proxy_generation_success(self, max_wait_seconds=10):
        """检测代理生成成功弹窗"""
        try:
            print("正在检测代理生成成功弹窗...")

            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找成功提示弹窗
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, '.arco-message-success')

                    for element in success_elements:
                        try:
                            # 检查弹窗内容
                            element_text = element.text.strip()
                            print(f"检测到弹窗内容: {element_text}")

                            # 检查是否包含代理生成成功的关键词
                            success_keywords = [
                                "Proxy generated successfully",
                                "proxy generated successfully",
                                "generation successful",
                                "Generated successfully"
                            ]

                            if any(keyword in element_text for keyword in success_keywords):
                                print(f"✅ 检测到代理生成成功弹窗: {element_text}")
                                return True

                        except Exception as text_error:
                            print(f"读取弹窗文本失败: {text_error}")
                            continue

                    # 也检查其他可能的成功提示元素
                    other_success_selectors = [
                        '.arco-message.arco-message-success',
                        'div[class*="arco-message-success"]',
                        '.arco-message-content'
                    ]

                    for selector in other_success_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                element_text = element.text.strip()
                                if element_text and "Proxy generated successfully" in element_text:
                                    print(f"✅ 通过备用选择器检测到成功弹窗: {element_text}")
                                    return True
                        except:
                            continue

                except Exception as check_error:
                    print(f"检查弹窗时出错: {check_error}")

                time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未检测到代理生成成功弹窗")
            return False

        except Exception as e:
            print(f"检测代理生成成功弹窗时出错: {e}")
            return False

    def select_proxy_in_modal(self, max_wait_seconds=15):
        """在弹窗中选择代理的新方法，直接在select-proxy-modal中查找"""
        try:
            print("正在使用新方法查找弹窗中的选择代理按钮...")

            start_time = time.time()
            check_interval = 1

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找特定的代理选择弹窗容器
                    modal_container = None
                    try:
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                        print("✓ 找到代理选择弹窗容器 (.select-proxy-modal)")
                    except:
                        # 如果找不到特定容器，尝试通用的弹窗容器
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container')
                        print("✓ 找到通用弹窗容器 (.arco-modal-container)")

                    # 等待弹窗内容加载（很短的时间，因为您说立刻就能加载出来）
                    time.sleep(1)

                    # 多种方式在弹窗容器中查找选择代理按钮
                    select_button = None

                    # 方法1：在弹窗容器中使用具体路径查找
                    try:
                        # 在弹窗容器内查找表格第9列的选择代理按钮
                        specific_button = modal_container.find_element(By.XPATH, ".//table/tbody/tr/td[9]/span/span/button[contains(text(),'Select Proxy')]")
                        if specific_button.is_displayed() and specific_button.is_enabled():
                            select_button = specific_button
                            print("✓ 在弹窗容器中通过具体路径找到选择代理按钮")
                    except Exception:
                        print("弹窗容器中具体路径查找失败，尝试其他方法...")

                    # 方法2：在弹窗容器中查找第9列的所有按钮
                    if not select_button:
                        try:
                            ninth_column_buttons = modal_container.find_elements(By.XPATH, ".//table/tbody/tr/td[9]//button")
                            print(f"在弹窗容器的第9列找到 {len(ninth_column_buttons)} 个按钮")

                            for i, btn in enumerate(ninth_column_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  第9列按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查第9列按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找第9列按钮失败，尝试其他方法...")

                    # 方法3：在弹窗容器中查找所有"选择代理"按钮
                    if not select_button:
                        try:
                            all_select_buttons = modal_container.find_elements(By.XPATH, ".//button[contains(text(),'Select Proxy')]")
                            print(f"在弹窗容器中找到 {len(all_select_buttons)} 个选择代理按钮")

                            for i, btn in enumerate(all_select_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  选择代理按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查选择代理按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找所有选择代理按钮失败，尝试其他方法...")

                    # 方法4：在弹窗容器中查找表格中的所有按钮并过滤
                    if not select_button:
                        try:
                            table_buttons = modal_container.find_elements(By.XPATH, ".//table//button")
                            print(f"在弹窗容器的表格中找到 {len(table_buttons)} 个按钮")

                            for i, btn in enumerate(table_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  表格按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查表格按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找表格按钮失败，尝试其他方法...")

                    # 方法5：使用您提供的完整选择器路径
                    if not select_button:
                        try:
                            full_path_button = self.driver.find_element(By.CSS_SELECTOR, "body > div.arco-modal-container.md-modal.select-proxy-modal table tbody tr td:nth-child(9) button")
                            if full_path_button.is_displayed() and full_path_button.is_enabled() and "Select Proxy" in full_path_button.text:
                                select_button = full_path_button
                                print("✓ 通过完整CSS选择器路径找到选择代理按钮")
                        except Exception:
                            print("完整CSS选择器路径查找失败，尝试其他方法...")

                    if select_button:
                        try:
                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", select_button)
                            time.sleep(1)

                            # 点击按钮
                            select_button.click()
                            print("✅ 已点击选择代理按钮")

                            # 检测编辑成功提示
                            if self.check_edit_success_message():
                                print("✅ 检测到编辑成功提示")
                                return True
                            else:
                                print("❌ 未检测到编辑成功提示，继续等待...")
                                time.sleep(check_interval)
                                continue

                        except Exception as click_error:
                            print(f"点击按钮失败: {click_error}")
                            try:
                                # 尝试JavaScript点击
                                self.driver.execute_script("arguments[0].click();", select_button)
                                print("✅ 已通过JavaScript点击选择代理按钮")

                                if self.check_edit_success_message():
                                    print("✅ 检测到编辑成功提示")
                                    return True
                                else:
                                    print("❌ 未检测到编辑成功提示，继续等待...")
                                    time.sleep(check_interval)
                                    continue
                            except Exception as js_error:
                                print(f"JavaScript点击也失败: {js_error}")
                    else:
                        print("未找到选择代理按钮，继续等待...")
                        time.sleep(check_interval)

                except Exception as e:
                    print(f"查找代理选择弹窗容器时出错: {e}")
                    time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未能成功选择代理")
            return False

        except Exception as e:
            print(f"选择代理时出错: {e}")
            return False

    def select_first_proxy(self, max_wait_seconds=20):
        """选择弹窗表格中第一个代理（原方法，作为备用）"""
        try:
            print("正在使用原方法查找弹窗表格中的第一个选择代理按钮...")

            start_time = time.time()
            check_interval = 1  # 每1秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找代理选择弹窗容器
                    modal_container = None
                    try:
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                        print("✓ 找到代理选择弹窗容器")
                    except:
                        # 如果找不到特定容器，尝试通用的弹窗容器
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal')
                        print("✓ 找到通用弹窗容器")

                    # 等待弹窗内容加载完成（很短时间）
                    time.sleep(1)

                    # 在弹窗容器中查找选择代理按钮
                    select_buttons = []

                    # 方法1：在弹窗容器中使用具体路径查找
                    try:
                        specific_xpath_button = modal_container.find_element(By.XPATH, ".//table//tbody//tr//td[9]//button[contains(text(),'Select Proxy')]")
                        if specific_xpath_button.is_displayed() and specific_xpath_button.is_enabled():
                            select_buttons.append(specific_xpath_button)
                            print("✓ 在弹窗容器中通过具体XPath找到选择代理按钮")
                    except Exception:
                        print("弹窗容器中具体XPath查找失败，尝试其他方法...")

                    # 方法2：在弹窗容器中查找第9列的按钮
                    if not select_buttons:
                        try:
                            ninth_column_buttons = modal_container.find_elements(By.XPATH, ".//table//tbody//tr//td[9]//button")
                            print(f"在弹窗容器的第9列找到 {len(ninth_column_buttons)} 个按钮")

                            for i, btn in enumerate(ninth_column_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  第9列按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_buttons.append(btn)
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                except Exception as btn_error:
                                    print(f"  检查第9列按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception as e:
                            print(f"在弹窗容器中查找第9列按钮失败: {e}")

                    # 方法3：在弹窗容器中查找所有"选择代理"按钮
                    if not select_buttons:
                        try:
                            all_select_buttons = modal_container.find_elements(By.XPATH, ".//button[contains(text(),'Select Proxy')]")
                            print(f"在弹窗容器中找到 {len(all_select_buttons)} 个选择代理按钮")

                            for i, btn in enumerate(all_select_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  选择代理按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if btn_visible and btn_enabled:
                                        select_buttons.append(btn)
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                except Exception as btn_error:
                                    print(f"  检查选择代理按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception as e:
                            print(f"在弹窗容器中查找所有选择代理按钮失败: {e}")

                    if select_buttons:
                        print(f"总共找到 {len(select_buttons)} 个可用的选择代理按钮")
                        # 点击第一个选择代理按钮（最新生成的代理）
                        first_button = select_buttons[0]

                        try:
                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", first_button)
                            time.sleep(1)

                            # 尝试点击按钮
                            first_button.click()
                            print("✅ 已点击第一个选择代理按钮")

                            # 检测"编辑成功"提示
                            if self.check_edit_success_message():
                                print("✅ 检测到编辑成功提示")
                                return True
                            else:
                                print("❌ 未检测到编辑成功提示，继续等待...")
                                time.sleep(check_interval)
                                continue

                        except Exception as click_error:
                            print(f"点击按钮失败: {click_error}")
                            try:
                                # 尝试JavaScript点击
                                self.driver.execute_script("arguments[0].click();", first_button)
                                print("✅ 已通过JavaScript点击第一个选择代理按钮")

                                if self.check_edit_success_message():
                                    print("✅ 检测到编辑成功提示")
                                    return True
                                else:
                                    print("❌ 未检测到编辑成功提示，继续等待...")
                                    time.sleep(check_interval)
                                    continue
                            except Exception as js_error:
                                print(f"JavaScript点击也失败: {js_error}")
                    else:
                        print("未找到选择代理按钮，继续等待...")
                        time.sleep(check_interval)

                except Exception as e:
                    print(f"查找代理表格或按钮时出错: {e}")
                    time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未能成功选择代理")
            return False

        except Exception as e:
            print(f"选择第一个代理时出错: {e}")
            return False

    def wait_for_iframe_content_loaded(self, max_wait=15):
        """等待iframe内容加载完成"""
        try:
            print(f"等待iframe内容加载完成，最多等待{max_wait}秒...")

            for wait_attempt in range(max_wait):
                try:
                    # 检查是否有可见且有文本的按钮
                    all_buttons = self.driver.find_elements(By.TAG_NAME, 'button')
                    visible_text_buttons = []

                    for btn in all_buttons:
                        try:
                            if btn.is_displayed() and btn.text.strip():
                                visible_text_buttons.append(btn)
                                print(f"发现可见按钮: '{btn.text.strip()}'")
                        except:
                            continue

                    # 如果找到了可见的有文本的按钮，说明内容已加载
                    if visible_text_buttons:
                        print(f"✓ iframe内容加载完成，找到 {len(visible_text_buttons)} 个可见按钮")
                        return True

                    # 特别检查是否有"选择代理"按钮
                    select_proxy_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(),'Select Proxy')]")
                    if select_proxy_buttons:
                        for btn in select_proxy_buttons:
                            if btn.is_displayed():
                                print("✓ 找到可见的'选择代理'按钮，iframe内容已加载")
                                return True

                    print(f"等待iframe加载... ({wait_attempt + 1}/{max_wait})")
                    time.sleep(1)

                except Exception as e:
                    print(f"检查iframe加载状态时出错: {e}")
                    time.sleep(1)

            print(f"⚠ iframe内容加载超时（{max_wait}秒）")
            return False

        except Exception as e:
            print(f"等待iframe内容加载时出错: {e}")
            return False

    def debug_modal_structure(self):
        """调试弹窗结构，帮助定位问题"""
        try:
            print("🔍 开始调试弹窗结构...")

            # 查找弹窗
            modal = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal')
            print("✓ 找到弹窗")

            # 获取弹窗的HTML结构（部分）
            modal_html = modal.get_attribute('outerHTML')[:1000]  # 只取前1000个字符
            print(f"弹窗HTML结构（前1000字符）:\n{modal_html}")

            # 查找iframe
            iframes = modal.find_elements(By.TAG_NAME, 'iframe')
            print(f"弹窗中找到 {len(iframes)} 个iframe")

            if iframes:
                print("🔍 检查iframe内部内容...")
                iframe = iframes[0]
                try:
                    # 切换到iframe
                    self.driver.switch_to.frame(iframe)
                    print("✓ 已切换到iframe内部")

                    # 等待iframe内容加载完成
                    print("等待iframe内容加载...")
                    iframe_loaded = self.wait_for_iframe_content_loaded(max_wait=10)

                    if not iframe_loaded:
                        print("⚠ iframe内容加载超时，但继续检查...")
                    else:
                        print("✓ iframe内容加载完成")

                    # 查找iframe内的表格
                    iframe_tables = self.driver.find_elements(By.TAG_NAME, 'table')
                    print(f"iframe中找到 {len(iframe_tables)} 个表格")

                    # 查找iframe内的所有按钮
                    iframe_buttons = self.driver.find_elements(By.TAG_NAME, 'button')
                    print(f"iframe中找到 {len(iframe_buttons)} 个按钮:")

                    for i, btn in enumerate(iframe_buttons):
                        try:
                            btn_text = btn.text.strip()
                            btn_class = btn.get_attribute('class')
                            btn_visible = btn.is_displayed()
                            btn_enabled = btn.is_enabled()
                            btn_location = btn.location
                            print(f"  iframe按钮 {i+1}:")
                            print(f"    文本: '{btn_text}'")
                            print(f"    类名: '{btn_class}'")
                            print(f"    可见: {btn_visible}")
                            print(f"    可用: {btn_enabled}")
                            print(f"    位置: {btn_location}")
                            print(f"    包含'选择代理': {'Select Proxy' in btn_text}")
                            print("    ---")
                        except Exception as btn_error:
                            print(f"  iframe按钮 {i+1}: 获取信息失败 - {btn_error}")

                    # 查找iframe内包含代理相关文本的元素
                    iframe_proxy_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '代理') or contains(text(), '选择')]")
                    print(f"iframe中包含'代理'或'选择'文本的元素: {len(iframe_proxy_elements)} 个")

                    for i, elem in enumerate(iframe_proxy_elements[:10]):  # 只显示前10个
                        try:
                            elem_text = elem.text.strip()
                            elem_tag = elem.tag_name
                            elem_visible = elem.is_displayed()
                            print(f"  iframe元素 {i+1}: <{elem_tag}> '{elem_text}' (可见: {elem_visible})")
                        except:
                            continue

                    # 切换回主页面
                    self.driver.switch_to.default_content()
                    print("✓ 已切换回主页面")

                except Exception as iframe_error:
                    print(f"检查iframe内容时出错: {iframe_error}")
                    # 确保切换回主页面
                    try:
                        self.driver.switch_to.default_content()
                    except:
                        pass
            else:
                # 如果没有iframe，检查弹窗本身的内容
                # 查找所有表格
                tables = modal.find_elements(By.TAG_NAME, 'table')
                print(f"弹窗中找到 {len(tables)} 个表格")

                # 查找所有按钮并显示详细信息
                all_buttons = modal.find_elements(By.TAG_NAME, 'button')
                print(f"弹窗中找到 {len(all_buttons)} 个按钮:")

                for i, btn in enumerate(all_buttons):
                    try:
                        btn_text = btn.text.strip()
                        btn_class = btn.get_attribute('class')
                        btn_visible = btn.is_displayed()
                        btn_enabled = btn.is_enabled()
                        btn_location = btn.location
                        print(f"  按钮 {i+1}:")
                        print(f"    文本: '{btn_text}'")
                        print(f"    类名: '{btn_class}'")
                        print(f"    可见: {btn_visible}")
                        print(f"    可用: {btn_enabled}")
                        print(f"    位置: {btn_location}")
                        print(f"    包含'选择代理': {'Select Proxy' in btn_text}")
                        print("    ---")
                    except Exception as btn_error:
                        print(f"  按钮 {i+1}: 获取信息失败 - {btn_error}")

                # 查找特定的代理相关元素
                proxy_elements = modal.find_elements(By.XPATH, "//*[contains(text(), '代理') or contains(text(), '选择')]")
                print(f"包含'代理'或'选择'文本的元素: {len(proxy_elements)} 个")

                for i, elem in enumerate(proxy_elements):
                    try:
                        elem_text = elem.text.strip()
                        elem_tag = elem.tag_name
                        elem_visible = elem.is_displayed()
                        print(f"  元素 {i+1}: <{elem_tag}> '{elem_text}' (可见: {elem_visible})")
                    except:
                        continue

        except Exception as e:
            print(f"调试弹窗结构时出错: {e}")

    def check_edit_success_message(self, max_wait_seconds=5):
        """检测编辑成功提示消息"""
        try:
            print("正在检测编辑成功提示...")

            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找编辑成功提示
                    success_selectors = [
                        '.arco-message-success',
                        'div[class*="arco-message-success"]',
                        '.arco-message-content'
                    ]

                    for selector in success_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                element_text = element.text.strip()
                                if element_text and "Edit Success" in element_text:
                                    print(f"✅ 检测到编辑成功提示: {element_text}")
                                    return True
                        except:
                            continue

                    # 也尝试通过XPath查找
                    try:
                        success_message = self.driver.find_element(By.XPATH, "//span[contains(text(), 'Edit Success')]")
                        if success_message:
                            print(f"✅ 通过XPath检测到编辑成功提示: {success_message.text}")
                            return True
                    except:
                        pass

                except Exception as check_error:
                    print(f"检查编辑成功提示时出错: {check_error}")

                time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未检测到编辑成功提示")
            return False

        except Exception as e:
            print(f"检测编辑成功提示时出错: {e}")
            return False

    def request_new_email(self):
        """请求新的邮箱地址"""
        try:
            with open(self.request_file, 'w', encoding='utf-8') as f:
                f.write("request_new_email")
            print("已发送新邮箱请求")
        except Exception as e:
            print(f"发送新邮箱请求失败: {e}")

    def monitor_email_file(self):
        """监控邮箱文件中的邮箱变化"""
        print("开始监控邮箱文件变化...")
        self.monitoring_email_file = True

        while self.monitoring_email_file:
            try:
                # 读取邮箱文件内容
                if os.path.exists(self.email_file):
                    with open(self.email_file, 'r', encoding='utf-8') as f:
                        email_content = f.read().strip()

                    # 检查是否是新的内容
                    if email_content != self.last_email_content and email_content:
                        self.last_email_content = email_content

                        # 检查是否是邮箱格式
                        import re
                        email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                        is_valid_email = re.match(email_regex, email_content) and '@' in email_content

                        if is_valid_email:
                            # 检查是否是新的邮箱
                            if email_content != self.current_email:
                                print(f"检测到新的邮箱地址: {email_content}")

                                # 如果已经有邮箱，说明这是新的邮箱，需要重新加载页面
                                if self.current_email:
                                    print("检测到新邮箱，重新加载页面开始新的注册...")
                                    self.reload_page()
                                    # 重置表单填写状态
                                    if hasattr(self, '_form_filled'):
                                        delattr(self, '_form_filled')

                                self.current_email = email_content
                                # 自动填写新邮箱
                                if self.fill_email(email_content):
                                    print("邮箱填写完成，准备填写密码...")
                                    # 立即填写密码和提交表单
                                    time.sleep(2)  # 增加等待时间确保邮箱填写完成
                                    try:
                                        form_result = self.complete_form_filling()
                                        if not form_result:
                                            print("表单填写失败，但会继续监控新邮箱...")
                                    except Exception as e:
                                        print(f"完成表单填写时出错: {e}")
                                else:
                                    print("邮箱填写失败，但会继续监控新邮箱...")

                time.sleep(2)  # 每2秒检查一次文件

            except Exception as e:
                print(f"监控邮箱文件时出错: {e}")
                time.sleep(2)

    def start_email_file_monitoring(self):
        """在后台线程中启动邮箱文件监控"""
        email_thread = threading.Thread(target=self.monitor_email_file, daemon=True)
        email_thread.start()
        print("邮箱文件监控已启动")

    def stop_email_file_monitoring(self):
        """停止邮箱文件监控"""
        self.monitoring_email_file = False
        print("邮箱文件监控已停止")

    def monitor_proxy_switch_requests(self):
        """监控来自email_automation.py的代理切换请求"""
        print("开始监控代理切换请求...")
        self.monitoring_proxy_switch = True

        while self.monitoring_proxy_switch:
            try:
                # 检查代理切换请求文件
                if os.path.exists(self.switch_proxy_file):
                    print("🔄 收到代理切换请求，开始执行切换流程...")

                    # 删除请求文件
                    try:
                        os.remove(self.switch_proxy_file)
                        print("已删除代理切换请求文件")
                    except Exception as e:
                        print(f"删除请求文件失败: {e}")

                    # 执行代理切换流程
                    if self.handle_proxy_switch_request():
                        print("✅ 代理切换流程执行成功")
                    else:
                        print("❌ 代理切换流程执行失败")

                time.sleep(2)  # 每2秒检查一次文件

            except Exception as e:
                print(f"监控代理切换请求时出错: {e}")
                time.sleep(2)

    def start_proxy_switch_monitoring(self):
        """在后台线程中启动代理切换请求监控"""
        import threading
        proxy_switch_thread = threading.Thread(target=self.monitor_proxy_switch_requests, daemon=True)
        proxy_switch_thread.start()
        print("代理切换请求监控已启动")

    def stop_proxy_switch_monitoring(self):
        """停止代理切换请求监控"""
        self.monitoring_proxy_switch = False
        print("代理切换请求监控已停止")

    def handle_proxy_switch_request(self):
        """处理代理切换请求"""
        try:
            print("🔄 开始处理代理切换请求：关闭注册页标签，重新选择代理...")

            # 1. 关闭注册页标签
            if self.close_registration_tab():
                print("✅ 注册页标签已关闭")

                # 2. 在代理页重新选择代理并生成
                if self.regenerate_proxy_and_reopen():
                    print("✅ 代理重新生成完成，注册页已重新打开")

                    # 3. 不再通知email_automation.py生成新邮件，因为email_automation.py会自动处理
                    print("✅ 代理切换完成，email_automation.py将自动生成新邮箱")

                    return True
                else:
                    print("❌ 代理重新生成失败")
                    return False
            else:
                print("❌ 关闭注册页标签失败")
                return False

        except Exception as e:
            print(f"处理代理切换请求时出错: {e}")
            return False

    def close_registration_tab(self):
        """关闭注册页标签"""
        try:
            print("正在关闭注册页标签...")

            # 检查是否有多个标签页
            if len(self.driver.window_handles) > 1:
                # 如果当前在注册页标签，直接关闭
                current_url = self.driver.current_url
                if "auth.bfl.ai" in current_url:
                    self.driver.close()
                    print("已关闭注册页标签")

                    # 切换回代理页标签（第一个标签）
                    self.driver.switch_to.window(self.driver.window_handles[0])
                    print("已切换回代理页标签")
                    time.sleep(2)
                    return True
                else:
                    # 如果当前不在注册页，需要找到注册页标签并关闭
                    for handle in self.driver.window_handles:
                        self.driver.switch_to.window(handle)
                        if "auth.bfl.ai" in self.driver.current_url:
                            self.driver.close()
                            print("已找到并关闭注册页标签")
                            break

                    # 切换回代理页标签
                    self.driver.switch_to.window(self.driver.window_handles[0])
                    print("已切换回代理页标签")
                    time.sleep(2)
                    return True
            else:
                print("只有一个标签页，无法关闭注册页标签")
                return False

        except Exception as e:
            print(f"关闭注册页标签时出错: {e}")
            return False

    def regenerate_proxy_and_reopen(self):
        """重新生成代理并重新打开注册页"""
        try:
            print("🔄 开始重新生成代理流程...")

            # 确保在代理页面
            current_url = self.driver.current_url
            if "dash.proxy302.com" not in current_url:
                print("不在代理页面，导航到代理页面...")
                self.driver.get("https://dash.proxy302.com/quick-access")
                time.sleep(3)

            # 重新执行代理选择和生成流程
            print("正在重新选择代理...")
            if self.select_proxy_first():
                print("✅ 代理重新选择完成，现在点击打开按钮...")
                # 代理选择完成后，再点击打开按钮
                if self.click_open_button():
                    print("✅ 注册页面已重新打开")
                    return True
                else:
                    print("❌ 重新打开注册页面失败")
                    return False
            else:
                print("❌ 重新选择代理失败")
                return False

        except Exception as e:
            print(f"重新生成代理时出错: {e}")
            return False

    def notify_reset_proxy_switch_count(self):
        """通知email_automation.py重置代理切换计数"""
        try:
            with open(self.reset_count_file, 'w', encoding='utf-8') as f:
                f.write("reset_refresh_count")
            print("已通知email_automation.py重置代理切换计数")
        except Exception as e:
            print(f"通知重置代理切换计数失败: {e}")

    def request_new_email_from_automation(self):
        """通知email_automation.py生成新邮件并复制到剪切板"""
        try:
            print("📧 正在通知email_automation.py生成新邮件...")

            # 创建请求新邮件的文件
            with open(self.request_file, 'w', encoding='utf-8') as f:
                f.write("request_new_email")
            print("✅ 已创建新邮件请求文件，通知email_automation.py生成新邮件")

            # 等待一段时间让email_automation.py处理请求
            print("⏳ 等待email_automation.py处理新邮件生成请求...")
            time.sleep(3)

            return True
        except Exception as e:
            print(f"通知email_automation.py生成新邮件失败: {e}")
            return False

    def simulate_human_mouse_movement(self):
        """模拟人类鼠标移动"""
        try:
            import random
            # 模拟随机鼠标移动
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                self.driver.execute_script(f"""
                    var event = new MouseEvent('mousemove', {{
                        clientX: {x},
                        clientY: {y},
                        bubbles: true
                    }});
                    document.dispatchEvent(event);
                """)
                time.sleep(random.uniform(0.1, 0.3))
        except Exception as e:
            print(f"模拟鼠标移动失败: {e}")

    def simulate_human_browsing_behavior(self):
        """模拟人类浏览行为"""
        try:
            import random
            print("正在模拟人类浏览行为...")

            # 随机滚动页面
            for _ in range(random.randint(2, 4)):
                scroll_amount = random.randint(100, 300)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")
                time.sleep(random.uniform(0.5, 1.5))

            # 回到顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(random.uniform(1, 2))

            # 模拟鼠标移动
            self.simulate_human_mouse_movement()

            # 随机停顿，模拟阅读
            time.sleep(random.uniform(2, 4))

        except Exception as e:
            print(f"模拟浏览行为失败: {e}")

    def simulate_human_typing(self, element, text):
        """模拟人类打字行为"""
        import random

        # 先清空输入框
        element.clear()
        time.sleep(random.uniform(0.3, 0.8))

        # 模拟人类打字 - 变化的速度和偶尔的停顿
        for i, char in enumerate(text):
            element.send_keys(char)

            # 模拟真实的打字节奏
            if char in [' ', '@', '.']:
                # 特殊字符稍微慢一点
                time.sleep(random.uniform(0.08, 0.15))
            elif i > 0 and i % random.randint(3, 7) == 0:
                # 偶尔的思考停顿
                time.sleep(random.uniform(0.2, 0.5))
            else:
                # 正常打字速度，有变化
                time.sleep(random.uniform(0.03, 0.12))

        # 打字完成后的短暂停顿
        time.sleep(random.uniform(0.5, 1.0))

    def fill_email(self, email):
        """填写邮箱地址"""
        try:
            import random
            print(f"正在填写邮箱: {email}")

            # 先模拟一些人类行为
            self.simulate_human_mouse_movement()
            time.sleep(random.uniform(1, 2))

            # 查找邮箱输入框
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="邮箱"]',
                'input[id*="email"]'
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    print(f"找到邮箱输入框: {selector}")
                    break
                except:
                    continue

            if not email_input:
                email_input = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="email"], input[type="email"]'))
                )

            # 滚动到输入框并模拟点击
            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", email_input)
            time.sleep(random.uniform(0.5, 1.0))

            # 模拟鼠标悬停
            self.driver.execute_script("arguments[0].focus();", email_input)
            time.sleep(random.uniform(0.2, 0.5))

            # 使用改进的人类打字模拟
            self.simulate_human_typing(email_input, email)
            print(f"已输入邮箱: {email}")

            # 模拟输入完成后的行为
            time.sleep(random.uniform(0.5, 1.5))
            return True

        except Exception as e:
            print(f"填写邮箱失败: {e}")
            return False

    def fill_passwords(self):
        """填写密码"""
        try:
            print("正在填写密码...")

            # 查找密码输入框
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[placeholder*="password"]',
                'input[placeholder*="密码"]',
                'input[id*="password"]'
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_inputs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if password_inputs:
                        # 如果有多个密码输入框，选择第一个（通常是密码框）
                        password_input = password_inputs[0]
                        print(f"找到密码输入框: {selector}")
                        break
                except Exception as e:
                    print(f"查找密码输入框失败 {selector}: {e}")
                    continue

            if not password_input:
                print("尝试等待密码输入框出现...")
                try:
                    password_input = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="password"], input[type="password"]'))
                    )
                    print("通过等待找到密码输入框")
                except TimeoutException:
                    print("等待密码输入框超时")
                    return False

            # 使用改进的人类打字模拟
            self.simulate_human_typing(password_input, self.password)
            print("已输入密码")

            # 查找确认密码输入框（如果存在）
            try:
                # 尝试多种方式查找确认密码输入框
                confirm_selectors = [
                    'input[name*="confirm"]',
                    'input[name*="repeat"]',
                    'input[name="confirmPassword"]',
                    'input[name="confirm_password"]'
                ]

                confirm_password_input = None
                for selector in confirm_selectors:
                    try:
                        confirm_password_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                        print(f"找到确认密码输入框: {selector}")
                        break
                    except:
                        continue

                # 如果没找到，尝试查找第二个密码输入框
                if not confirm_password_input:
                    password_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
                    if len(password_inputs) > 1:
                        confirm_password_input = password_inputs[1]  # 第二个密码输入框
                        print("找到第二个密码输入框作为确认密码")

                if confirm_password_input:
                    self.simulate_human_typing(confirm_password_input, self.confirm_password)
                    print("已输入确认密码")
                else:
                    print("未找到确认密码输入框，跳过")
            except Exception as e:
                print(f"处理确认密码输入框时出错: {e}")

            return True

        except Exception as e:
            print(f"填写密码失败: {e}")
            return False

    def check_terms_checkbox(self):
        """勾选条款复选框"""
        if not self.auto_check_terms:
            return True

        try:
            print("正在查找条款复选框...")
            checkbox_found = False

            # 首先尝试查找自定义复选框按钮 (id="terms")
            try:
                terms_button = self.driver.find_element(By.CSS_SELECTOR, 'button[id="terms"]')
                aria_checked = terms_button.get_attribute('aria-checked')

                if aria_checked == 'false':
                    # 滚动到复选框位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", terms_button)
                    time.sleep(0.5)

                    # 点击自定义复选框按钮
                    terms_button.click()
                    print("已勾选条款复选框 (自定义按钮)")
                    checkbox_found = True
                    time.sleep(1)

                    # 验证是否成功勾选
                    new_aria_checked = terms_button.get_attribute('aria-checked')
                    if new_aria_checked == 'true':
                        print("✓ 复选框勾选成功")
                    else:
                        print("⚠ 复选框可能未成功勾选")
                else:
                    print("条款复选框已经勾选")
                    checkbox_found = True

            except Exception as e:
                print(f"查找自定义复选框失败: {e}")

            # 如果自定义复选框失败，尝试其他方法
            if not checkbox_found:
                # 尝试查找其他复选框元素
                checkbox_selectors = [
                    'button[role="checkbox"]',
                    'input[type="checkbox"]',
                    'button[id*="terms"]',
                    'input[id*="terms"]',
                    'button[aria-checked]'
                ]

                for selector in checkbox_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in elements:
                            # 检查是否未勾选
                            is_checked = False
                            if element.tag_name == 'button':
                                is_checked = element.get_attribute('aria-checked') == 'true'
                            else:
                                is_checked = element.is_selected()

                            if not is_checked:
                                try:
                                    element.click()
                                    print(f"已勾选复选框: {selector}")
                                    checkbox_found = True
                                    break
                                except:
                                    try:
                                        self.driver.execute_script("arguments[0].click();", element)
                                        print(f"已勾选复选框(JS): {selector}")
                                        checkbox_found = True
                                        break
                                    except:
                                        continue
                        if checkbox_found:
                            break
                    except:
                        continue

            if not checkbox_found:
                print("未找到可操作的条款复选框")

            return checkbox_found

        except Exception as e:
            print(f"处理复选框时出错: {e}")
            return False

    def check_success_alert(self):
        """检测是否有成功提示框弹出"""
        try:
            print("正在检测成功提示框...")

            # 等待最多5秒检测成功提示框
            success_selectors = [
                'div[role="alert"]',
                '.alert',
                '.success',
                '.notification',
                'div[class*="alert"]',
                'div[class*="success"]',
                'div[class*="notification"]'
            ]

            for selector in success_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        element_text = element.text.strip()
                        # 检查是否包含成功注册的关键词
                        success_keywords = [
                            "Registration email sent",
                            "please check your inbox",
                            "confirm your email",
                            "注册邮件已发送",
                            "请检查您的邮箱",
                            "确认您的邮箱"
                        ]

                        if any(keyword.lower() in element_text.lower() for keyword in success_keywords):
                            print(f"✅ 检测到成功提示框: {element_text}")
                            return True

                except Exception as e:
                    continue

            print("❌ 未检测到成功提示框")
            return False

        except Exception as e:
            print(f"检测成功提示框时出错: {e}")
            return False

    def wait_for_registration_completion(self, max_wait_seconds=30):
        """监控网络请求，等待注册完成"""
        try:
            print(f"开始监控注册请求，最多等待{max_wait_seconds}秒...")

            # 重置JavaScript状态变量
            self.driver.execute_script("""
                window.registrationStatus = null;
                window.registrationError = null;
                window.registrationUrl = null;
            """)

            # 注入JavaScript代码来监控网络请求（如果还没有注入）
            self.driver.execute_script("""
                if (!window.registrationMonitorInjected) {
                    window.registrationMonitorInjected = true;

                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    return originalFetch.apply(this, args).then(response => {
                        if (args[0].includes('api.bfl.ai/auth/register')) {
                            window.registrationUrl = args[0];
                            window.registrationStatus = response.status;
                            if (!response.ok) {
                                response.clone().text().then(text => {
                                    window.registrationError = text;
                                });
                            }
                        }
                        return response;
                    });
                };

                // 拦截XMLHttpRequest
                const originalXHR = window.XMLHttpRequest;
                window.XMLHttpRequest = function() {
                    const xhr = new originalXHR();
                    const originalOpen = xhr.open;
                    const originalSend = xhr.send;

                    xhr.open = function(method, url, ...args) {
                        this._url = url;
                        this._method = method;
                        return originalOpen.apply(this, [method, url, ...args]);
                    };

                    xhr.send = function(...args) {
                        this.addEventListener('readystatechange', function() {
                            if (this.readyState === 4 && this._url && this._url.includes('api.bfl.ai/auth/register')) {
                                window.registrationUrl = this._url;
                                window.registrationStatus = this.status;
                                if (this.status !== 200) {
                                    window.registrationError = this.responseText;
                                }
                            }
                        });
                        return originalSend.apply(this, args);
                    };

                    return xhr;
                };
                }
            """)

            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 检查JavaScript中的注册状态
                    status = self.driver.execute_script("return window.registrationStatus;")
                    error = self.driver.execute_script("return window.registrationError;")
                    url = self.driver.execute_script("return window.registrationUrl;")

                    if status is not None:
                        print(f"Request URL: {url}")
                        print(f"Request Method: POST")
                        print(f"Status Code: {status}")

                        if status == 200:
                            print("✅ 注册请求成功 (200)！")
                            # 再检查一下成功提示框确认
                            time.sleep(2)  # 等待页面更新
                            if self.check_success_alert():
                                print("✅ 同时检测到成功提示框，确认注册成功！")
                            return True
                        else:
                            print(f"❌ 注册请求失败")
                            if error:
                                print(f"错误详情: {error}")
                            # 检查页面错误信息
                            self.get_page_error_info()
                            return False

                except Exception as e:
                    print(f"检查注册状态时出错: {e}")

                time.sleep(check_interval)

            print(f"⚠ 等待{max_wait_seconds}秒后超时，未检测到注册请求")
            # 超时后检查是否有成功提示框
            if self.check_success_alert():
                print("✅ 虽然未检测到请求，但发现成功提示框！")
                return True

            print("❌ 注册请求监控超时")
            return False

        except Exception as e:
            print(f"监控注册请求时出错: {e}")
            return False

    def get_page_error_info(self):
        """获取页面错误信息"""
        try:
            # 检查页面上是否有错误信息显示
            error_selectors = [
                '.error',
                '.alert-error',
                '.text-red',
                '[class*="error"]',
                '[class*="danger"]',
                'div[role="alert"]'
            ]

            for selector in error_selectors:
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in error_elements:
                        error_text = element.text.strip()
                        if error_text and len(error_text) > 0:
                            print(f"页面错误信息: {error_text}")
                except:
                    continue

        except Exception as e:
            print(f"获取页面错误信息时出错: {e}")

    def submit_form(self):
        """提交表单"""
        if not self.auto_submit:
            print("请手动点击注册按钮完成注册")
            return True

        try:
            print("正在查找提交按钮...")

            # 等待提交按钮变为可用状态
            submit_button = None
            max_wait = 10  # 最多等待10秒

            for attempt in range(max_wait):
                # 查找提交按钮
                selectors = [
                    'button[type="submit"]:not([disabled])',  # 优先查找未禁用的提交按钮
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button'
                ]

                for selector in selectors:
                    try:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            button_text = button.text.lower() if button.text else ""
                            is_disabled = button.get_attribute('disabled')

                            if any(keyword in button_text for keyword in ["注册", "register", "sign up", "submit"]):
                                if not is_disabled:  # 按钮未禁用
                                    submit_button = button
                                    print(f"找到可用的提交按钮: {button_text}")
                                    break
                                else:
                                    print(f"找到提交按钮但被禁用: {button_text}, 等待...")
                        if submit_button:
                            break
                    except:
                        continue

                if submit_button:
                    break

                time.sleep(1)  # 等待1秒后重试

            if submit_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                time.sleep(1)

                # 尝试点击按钮（先普通点击，失败则用JS点击）
                click_success = False
                try:
                    submit_button.click()
                    print("已自动提交表单")
                    click_success = True
                except Exception as click_error:
                    print(f"普通点击失败: {click_error}")
                    try:
                        self.driver.execute_script("arguments[0].click();", submit_button)
                        print("已自动提交表单(JS)")
                        click_success = True
                    except Exception as js_error:
                        print(f"JavaScript点击也失败: {js_error}")
                        return False

                if click_success:
                    # 使用新的网络请求监控方法，等待注册完成
                    result = self.wait_for_registration_completion(max_wait_seconds=30)

                    # 如果注册失败，尝试重新提交一次
                    if not result:
                        print("🔄 注册失败，尝试重新提交...")
                        time.sleep(2)  # 等待2秒后重试

                        # 重新点击注册按钮
                        try:
                            submit_button.click()
                            print("已重新提交表单")
                        except:
                            try:
                                self.driver.execute_script("arguments[0].click();", submit_button)
                                print("已重新提交表单(JS)")
                            except Exception as retry_error:
                                print(f"重试点击失败: {retry_error}")
                                return False

                        # 再次监控注册请求
                        result = self.wait_for_registration_completion(max_wait_seconds=30)
                        if result:
                            print("✅ 重试后注册成功！")
                        else:
                            print("❌ 重试后仍然失败，自动切换代理...")
                            # 关闭注册标签页，切换代理
                            if len(self.driver.window_handles) > 1:
                                self.driver.close()
                                self.driver.switch_to.window(self.driver.window_handles[0])
                                # 切换代理并自动继续
                                if self.switch_proxy_and_retry():
                                    print("✅ 代理切换成功，将自动继续处理")
                                    result = True  # 设置为成功，因为代理切换后会自动处理
                                else:
                                    print("❌ 代理切换失败")
                                    result = False

                    return result
            else:
                print("未找到可用的提交按钮，请手动点击注册按钮")
                return False

        except Exception as e:
            print(f"提交表单时出错: {e}")
            return False

    def complete_form_filling(self):
        """完成表单填写（密码、复选框、提交）"""
        try:
            print("开始完成表单填写...")

            # 填写密码
            print("正在填写密码...")
            if self.fill_passwords():
                print("密码填写完成")
                time.sleep(1)

                # 勾选条款
                print("正在勾选条款...")
                self.check_terms_checkbox()
                time.sleep(2)

                # 提交表单
                print("正在提交表单...")
                if self.submit_form():
                    print("✅ 表单提交完成！等待下一个邮箱...")
                    self._form_filled = True
                    return True
                else:
                    print("❌ 表单提交失败，将自动重试或切换代理")
                    return False
            else:
                print("❌ 密码填写失败，将自动重试")
                return False

        except Exception as e:
            print(f"完成表单填写时出错: {e}")
            return False

    def run(self):
        """运行主流程"""
        print("=" * 60)
        print("🚀 open_register_new.py 启动")
        print(f"📍 进程ID: {os.getpid()}")
        print(f"📂 工作目录: {os.getcwd()}")
        print(f"🕒 启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        try:
            # 1. 设置浏览器
            print("🔧 开始设置浏览器...")
            if not self.setup_browser():
                print("❌ 浏览器设置失败")
                return False
            print("✅ 浏览器设置成功")

            # 2. 打开代理页面并点击打开按钮
            if self.open_proxy_page():
                print("✅ 注册页面打开成功")
                print("💡 脚本任务完成，浏览器将保持打开状态")
                print("🔔 如果遇到429错误，请手动关闭注册标签页")

                # 3. 启动代理切换请求监控
                self.start_proxy_switch_monitoring()
                print("📡 代理切换请求监控已启动")
                print("🔄 当email_automation.py刷新20次后，将自动切换代理")
            else:
                print("❌ 代理页面操作失败")
                return False

            # 保持脚本运行，等待用户操作
            try:
                print("脚本正在后台运行，按 Ctrl+C 可退出")
                print("📡 正在监控来自email_automation.py的代理切换请求...")
                while True:
                    time.sleep(10)  # 每10秒检查一次，保持脚本运行
            except KeyboardInterrupt:
                print("\n用户中断程序")

            # ========== 以下所有操作已注销 ==========
            # # 3. 导航到注册页面
            # if not self.navigate_to_register_page():
            #     return False

            # # 4. 启动邮箱文件监控
            # self.start_email_file_monitoring()

            # # 5. 等待用户确认并获取初始邮箱
            # print("=" * 60)
            # print("🔔 页面已打开，邮箱文件监控已启动")
            # print("📋 请启动email_automation.py脚本生成邮箱地址")
            # print("💡 脚本将自动检测邮箱文件中的邮箱变化并填写表单")
            # print(f"🔐 密码: {self.password}")
            # print(f"✅ 自动勾选条款: {'是' if self.auto_check_terms else '否'}")
            # print(f"🚀 自动提交表单: {'是' if self.auto_submit else '否'}")
            # print("=" * 60)

            # # 注释掉用户确认步骤，自动开始监控
            # # try:
            # #     input("👆 请确认页面正常加载后，按回车键开始监控...")
            # # except KeyboardInterrupt:
            # #     print("\n❌ 用户取消操作")
            # #     return False

            # print("\n🚀 自动开始监控邮箱文件变化...")
            # print("💡 请启动email_automation.py脚本，脚本将自动填写表单")

            # # 6. 保持运行，等待邮箱文件变化
            # try:
            #     print("监控已启动，等待邮箱文件变化...")
            #     while True:
            #         time.sleep(5)  # 主循环只需要保持运行状态

            # except KeyboardInterrupt:
            #     print("\n用户中断程序")

        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        finally:
            # 停止代理切换监控
            self.stop_proxy_switch_monitoring()

            # # 停止邮箱文件监控（已注销）
            # self.stop_email_file_monitoring()

            print("\n脚本结束，浏览器将保持打开状态")
            print("=" * 50)
            print("Open按钮点击完成！")
            print("浏览器将保持打开状态，您可以:")
            print("1. 手动操作注册页面")
            print("2. 查看代理是否正常工作")
            print("3. 进行其他测试")
            print("4. 关闭浏览器窗口退出")
            print("=" * 50)

            # 注释掉用户确认步骤，保持浏览器打开
            # try:
            #     input("按回车键关闭浏览器...")
            # except KeyboardInterrupt:
            #     print("\n用户中断，浏览器将保持打开")

            print("浏览器将保持打开状态，程序继续运行...")

def main():
    """主函数"""
    automation = RegistrationAutomation()
    automation.run()

if __name__ == "__main__":
    main()
