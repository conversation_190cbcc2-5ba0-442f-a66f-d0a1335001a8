2025-07-31 07:07:53,028 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:07:54,147 - INFO - 浏览器启动成功
2025-07-31 07:07:55,230 - INFO - 正在加载 tempmail.la...
2025-07-31 07:07:55,361 - INFO - tempmail.la 页面加载完成
2025-07-31 07:07:55,457 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:07:56,579 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:07:56,579 - INFO - 开始监控新邮箱请求...
2025-07-31 07:07:56,579 - INFO - 开始监控注册状态...
2025-07-31 07:07:56,579 - INFO - 启动时换一次新邮箱...
2025-07-31 07:07:56,579 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:07:56,687 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:07:56,688 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:07:56,719 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:07:56,719 - INFO - 正在获取邮箱地址...
2025-07-31 07:07:58,795 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:07:58,795 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:07:58,803 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:07:58,807 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:07:58,811 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:07:58,886 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:00,963 - INFO - 暂无邮件
2025-07-31 07:08:04,036 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:06,079 - INFO - 暂无邮件
2025-07-31 07:08:09,140 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:11,198 - INFO - 暂无邮件
2025-07-31 07:08:14,375 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:16,424 - INFO - 暂无邮件
2025-07-31 07:08:19,533 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:21,560 - INFO - 暂无邮件
2025-07-31 07:08:24,689 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:26,755 - INFO - 暂无邮件
2025-07-31 07:08:29,900 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:31,971 - INFO - 暂无邮件
2025-07-31 07:08:35,088 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:37,142 - INFO - 暂无邮件
2025-07-31 07:08:40,294 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:42,336 - INFO - 暂无邮件
2025-07-31 07:08:45,466 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:45,466 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:08:45,470 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:08:45,480 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:08:45,480 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:08:45,481 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:08:47,511 - INFO - 暂无邮件
2025-07-31 07:08:47,511 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:08:50,596 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:52,647 - INFO - 暂无邮件
2025-07-31 07:08:55,735 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:57,762 - INFO - 暂无邮件
2025-07-31 07:09:00,841 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:02,870 - INFO - 暂无邮件
2025-07-31 07:09:05,950 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:08,005 - INFO - 暂无邮件
2025-07-31 07:09:11,117 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:13,149 - INFO - 暂无邮件
2025-07-31 07:09:16,217 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:18,278 - INFO - 暂无邮件
2025-07-31 07:09:21,341 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:23,389 - INFO - 暂无邮件
2025-07-31 07:09:27,174 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:29,220 - INFO - 暂无邮件
2025-07-31 07:09:32,339 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:34,386 - INFO - 暂无邮件
2025-07-31 07:09:37,469 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:37,469 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:09:37,473 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:09:37,482 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:09:37,482 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:09:37,483 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:09:39,512 - INFO - 暂无邮件
2025-07-31 07:09:39,512 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:09:42,575 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:44,611 - INFO - 暂无邮件
2025-07-31 07:09:47,733 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:49,796 - INFO - 暂无邮件
2025-07-31 07:09:52,924 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:53,719 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:09:54,977 - INFO - 暂无邮件
2025-07-31 07:09:58,083 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:00,158 - INFO - 暂无邮件
2025-07-31 07:10:03,298 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:05,337 - INFO - 暂无邮件
2025-07-31 07:10:08,466 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:10,482 - INFO - 发现 1 封邮件
2025-07-31 07:10:10,482 - INFO - 收到邮件！当前总刷新次数: 26
2025-07-31 07:10:10,488 - INFO - 滚动到邮件位置
2025-07-31 07:10:11,511 - INFO - 通过JavaScript点击邮件
2025-07-31 07:10:11,511 - INFO - 点击邮件
2025-07-31 07:10:16,530 - INFO - 第1次尝试查找iframe...
2025-07-31 07:10:16,557 - INFO - 成功找到iframe
2025-07-31 07:10:16,567 - INFO - 开始提取验证码...
2025-07-31 07:10:16,567 - INFO - 方法2成功提取验证码: 191932
2025-07-31 07:10:16,567 - INFO - 提取到验证码: 191932
2025-07-31 07:10:16,569 - INFO - 验证码已保存到文件: 191932
2025-07-31 07:10:16,572 - INFO - 验证码已复制到剪贴板: 191932
2025-07-31 07:10:16,577 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:10:16,659 - INFO - 成功点击返回按钮
2025-07-31 07:10:18,671 - INFO - 结果已保存: <EMAIL> -> 191932
2025-07-31 07:10:18,672 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:10:18,672 - INFO - 所有计数器已重置为0
2025-07-31 07:10:18,672 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/10)
2025-07-31 07:10:18,673 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:10:18,673 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:10:20,773 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:10:48,813 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:11:06,245 - INFO - 用户中断程序
2025-07-31 07:11:06,250 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/c1083fed533c15ade3ba72949d9a9e83
2025-07-31 07:11:34,808 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:11:35,911 - INFO - 浏览器启动成功
2025-07-31 07:11:37,780 - INFO - 正在加载 tempmail.la...
2025-07-31 07:11:37,921 - INFO - tempmail.la 页面加载完成
2025-07-31 07:11:38,011 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:11:39,152 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:11:39,152 - INFO - 开始监控新邮箱请求...
2025-07-31 07:11:39,152 - INFO - 开始监控注册状态...
2025-07-31 07:11:39,152 - INFO - 启动时换一次新邮箱...
2025-07-31 07:11:39,153 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:11:39,153 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:11:39,154 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:11:39,222 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:11:39,223 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:11:39,236 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:11:39,236 - INFO - 正在获取邮箱地址...
2025-07-31 07:11:41,292 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:11:41,293 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:11:41,301 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:11:41,308 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:11:41,308 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:11:41,309 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:11:41,309 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:11:41,310 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:11:41,402 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:43,442 - INFO - 暂无邮件
2025-07-31 07:11:46,519 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:48,569 - INFO - 暂无邮件
2025-07-31 07:11:51,656 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:53,716 - INFO - 暂无邮件
2025-07-31 07:11:56,885 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:58,935 - INFO - 暂无邮件
2025-07-31 07:12:02,096 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:04,176 - INFO - 暂无邮件
2025-07-31 07:12:07,233 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:09,264 - INFO - 暂无邮件
2025-07-31 07:12:12,394 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:14,462 - INFO - 暂无邮件
2025-07-31 07:12:17,518 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:19,567 - INFO - 暂无邮件
2025-07-31 07:12:22,650 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:24,677 - INFO - 暂无邮件
2025-07-31 07:12:27,732 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:27,732 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:12:27,737 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:12:27,744 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:12:27,744 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:12:27,744 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:12:29,769 - INFO - 暂无邮件
2025-07-31 07:12:29,769 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:12:32,838 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:34,867 - INFO - 暂无邮件
2025-07-31 07:12:37,935 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:39,960 - INFO - 暂无邮件
2025-07-31 07:12:43,027 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:45,097 - INFO - 暂无邮件
2025-07-31 07:12:48,287 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:50,360 - INFO - 暂无邮件
2025-07-31 07:12:53,466 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:55,492 - INFO - 暂无邮件
2025-07-31 07:12:58,554 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:00,611 - INFO - 暂无邮件
2025-07-31 07:13:03,798 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:05,874 - INFO - 暂无邮件
2025-07-31 07:13:08,993 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:11,013 - INFO - 暂无邮件
2025-07-31 07:13:14,168 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:16,217 - INFO - 暂无邮件
2025-07-31 07:13:19,795 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:19,795 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:13:19,808 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:13:19,818 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:13:19,819 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:13:19,822 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:13:21,916 - INFO - 暂无邮件
2025-07-31 07:13:21,916 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:13:25,047 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:27,065 - INFO - 暂无邮件
2025-07-31 07:13:30,181 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:32,213 - INFO - 暂无邮件
2025-07-31 07:13:35,330 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:37,373 - INFO - 暂无邮件
2025-07-31 07:13:40,026 - INFO - 用户中断等待
2025-07-31 07:13:40,027 - WARNING - 第 1 个邮箱处理失败或被中断
2025-07-31 07:13:40,032 - INFO - 🎉 自动化完成！共成功处理 0 个邮箱，目标: 10 个
2025-07-31 07:14:17,743 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:14:18,840 - INFO - 浏览器启动成功
2025-07-31 07:14:28,064 - INFO - 正在加载 tempmail.la...
2025-07-31 07:14:28,076 - INFO - tempmail.la 页面加载完成
2025-07-31 07:14:28,150 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:14:29,195 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:14:29,195 - INFO - 开始监控新邮箱请求...
2025-07-31 07:14:29,195 - INFO - 开始监控注册状态...
2025-07-31 07:14:29,195 - INFO - 启动时换一次新邮箱...
2025-07-31 07:14:29,199 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:14:29,199 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:14:29,199 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:14:29,278 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:14:29,278 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:14:29,295 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:14:29,296 - INFO - 正在获取邮箱地址...
2025-07-31 07:14:31,355 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:14:31,359 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:14:31,372 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:14:31,380 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:14:31,380 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:14:31,381 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:14:31,382 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:14:31,382 - INFO - 📍 主循环开始 - 第 1/20 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:14:31,382 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:14:31,383 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:14:31,383 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:14:31,466 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:33,509 - INFO - 暂无邮件
2025-07-31 07:14:36,670 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:38,730 - INFO - 暂无邮件
2025-07-31 07:14:41,795 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:43,859 - INFO - 暂无邮件
2025-07-31 07:14:46,930 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:48,992 - INFO - 暂无邮件
2025-07-31 07:14:52,065 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:54,117 - INFO - 暂无邮件
2025-07-31 07:14:57,317 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:59,390 - INFO - 暂无邮件
2025-07-31 07:15:02,527 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:04,548 - INFO - 暂无邮件
2025-07-31 07:15:07,697 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:09,734 - INFO - 暂无邮件
2025-07-31 07:15:12,801 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:14,850 - INFO - 暂无邮件
2025-07-31 07:15:17,991 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:17,992 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:15:17,997 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:15:18,003 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:15:18,003 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:15:18,003 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:15:20,027 - INFO - 暂无邮件
2025-07-31 07:15:20,027 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:15:23,095 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:25,124 - INFO - 暂无邮件
2025-07-31 07:15:28,189 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:30,214 - INFO - 暂无邮件
2025-07-31 07:15:33,275 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:35,315 - INFO - 暂无邮件
2025-07-31 07:15:38,485 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:40,540 - INFO - 暂无邮件
2025-07-31 07:15:40,600 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:41,233 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:43,360 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:43,701 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:45,738 - INFO - 暂无邮件
2025-07-31 07:15:48,873 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:50,915 - INFO - 发现 1 封邮件
2025-07-31 07:15:50,918 - INFO - 收到邮件！当前总刷新次数: 16
2025-07-31 07:15:50,942 - INFO - 滚动到邮件位置
2025-07-31 07:15:51,970 - INFO - 通过JavaScript点击邮件
2025-07-31 07:15:51,970 - INFO - 点击邮件
2025-07-31 07:15:56,986 - INFO - 第1次尝试查找iframe...
2025-07-31 07:15:57,049 - INFO - 成功找到iframe
2025-07-31 07:15:57,079 - INFO - 开始提取验证码...
2025-07-31 07:15:57,081 - INFO - 方法2成功提取验证码: 415028
2025-07-31 07:15:57,083 - INFO - 提取到验证码: 415028
2025-07-31 07:15:57,087 - INFO - 验证码已保存到文件: 415028
2025-07-31 07:15:57,096 - INFO - 验证码已复制到剪贴板: 415028
2025-07-31 07:15:57,110 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:15:57,252 - INFO - 成功点击返回按钮
2025-07-31 07:15:59,257 - INFO - 结果已保存: <EMAIL> -> 415028
2025-07-31 07:15:59,257 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:15:59,260 - INFO - 所有计数器已重置为0
2025-07-31 07:15:59,261 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/20)
2025-07-31 07:15:59,262 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:15:59,263 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:16:29,382 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:16:59,530 - INFO - ⏳ 等待注册完成中... 已等待 60s / 300s
2025-07-31 07:17:11,792 - INFO - 用户中断程序
2025-07-31 07:17:11,792 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/995fb470f0e4528d5eff78c3678e736b
2025-07-31 07:17:18,532 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:17:19,614 - INFO - 浏览器启动成功
2025-07-31 07:17:21,148 - INFO - 正在加载 tempmail.la...
2025-07-31 07:17:21,279 - INFO - tempmail.la 页面加载完成
2025-07-31 07:17:21,381 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:17:23,478 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:17:23,478 - INFO - 开始监控新邮箱请求...
2025-07-31 07:17:23,479 - INFO - 开始监控注册状态...
2025-07-31 07:17:23,479 - INFO - 启动时换一次新邮箱...
2025-07-31 07:17:23,480 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:17:23,480 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:17:23,480 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:17:23,507 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:17:23,508 - INFO - 已清空验证码文件
2025-07-31 07:17:23,570 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:17:26,570 - INFO - 正在获取邮箱地址...
2025-07-31 07:17:28,634 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:17:28,635 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:28,637 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:17:28,649 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:17:28,650 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:17:28,651 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:28,652 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:17:28,652 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:17:28,652 - INFO - 正在获取邮箱地址...
2025-07-31 07:17:30,706 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:17:30,707 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:30,711 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:17:30,714 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:17:30,715 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:17:30,716 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:30,716 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:17:30,717 - INFO - 启动时已换新邮箱: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:17:30,782 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:32,818 - INFO - 暂无邮件
2025-07-31 07:17:36,211 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:38,272 - INFO - 暂无邮件
2025-07-31 07:17:41,423 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:43,477 - INFO - 暂无邮件
2025-07-31 07:17:46,541 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:48,566 - INFO - 暂无邮件
2025-07-31 07:17:51,757 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:53,814 - INFO - 暂无邮件
2025-07-31 07:17:56,897 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:58,983 - INFO - 暂无邮件
2025-07-31 07:18:02,210 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:04,296 - INFO - 暂无邮件
2025-07-31 07:18:07,438 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:09,488 - INFO - 暂无邮件
2025-07-31 07:18:12,572 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:14,606 - INFO - 暂无邮件
2025-07-31 07:18:17,759 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:17,759 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:18:17,765 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:18:17,774 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:18:17,774 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:18:17,775 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:18:19,804 - INFO - 暂无邮件
2025-07-31 07:18:19,805 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:18:22,985 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:25,036 - INFO - 暂无邮件
2025-07-31 07:18:28,105 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:30,140 - INFO - 暂无邮件
2025-07-31 07:18:33,204 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:35,231 - INFO - 暂无邮件
2025-07-31 07:18:38,292 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:40,324 - INFO - 暂无邮件
2025-07-31 07:18:43,427 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:45,469 - INFO - 暂无邮件
2025-07-31 07:18:48,614 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:50,661 - INFO - 暂无邮件
2025-07-31 07:18:53,810 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:55,881 - INFO - 暂无邮件
2025-07-31 07:18:57,993 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:18:59,088 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:01,158 - INFO - 暂无邮件
2025-07-31 07:19:04,306 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:06,325 - INFO - 暂无邮件
2025-07-31 07:19:09,392 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:09,392 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:19:09,397 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:09,404 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:09,404 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:09,404 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:11,425 - INFO - 发现 1 封邮件
2025-07-31 07:19:11,425 - INFO - 收到邮件！当前总刷新次数: 20
2025-07-31 07:19:11,443 - INFO - 滚动到邮件位置
2025-07-31 07:19:12,458 - INFO - 通过JavaScript点击邮件
2025-07-31 07:19:12,459 - INFO - 点击邮件
2025-07-31 07:19:17,471 - INFO - 第1次尝试查找iframe...
2025-07-31 07:19:17,495 - INFO - 成功找到iframe
2025-07-31 07:19:17,506 - INFO - 开始提取验证码...
2025-07-31 07:19:17,507 - INFO - 方法2成功提取验证码: 809440
2025-07-31 07:19:17,507 - INFO - 提取到验证码: 809440
2025-07-31 07:19:17,507 - INFO - 验证码已保存到文件: 809440
2025-07-31 07:19:17,513 - INFO - 验证码已复制到剪贴板: 809440
2025-07-31 07:19:17,519 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:19:17,628 - INFO - 成功点击返回按钮
2025-07-31 07:19:19,641 - INFO - 结果已保存: <EMAIL> -> 809440
2025-07-31 07:19:19,641 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:19:19,643 - INFO - 所有计数器已重置为0
2025-07-31 07:19:19,643 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/10)
2025-07-31 07:19:19,644 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:19:19,644 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:19:33,703 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:19:33,703 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:19:33,707 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:19:33,707 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:19:33,764 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:19:33,764 - INFO - 已清空验证码文件
2025-07-31 07:19:33,832 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:19:36,835 - INFO - 正在获取邮箱地址...
2025-07-31 07:19:38,930 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:19:38,930 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:38,936 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:38,941 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:38,941 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:19:38,942 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 正在获取邮箱地址...
2025-07-31 07:19:41,021 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:19:41,021 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:41,026 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:41,031 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:41,031 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:41,032 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:41,032 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:19:41,033 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:19:41,033 - INFO - 🔄 开始第 2/10 个邮箱的邮件等待...
2025-07-31 07:19:41,033 - INFO - 📍 主循环开始 - 第 2/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:19:41,033 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:19:41,034 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:19:41,034 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:19:41,105 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:43,188 - INFO - 暂无邮件
2025-07-31 07:19:46,256 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:48,300 - INFO - 暂无邮件
2025-07-31 07:19:51,431 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:53,487 - INFO - 暂无邮件
2025-07-31 07:19:57,192 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:59,234 - INFO - 暂无邮件
2025-07-31 07:20:02,392 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:04,465 - INFO - 暂无邮件
2025-07-31 07:20:07,550 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:09,614 - INFO - 暂无邮件
2025-07-31 07:20:09,963 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:20:12,732 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:14,757 - INFO - 暂无邮件
2025-07-31 07:20:17,869 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:19,875 - INFO - 发现 1 封邮件
2025-07-31 07:20:19,875 - INFO - 收到邮件！当前总刷新次数: 8
2025-07-31 07:20:19,884 - INFO - 滚动到邮件位置
2025-07-31 07:20:20,912 - INFO - 通过JavaScript点击邮件
2025-07-31 07:20:20,912 - INFO - 点击邮件
2025-07-31 07:20:25,942 - INFO - 第1次尝试查找iframe...
2025-07-31 07:20:25,992 - INFO - 成功找到iframe
2025-07-31 07:20:26,016 - INFO - 开始提取验证码...
2025-07-31 07:20:26,017 - INFO - 方法2成功提取验证码: 353030
2025-07-31 07:20:26,017 - INFO - 提取到验证码: 353030
2025-07-31 07:20:26,018 - INFO - 验证码已保存到文件: 353030
2025-07-31 07:20:26,024 - INFO - 验证码已复制到剪贴板: 353030
2025-07-31 07:20:26,030 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:20:26,136 - INFO - 成功点击返回按钮
2025-07-31 07:20:28,151 - INFO - 结果已保存: <EMAIL> -> 353030
2025-07-31 07:20:28,151 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:20:28,155 - INFO - 所有计数器已重置为0
2025-07-31 07:20:28,155 - INFO - 第 2 个邮箱完成，成功获取邮件和验证码 (2/10)
2025-07-31 07:20:28,159 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:20:28,159 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:20:40,217 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:20:40,217 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:20:40,219 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:20:40,219 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:20:40,303 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:20:40,303 - INFO - 已清空验证码文件
2025-07-31 07:20:40,352 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:20:43,364 - INFO - 正在获取邮箱地址...
2025-07-31 07:20:45,431 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:20:45,432 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:45,435 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:20:45,440 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:20:45,441 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:20:45,441 - INFO - 正在获取邮箱地址...
2025-07-31 07:20:47,540 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:20:47,540 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:47,546 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:20:47,550 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:20:47,550 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:20:47,551 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:47,551 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:20:47,551 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 开始第 3/10 个邮箱的邮件等待...
2025-07-31 07:20:47,552 - INFO - 📍 主循环开始 - 第 3/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:20:47,611 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:49,692 - INFO - 暂无邮件
2025-07-31 07:20:52,908 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:54,931 - INFO - 暂无邮件
2025-07-31 07:20:57,991 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:00,025 - INFO - 暂无邮件
2025-07-31 07:21:03,087 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:05,120 - INFO - 暂无邮件
2025-07-31 07:21:08,187 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:10,212 - INFO - 暂无邮件
2025-07-31 07:21:13,285 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:15,320 - INFO - 暂无邮件
2025-07-31 07:21:18,381 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:20,404 - INFO - 暂无邮件
2025-07-31 07:21:23,474 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:25,498 - INFO - 暂无邮件
2025-07-31 07:21:28,569 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:30,603 - INFO - 暂无邮件
2025-07-31 07:21:33,664 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:33,664 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:21:33,668 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:21:33,672 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:21:33,676 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:21:33,677 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:21:35,714 - INFO - 暂无邮件
2025-07-31 07:21:35,715 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:21:38,781 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:40,811 - INFO - 暂无邮件
2025-07-31 07:21:43,874 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:45,900 - INFO - 暂无邮件
2025-07-31 07:21:48,966 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:50,999 - INFO - 暂无邮件
2025-07-31 07:21:54,072 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:56,105 - INFO - 暂无邮件
2025-07-31 07:21:59,173 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:01,206 - INFO - 暂无邮件
2025-07-31 07:22:04,268 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:06,296 - INFO - 暂无邮件
2025-07-31 07:22:09,352 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:11,378 - INFO - 暂无邮件
2025-07-31 07:22:14,452 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:16,483 - INFO - 暂无邮件
2025-07-31 07:22:19,540 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:21,572 - INFO - 暂无邮件
2025-07-31 07:22:24,633 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:24,633 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:22:24,638 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:22:24,644 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:22:24,644 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:22:24,648 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:22:26,669 - INFO - 暂无邮件
2025-07-31 07:22:26,669 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:22:29,737 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:31,764 - INFO - 暂无邮件
2025-07-31 07:22:34,939 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:36,998 - INFO - 暂无邮件
2025-07-31 07:22:40,066 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:42,103 - INFO - 暂无邮件
2025-07-31 07:22:45,225 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:47,282 - INFO - 暂无邮件
2025-07-31 07:22:49,856 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:22:50,431 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:52,483 - INFO - 暂无邮件
2025-07-31 07:22:55,571 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:57,617 - INFO - 暂无邮件
2025-07-31 07:23:00,710 - INFO - 🔄 点击刷新邮件按钮 (总计第27次，代理切换计数第27次) - 当前邮箱: <EMAIL>
2025-07-31 07:23:02,746 - INFO - 发现 1 封邮件
2025-07-31 07:23:02,746 - INFO - 收到邮件！当前总刷新次数: 27
2025-07-31 07:23:02,758 - INFO - 滚动到邮件位置
2025-07-31 07:23:03,784 - INFO - 通过JavaScript点击邮件
2025-07-31 07:23:03,785 - INFO - 点击邮件
2025-07-31 07:23:08,804 - INFO - 第1次尝试查找iframe...
2025-07-31 07:23:08,865 - INFO - 成功找到iframe
2025-07-31 07:23:08,876 - INFO - 开始提取验证码...
2025-07-31 07:23:08,876 - INFO - 方法2成功提取验证码: 816257
2025-07-31 07:23:08,876 - INFO - 提取到验证码: 816257
2025-07-31 07:23:08,876 - INFO - 验证码已保存到文件: 816257
2025-07-31 07:23:08,885 - INFO - 验证码已复制到剪贴板: 816257
2025-07-31 07:23:08,892 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:23:08,984 - INFO - 成功点击返回按钮
2025-07-31 07:23:10,989 - INFO - 结果已保存: <EMAIL> -> 816257
2025-07-31 07:23:10,989 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:23:10,995 - INFO - 所有计数器已重置为0
2025-07-31 07:23:10,996 - INFO - 第 3 个邮箱完成，成功获取邮件和验证码 (3/10)
2025-07-31 07:23:10,997 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:23:10,998 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:23:15,876 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:23:41,150 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:24:11,296 - INFO - ⏳ 等待注册完成中... 已等待 60s / 300s
2025-07-31 07:24:41,416 - INFO - ⏳ 等待注册完成中... 已等待 90s / 300s
2025-07-31 07:25:11,555 - INFO - ⏳ 等待注册完成中... 已等待 121s / 300s
2025-07-31 07:25:41,675 - INFO - ⏳ 等待注册完成中... 已等待 151s / 300s
2025-07-31 07:26:11,808 - INFO - ⏳ 等待注册完成中... 已等待 181s / 300s
2025-07-31 07:26:41,919 - INFO - ⏳ 等待注册完成中... 已等待 211s / 300s
2025-07-31 07:28:07,744 - INFO - 用户中断程序
2025-07-31 07:28:07,747 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/1c6fe2ff73fd63dffe81f6d85afab6f4
2025-07-31 07:28:25,169 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:28:26,266 - INFO - 浏览器启动成功
2025-07-31 07:28:27,706 - INFO - 正在加载 tempmail.la...
2025-07-31 07:28:27,774 - INFO - tempmail.la 页面加载完成
2025-07-31 07:28:27,888 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:28:28,944 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:28:28,945 - INFO - 开始监控新邮箱请求...
2025-07-31 07:28:28,945 - INFO - 开始监控注册状态...
2025-07-31 07:28:28,945 - INFO - 启动时换一次新邮箱...
2025-07-31 07:28:28,946 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:28:28,946 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:28:28,946 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:28:29,092 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:28:29,092 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:28:29,144 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:28:29,144 - INFO - 正在获取邮箱地址...
2025-07-31 07:28:31,219 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:28:31,219 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:28:31,227 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:28:31,236 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:28:31,236 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:28:31,237 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:28:31,237 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:28:31,237 - INFO - 📍 主循环开始 - 第 1/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:28:31,238 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:28:31,238 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:28:31,239 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:28:31,364 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:33,454 - INFO - 暂无邮件
2025-07-31 07:28:36,525 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:38,573 - INFO - 暂无邮件
2025-07-31 07:28:41,705 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:43,774 - INFO - 暂无邮件
2025-07-31 07:28:46,892 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:48,965 - INFO - 暂无邮件
2025-07-31 07:28:52,099 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:54,144 - INFO - 暂无邮件
2025-07-31 07:28:57,259 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:59,315 - INFO - 暂无邮件
2025-07-31 07:29:02,474 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:04,556 - INFO - 暂无邮件
2025-07-31 07:29:07,660 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:09,705 - INFO - 暂无邮件
2025-07-31 07:29:12,771 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:14,796 - INFO - 暂无邮件
2025-07-31 07:29:17,864 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:17,864 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:29:17,870 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:29:17,875 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:29:17,876 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:29:17,876 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:29:19,904 - INFO - 暂无邮件
2025-07-31 07:29:19,904 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:29:22,975 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:25,008 - INFO - 暂无邮件
2025-07-31 07:29:28,076 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:30,107 - INFO - 暂无邮件
2025-07-31 07:29:33,174 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:35,194 - INFO - 暂无邮件
2025-07-31 07:29:38,263 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:40,297 - INFO - 暂无邮件
2025-07-31 07:29:43,375 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:45,395 - INFO - 暂无邮件
2025-07-31 07:29:48,459 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:50,482 - INFO - 暂无邮件
2025-07-31 07:29:53,549 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:55,578 - INFO - 暂无邮件
2025-07-31 07:29:58,643 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:00,674 - INFO - 暂无邮件
2025-07-31 07:30:03,745 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:05,774 - INFO - 暂无邮件
2025-07-31 07:30:09,451 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:09,451 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:30:09,455 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:30:09,459 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:30:09,459 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:30:09,459 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:30:11,490 - INFO - 暂无邮件
2025-07-31 07:30:11,491 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:30:14,561 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:16,592 - INFO - 暂无邮件
2025-07-31 07:30:19,651 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:21,678 - INFO - 暂无邮件
2025-07-31 07:30:24,740 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:26,770 - INFO - 暂无邮件
2025-07-31 07:30:29,829 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:31,864 - INFO - 暂无邮件
2025-07-31 07:30:34,922 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:36,953 - INFO - 暂无邮件
2025-07-31 07:30:40,021 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:42,041 - INFO - 暂无邮件
2025-07-31 07:30:45,108 - INFO - 🔄 点击刷新邮件按钮 (总计第27次，代理切换计数第27次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:47,137 - INFO - 暂无邮件
2025-07-31 07:30:50,207 - INFO - 🔄 点击刷新邮件按钮 (总计第28次，代理切换计数第28次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:52,240 - INFO - 暂无邮件
2025-07-31 07:30:55,297 - INFO - 🔄 点击刷新邮件按钮 (总计第29次，代理切换计数第29次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:57,327 - INFO - 暂无邮件
2025-07-31 07:31:00,390 - INFO - 🔄 点击刷新邮件按钮 (总计第30次，代理切换计数第30次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:00,391 - INFO - 已刷新30次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:31:00,395 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:31:00,404 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:31:00,404 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:31:00,405 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:31:02,426 - INFO - 暂无邮件
2025-07-31 07:31:02,426 - INFO - 已刷新 30 次，继续等待邮件...
2025-07-31 07:31:05,492 - INFO - 🔄 点击刷新邮件按钮 (总计第31次，代理切换计数第31次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:07,540 - INFO - 暂无邮件
2025-07-31 07:31:10,600 - INFO - 🔄 点击刷新邮件按钮 (总计第32次，代理切换计数第32次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:12,624 - INFO - 暂无邮件
2025-07-31 07:31:15,686 - INFO - 🔄 点击刷新邮件按钮 (总计第33次，代理切换计数第33次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:17,722 - INFO - 暂无邮件
2025-07-31 07:31:19,970 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:20,564 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:20,793 - INFO - 🔄 点击刷新邮件按钮 (总计第34次，代理切换计数第34次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:22,814 - INFO - 暂无邮件
2025-07-31 07:31:25,873 - INFO - 🔄 点击刷新邮件按钮 (总计第35次，代理切换计数第35次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:27,900 - INFO - 暂无邮件
2025-07-31 07:31:30,969 - INFO - 🔄 点击刷新邮件按钮 (总计第36次，代理切换计数第36次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:33,003 - INFO - 暂无邮件
2025-07-31 07:31:36,080 - INFO - 🔄 点击刷新邮件按钮 (总计第37次，代理切换计数第37次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:38,109 - INFO - 发现 1 封邮件
2025-07-31 07:31:38,110 - INFO - 收到邮件！当前总刷新次数: 37
2025-07-31 07:31:38,119 - INFO - 滚动到邮件位置
2025-07-31 07:31:39,139 - INFO - 通过JavaScript点击邮件
2025-07-31 07:31:39,139 - INFO - 点击邮件
2025-07-31 07:31:42,807 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:44,162 - INFO - 第1次尝试查找iframe...
2025-07-31 07:31:44,185 - INFO - 成功找到iframe
2025-07-31 07:31:44,193 - INFO - 开始提取验证码...
2025-07-31 07:31:44,193 - INFO - 方法2成功提取验证码: 315169
2025-07-31 07:31:44,193 - INFO - 提取到验证码: 315169
2025-07-31 07:31:44,197 - INFO - 验证码已保存到文件: 315169
2025-07-31 07:31:44,201 - INFO - 验证码已复制到剪贴板: 315169
2025-07-31 07:31:44,204 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:31:44,273 - INFO - 成功点击返回按钮
2025-07-31 07:31:44,852 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:46,284 - INFO - 结果已保存: <EMAIL> -> 315169
2025-07-31 07:31:46,284 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:31:46,285 - INFO - 所有计数器已重置为0
2025-07-31 07:31:46,286 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/50)
2025-07-31 07:31:46,286 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:31:46,286 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:32:16,379 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:32:42,608 - INFO - 用户中断程序
2025-07-31 07:32:42,611 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/7d3bc410da9cdd9b3147ab9dfcd53742
2025-07-31 07:33:33,388 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:33:34,541 - INFO - 浏览器启动成功
2025-07-31 07:33:36,155 - INFO - 正在加载 tempmail.la...
2025-07-31 07:33:36,243 - INFO - tempmail.la 页面加载完成
2025-07-31 07:33:36,346 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:33:37,494 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:33:37,495 - INFO - 开始监控新邮箱请求...
2025-07-31 07:33:37,495 - INFO - 开始监控注册状态...
2025-07-31 07:33:37,495 - INFO - 启动时换一次新邮箱...
2025-07-31 07:33:37,496 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:33:37,496 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:33:37,496 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:33:37,561 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:33:37,562 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:33:37,591 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:33:37,591 - INFO - 正在获取邮箱地址...
2025-07-31 07:33:39,644 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:33:39,645 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:33:39,650 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:33:39,654 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:33:39,654 - INFO - 📍 主循环开始 - 第 1/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:33:39,658 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:33:39,658 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:33:39,735 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:41,772 - INFO - 暂无邮件
2025-07-31 07:33:44,853 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:46,889 - INFO - 暂无邮件
2025-07-31 07:33:49,945 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:52,013 - INFO - 暂无邮件
2025-07-31 07:33:55,136 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:57,183 - INFO - 暂无邮件
2025-07-31 07:34:00,283 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:02,305 - INFO - 暂无邮件
2025-07-31 07:34:05,454 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:07,504 - INFO - 暂无邮件
2025-07-31 07:34:10,591 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:12,616 - INFO - 暂无邮件
2025-07-31 07:34:15,673 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:17,705 - INFO - 暂无邮件
2025-07-31 07:34:20,775 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:22,796 - INFO - 暂无邮件
2025-07-31 07:34:25,859 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:25,859 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:34:25,865 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:34:25,869 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:34:25,869 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:34:25,870 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:34:27,896 - INFO - 暂无邮件
2025-07-31 07:34:27,897 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:34:30,957 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:32,983 - INFO - 暂无邮件
2025-07-31 07:34:36,055 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:38,088 - INFO - 暂无邮件
2025-07-31 07:34:41,153 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:43,179 - INFO - 暂无邮件
2025-07-31 07:34:46,251 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:48,278 - INFO - 暂无邮件
2025-07-31 07:34:51,355 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:53,384 - INFO - 暂无邮件
2025-07-31 07:34:56,464 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:58,494 - INFO - 暂无邮件
2025-07-31 07:35:01,560 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:03,595 - INFO - 暂无邮件
2025-07-31 07:35:06,669 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:08,702 - INFO - 暂无邮件
2025-07-31 07:35:11,784 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:13,472 - INFO - 用户中断等待
2025-07-31 07:35:13,473 - WARNING - 第 1 个邮箱处理失败或被中断
2025-07-31 07:35:13,473 - INFO - 🎉 自动化完成！共成功处理 0 个邮箱，目标: 50 个
