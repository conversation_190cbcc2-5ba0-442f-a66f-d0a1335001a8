2025-07-31 07:07:53,028 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:07:54,147 - INFO - 浏览器启动成功
2025-07-31 07:07:55,230 - INFO - 正在加载 tempmail.la...
2025-07-31 07:07:55,361 - INFO - tempmail.la 页面加载完成
2025-07-31 07:07:55,457 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:07:56,579 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:07:56,579 - INFO - 开始监控新邮箱请求...
2025-07-31 07:07:56,579 - INFO - 开始监控注册状态...
2025-07-31 07:07:56,579 - INFO - 启动时换一次新邮箱...
2025-07-31 07:07:56,579 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:07:56,687 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:07:56,688 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:07:56,719 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:07:56,719 - INFO - 正在获取邮箱地址...
2025-07-31 07:07:58,795 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:07:58,795 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:07:58,803 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:07:58,807 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:07:58,807 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:07:58,811 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:07:58,811 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:07:58,886 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:00,963 - INFO - 暂无邮件
2025-07-31 07:08:04,036 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:06,079 - INFO - 暂无邮件
2025-07-31 07:08:09,140 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:11,198 - INFO - 暂无邮件
2025-07-31 07:08:14,375 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:16,424 - INFO - 暂无邮件
2025-07-31 07:08:19,533 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:21,560 - INFO - 暂无邮件
2025-07-31 07:08:24,689 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:26,755 - INFO - 暂无邮件
2025-07-31 07:08:29,900 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:31,971 - INFO - 暂无邮件
2025-07-31 07:08:35,088 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:37,142 - INFO - 暂无邮件
2025-07-31 07:08:40,294 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:42,336 - INFO - 暂无邮件
2025-07-31 07:08:45,466 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:45,466 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:08:45,470 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:08:45,480 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:08:45,480 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:08:45,481 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:08:47,511 - INFO - 暂无邮件
2025-07-31 07:08:47,511 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:08:50,596 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:52,647 - INFO - 暂无邮件
2025-07-31 07:08:55,735 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:08:57,762 - INFO - 暂无邮件
2025-07-31 07:09:00,841 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:02,870 - INFO - 暂无邮件
2025-07-31 07:09:05,950 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:08,005 - INFO - 暂无邮件
2025-07-31 07:09:11,117 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:13,149 - INFO - 暂无邮件
2025-07-31 07:09:16,217 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:18,278 - INFO - 暂无邮件
2025-07-31 07:09:21,341 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:23,389 - INFO - 暂无邮件
2025-07-31 07:09:27,174 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:29,220 - INFO - 暂无邮件
2025-07-31 07:09:32,339 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:34,386 - INFO - 暂无邮件
2025-07-31 07:09:37,469 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:37,469 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:09:37,473 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:09:37,482 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:09:37,482 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:09:37,483 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:09:39,512 - INFO - 暂无邮件
2025-07-31 07:09:39,512 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:09:42,575 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:44,611 - INFO - 暂无邮件
2025-07-31 07:09:47,733 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:49,796 - INFO - 暂无邮件
2025-07-31 07:09:52,924 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:09:53,719 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:09:54,977 - INFO - 暂无邮件
2025-07-31 07:09:58,083 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:00,158 - INFO - 暂无邮件
2025-07-31 07:10:03,298 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:05,337 - INFO - 暂无邮件
2025-07-31 07:10:08,466 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:10:10,482 - INFO - 发现 1 封邮件
2025-07-31 07:10:10,482 - INFO - 收到邮件！当前总刷新次数: 26
2025-07-31 07:10:10,488 - INFO - 滚动到邮件位置
2025-07-31 07:10:11,511 - INFO - 通过JavaScript点击邮件
2025-07-31 07:10:11,511 - INFO - 点击邮件
2025-07-31 07:10:16,530 - INFO - 第1次尝试查找iframe...
2025-07-31 07:10:16,557 - INFO - 成功找到iframe
2025-07-31 07:10:16,567 - INFO - 开始提取验证码...
2025-07-31 07:10:16,567 - INFO - 方法2成功提取验证码: 191932
2025-07-31 07:10:16,567 - INFO - 提取到验证码: 191932
2025-07-31 07:10:16,569 - INFO - 验证码已保存到文件: 191932
2025-07-31 07:10:16,572 - INFO - 验证码已复制到剪贴板: 191932
2025-07-31 07:10:16,577 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:10:16,659 - INFO - 成功点击返回按钮
2025-07-31 07:10:18,671 - INFO - 结果已保存: <EMAIL> -> 191932
2025-07-31 07:10:18,672 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:10:18,672 - INFO - 所有计数器已重置为0
2025-07-31 07:10:18,672 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/10)
2025-07-31 07:10:18,673 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:10:18,673 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:10:20,773 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:10:48,813 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:11:06,245 - INFO - 用户中断程序
2025-07-31 07:11:06,250 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/c1083fed533c15ade3ba72949d9a9e83
2025-07-31 07:11:34,808 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:11:35,911 - INFO - 浏览器启动成功
2025-07-31 07:11:37,780 - INFO - 正在加载 tempmail.la...
2025-07-31 07:11:37,921 - INFO - tempmail.la 页面加载完成
2025-07-31 07:11:38,011 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:11:39,152 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:11:39,152 - INFO - 开始监控新邮箱请求...
2025-07-31 07:11:39,152 - INFO - 开始监控注册状态...
2025-07-31 07:11:39,152 - INFO - 启动时换一次新邮箱...
2025-07-31 07:11:39,153 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:11:39,153 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:11:39,154 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:11:39,222 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:11:39,223 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:11:39,236 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:11:39,236 - INFO - 正在获取邮箱地址...
2025-07-31 07:11:41,292 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:11:41,293 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:11:41,301 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:11:41,308 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:11:41,308 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:11:41,309 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:11:41,309 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:11:41,310 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:11:41,310 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:11:41,402 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:43,442 - INFO - 暂无邮件
2025-07-31 07:11:46,519 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:48,569 - INFO - 暂无邮件
2025-07-31 07:11:51,656 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:53,716 - INFO - 暂无邮件
2025-07-31 07:11:56,885 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:11:58,935 - INFO - 暂无邮件
2025-07-31 07:12:02,096 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:04,176 - INFO - 暂无邮件
2025-07-31 07:12:07,233 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:09,264 - INFO - 暂无邮件
2025-07-31 07:12:12,394 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:14,462 - INFO - 暂无邮件
2025-07-31 07:12:17,518 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:19,567 - INFO - 暂无邮件
2025-07-31 07:12:22,650 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:24,677 - INFO - 暂无邮件
2025-07-31 07:12:27,732 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:27,732 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:12:27,737 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:12:27,744 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:12:27,744 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:12:27,744 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:12:29,769 - INFO - 暂无邮件
2025-07-31 07:12:29,769 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:12:32,838 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:34,867 - INFO - 暂无邮件
2025-07-31 07:12:37,935 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:39,960 - INFO - 暂无邮件
2025-07-31 07:12:43,027 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:45,097 - INFO - 暂无邮件
2025-07-31 07:12:48,287 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:50,360 - INFO - 暂无邮件
2025-07-31 07:12:53,466 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:12:55,492 - INFO - 暂无邮件
2025-07-31 07:12:58,554 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:00,611 - INFO - 暂无邮件
2025-07-31 07:13:03,798 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:05,874 - INFO - 暂无邮件
2025-07-31 07:13:08,993 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:11,013 - INFO - 暂无邮件
2025-07-31 07:13:14,168 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:16,217 - INFO - 暂无邮件
2025-07-31 07:13:19,795 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:19,795 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:13:19,808 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:13:19,818 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:13:19,819 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:13:19,822 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:13:21,916 - INFO - 暂无邮件
2025-07-31 07:13:21,916 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:13:25,047 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:27,065 - INFO - 暂无邮件
2025-07-31 07:13:30,181 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:32,213 - INFO - 暂无邮件
2025-07-31 07:13:35,330 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:13:37,373 - INFO - 暂无邮件
2025-07-31 07:13:40,026 - INFO - 用户中断等待
2025-07-31 07:13:40,027 - WARNING - 第 1 个邮箱处理失败或被中断
2025-07-31 07:13:40,032 - INFO - 🎉 自动化完成！共成功处理 0 个邮箱，目标: 10 个
2025-07-31 07:14:17,743 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:14:18,840 - INFO - 浏览器启动成功
2025-07-31 07:14:28,064 - INFO - 正在加载 tempmail.la...
2025-07-31 07:14:28,076 - INFO - tempmail.la 页面加载完成
2025-07-31 07:14:28,150 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:14:29,195 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:14:29,195 - INFO - 开始监控新邮箱请求...
2025-07-31 07:14:29,195 - INFO - 开始监控注册状态...
2025-07-31 07:14:29,195 - INFO - 启动时换一次新邮箱...
2025-07-31 07:14:29,199 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:14:29,199 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:14:29,199 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:14:29,278 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:14:29,278 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:14:29,295 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:14:29,296 - INFO - 正在获取邮箱地址...
2025-07-31 07:14:31,355 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:14:31,359 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:14:31,372 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:14:31,380 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:14:31,380 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:14:31,381 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:14:31,382 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:14:31,382 - INFO - 📍 主循环开始 - 第 1/20 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:14:31,382 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:14:31,383 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:14:31,383 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:14:31,466 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:33,509 - INFO - 暂无邮件
2025-07-31 07:14:36,670 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:38,730 - INFO - 暂无邮件
2025-07-31 07:14:41,795 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:43,859 - INFO - 暂无邮件
2025-07-31 07:14:46,930 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:48,992 - INFO - 暂无邮件
2025-07-31 07:14:52,065 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:54,117 - INFO - 暂无邮件
2025-07-31 07:14:57,317 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:14:59,390 - INFO - 暂无邮件
2025-07-31 07:15:02,527 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:04,548 - INFO - 暂无邮件
2025-07-31 07:15:07,697 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:09,734 - INFO - 暂无邮件
2025-07-31 07:15:12,801 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:14,850 - INFO - 暂无邮件
2025-07-31 07:15:17,991 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:17,992 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:15:17,997 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:15:18,003 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:15:18,003 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:15:18,003 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:15:20,027 - INFO - 暂无邮件
2025-07-31 07:15:20,027 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:15:23,095 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:25,124 - INFO - 暂无邮件
2025-07-31 07:15:28,189 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:30,214 - INFO - 暂无邮件
2025-07-31 07:15:33,275 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:35,315 - INFO - 暂无邮件
2025-07-31 07:15:38,485 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:40,540 - INFO - 暂无邮件
2025-07-31 07:15:40,600 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:41,233 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:43,360 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:15:43,701 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:45,738 - INFO - 暂无邮件
2025-07-31 07:15:48,873 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:15:50,915 - INFO - 发现 1 封邮件
2025-07-31 07:15:50,918 - INFO - 收到邮件！当前总刷新次数: 16
2025-07-31 07:15:50,942 - INFO - 滚动到邮件位置
2025-07-31 07:15:51,970 - INFO - 通过JavaScript点击邮件
2025-07-31 07:15:51,970 - INFO - 点击邮件
2025-07-31 07:15:56,986 - INFO - 第1次尝试查找iframe...
2025-07-31 07:15:57,049 - INFO - 成功找到iframe
2025-07-31 07:15:57,079 - INFO - 开始提取验证码...
2025-07-31 07:15:57,081 - INFO - 方法2成功提取验证码: 415028
2025-07-31 07:15:57,083 - INFO - 提取到验证码: 415028
2025-07-31 07:15:57,087 - INFO - 验证码已保存到文件: 415028
2025-07-31 07:15:57,096 - INFO - 验证码已复制到剪贴板: 415028
2025-07-31 07:15:57,110 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:15:57,252 - INFO - 成功点击返回按钮
2025-07-31 07:15:59,257 - INFO - 结果已保存: <EMAIL> -> 415028
2025-07-31 07:15:59,257 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:15:59,260 - INFO - 所有计数器已重置为0
2025-07-31 07:15:59,261 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/20)
2025-07-31 07:15:59,262 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:15:59,263 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:16:29,382 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:16:59,530 - INFO - ⏳ 等待注册完成中... 已等待 60s / 300s
2025-07-31 07:17:11,792 - INFO - 用户中断程序
2025-07-31 07:17:11,792 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/995fb470f0e4528d5eff78c3678e736b
2025-07-31 07:17:18,532 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:17:19,614 - INFO - 浏览器启动成功
2025-07-31 07:17:21,148 - INFO - 正在加载 tempmail.la...
2025-07-31 07:17:21,279 - INFO - tempmail.la 页面加载完成
2025-07-31 07:17:21,381 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:17:23,478 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:17:23,478 - INFO - 开始监控新邮箱请求...
2025-07-31 07:17:23,479 - INFO - 开始监控注册状态...
2025-07-31 07:17:23,479 - INFO - 启动时换一次新邮箱...
2025-07-31 07:17:23,480 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:17:23,480 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:17:23,480 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:17:23,507 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:17:23,508 - INFO - 已清空验证码文件
2025-07-31 07:17:23,570 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:17:26,570 - INFO - 正在获取邮箱地址...
2025-07-31 07:17:28,634 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:17:28,635 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:28,637 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:17:28,649 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:17:28,650 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:17:28,651 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:28,652 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:17:28,652 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:17:28,652 - INFO - 正在获取邮箱地址...
2025-07-31 07:17:30,706 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:17:30,707 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:30,711 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:17:30,714 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:17:30,715 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:17:30,716 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:17:30,716 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:17:30,717 - INFO - 启动时已换新邮箱: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:17:30,717 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:17:30,782 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:32,818 - INFO - 暂无邮件
2025-07-31 07:17:36,211 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:38,272 - INFO - 暂无邮件
2025-07-31 07:17:41,423 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:43,477 - INFO - 暂无邮件
2025-07-31 07:17:46,541 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:48,566 - INFO - 暂无邮件
2025-07-31 07:17:51,757 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:53,814 - INFO - 暂无邮件
2025-07-31 07:17:56,897 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:17:58,983 - INFO - 暂无邮件
2025-07-31 07:18:02,210 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:04,296 - INFO - 暂无邮件
2025-07-31 07:18:07,438 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:09,488 - INFO - 暂无邮件
2025-07-31 07:18:12,572 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:14,606 - INFO - 暂无邮件
2025-07-31 07:18:17,759 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:17,759 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:18:17,765 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:18:17,774 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:18:17,774 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:18:17,775 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:18:19,804 - INFO - 暂无邮件
2025-07-31 07:18:19,805 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:18:22,985 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:25,036 - INFO - 暂无邮件
2025-07-31 07:18:28,105 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:30,140 - INFO - 暂无邮件
2025-07-31 07:18:33,204 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:35,231 - INFO - 暂无邮件
2025-07-31 07:18:38,292 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:40,324 - INFO - 暂无邮件
2025-07-31 07:18:43,427 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:45,469 - INFO - 暂无邮件
2025-07-31 07:18:48,614 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:50,661 - INFO - 暂无邮件
2025-07-31 07:18:53,810 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:18:55,881 - INFO - 暂无邮件
2025-07-31 07:18:57,993 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:18:59,088 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:01,158 - INFO - 暂无邮件
2025-07-31 07:19:04,306 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:06,325 - INFO - 暂无邮件
2025-07-31 07:19:09,392 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:09,392 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:19:09,397 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:09,404 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:09,404 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:09,404 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:11,425 - INFO - 发现 1 封邮件
2025-07-31 07:19:11,425 - INFO - 收到邮件！当前总刷新次数: 20
2025-07-31 07:19:11,443 - INFO - 滚动到邮件位置
2025-07-31 07:19:12,458 - INFO - 通过JavaScript点击邮件
2025-07-31 07:19:12,459 - INFO - 点击邮件
2025-07-31 07:19:17,471 - INFO - 第1次尝试查找iframe...
2025-07-31 07:19:17,495 - INFO - 成功找到iframe
2025-07-31 07:19:17,506 - INFO - 开始提取验证码...
2025-07-31 07:19:17,507 - INFO - 方法2成功提取验证码: 809440
2025-07-31 07:19:17,507 - INFO - 提取到验证码: 809440
2025-07-31 07:19:17,507 - INFO - 验证码已保存到文件: 809440
2025-07-31 07:19:17,513 - INFO - 验证码已复制到剪贴板: 809440
2025-07-31 07:19:17,519 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:19:17,628 - INFO - 成功点击返回按钮
2025-07-31 07:19:19,641 - INFO - 结果已保存: <EMAIL> -> 809440
2025-07-31 07:19:19,641 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:19:19,643 - INFO - 所有计数器已重置为0
2025-07-31 07:19:19,643 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/10)
2025-07-31 07:19:19,644 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:19:19,644 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:19:33,703 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:19:33,703 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:19:33,707 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:19:33,707 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:19:33,764 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:19:33,764 - INFO - 已清空验证码文件
2025-07-31 07:19:33,832 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:19:36,835 - INFO - 正在获取邮箱地址...
2025-07-31 07:19:38,930 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:19:38,930 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:38,936 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:38,941 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:38,941 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:19:38,942 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:19:38,942 - INFO - 正在获取邮箱地址...
2025-07-31 07:19:41,021 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:19:41,021 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:41,026 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:19:41,031 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:19:41,031 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:19:41,032 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:19:41,032 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:19:41,033 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:19:41,033 - INFO - 🔄 开始第 2/10 个邮箱的邮件等待...
2025-07-31 07:19:41,033 - INFO - 📍 主循环开始 - 第 2/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:19:41,033 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:19:41,034 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:19:41,034 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:19:41,105 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:43,188 - INFO - 暂无邮件
2025-07-31 07:19:46,256 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:48,300 - INFO - 暂无邮件
2025-07-31 07:19:51,431 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:53,487 - INFO - 暂无邮件
2025-07-31 07:19:57,192 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:19:59,234 - INFO - 暂无邮件
2025-07-31 07:20:02,392 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:04,465 - INFO - 暂无邮件
2025-07-31 07:20:07,550 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:09,614 - INFO - 暂无邮件
2025-07-31 07:20:09,963 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:20:12,732 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:14,757 - INFO - 暂无邮件
2025-07-31 07:20:17,869 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:19,875 - INFO - 发现 1 封邮件
2025-07-31 07:20:19,875 - INFO - 收到邮件！当前总刷新次数: 8
2025-07-31 07:20:19,884 - INFO - 滚动到邮件位置
2025-07-31 07:20:20,912 - INFO - 通过JavaScript点击邮件
2025-07-31 07:20:20,912 - INFO - 点击邮件
2025-07-31 07:20:25,942 - INFO - 第1次尝试查找iframe...
2025-07-31 07:20:25,992 - INFO - 成功找到iframe
2025-07-31 07:20:26,016 - INFO - 开始提取验证码...
2025-07-31 07:20:26,017 - INFO - 方法2成功提取验证码: 353030
2025-07-31 07:20:26,017 - INFO - 提取到验证码: 353030
2025-07-31 07:20:26,018 - INFO - 验证码已保存到文件: 353030
2025-07-31 07:20:26,024 - INFO - 验证码已复制到剪贴板: 353030
2025-07-31 07:20:26,030 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:20:26,136 - INFO - 成功点击返回按钮
2025-07-31 07:20:28,151 - INFO - 结果已保存: <EMAIL> -> 353030
2025-07-31 07:20:28,151 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:20:28,155 - INFO - 所有计数器已重置为0
2025-07-31 07:20:28,155 - INFO - 第 2 个邮箱完成，成功获取邮件和验证码 (2/10)
2025-07-31 07:20:28,159 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:20:28,159 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:20:40,217 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:20:40,217 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:20:40,219 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:20:40,219 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:20:40,303 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:20:40,303 - INFO - 已清空验证码文件
2025-07-31 07:20:40,352 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:20:43,364 - INFO - 正在获取邮箱地址...
2025-07-31 07:20:45,431 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:20:45,432 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:45,435 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:20:45,440 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:45,440 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:20:45,441 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:20:45,441 - INFO - 正在获取邮箱地址...
2025-07-31 07:20:47,540 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:20:47,540 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:47,546 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:20:47,550 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:20:47,550 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:20:47,551 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:20:47,551 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:20:47,551 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 开始第 3/10 个邮箱的邮件等待...
2025-07-31 07:20:47,552 - INFO - 📍 主循环开始 - 第 3/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:20:47,552 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:20:47,611 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:49,692 - INFO - 暂无邮件
2025-07-31 07:20:52,908 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:20:54,931 - INFO - 暂无邮件
2025-07-31 07:20:57,991 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:00,025 - INFO - 暂无邮件
2025-07-31 07:21:03,087 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:05,120 - INFO - 暂无邮件
2025-07-31 07:21:08,187 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:10,212 - INFO - 暂无邮件
2025-07-31 07:21:13,285 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:15,320 - INFO - 暂无邮件
2025-07-31 07:21:18,381 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:20,404 - INFO - 暂无邮件
2025-07-31 07:21:23,474 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:25,498 - INFO - 暂无邮件
2025-07-31 07:21:28,569 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:30,603 - INFO - 暂无邮件
2025-07-31 07:21:33,664 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:33,664 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:21:33,668 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:21:33,672 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:21:33,676 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:21:33,677 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:21:35,714 - INFO - 暂无邮件
2025-07-31 07:21:35,715 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:21:38,781 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:40,811 - INFO - 暂无邮件
2025-07-31 07:21:43,874 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:45,900 - INFO - 暂无邮件
2025-07-31 07:21:48,966 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:50,999 - INFO - 暂无邮件
2025-07-31 07:21:54,072 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:21:56,105 - INFO - 暂无邮件
2025-07-31 07:21:59,173 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:01,206 - INFO - 暂无邮件
2025-07-31 07:22:04,268 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:06,296 - INFO - 暂无邮件
2025-07-31 07:22:09,352 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:11,378 - INFO - 暂无邮件
2025-07-31 07:22:14,452 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:16,483 - INFO - 暂无邮件
2025-07-31 07:22:19,540 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:21,572 - INFO - 暂无邮件
2025-07-31 07:22:24,633 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:24,633 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:22:24,638 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:22:24,644 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:22:24,644 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:22:24,648 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:22:26,669 - INFO - 暂无邮件
2025-07-31 07:22:26,669 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:22:29,737 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:31,764 - INFO - 暂无邮件
2025-07-31 07:22:34,939 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:36,998 - INFO - 暂无邮件
2025-07-31 07:22:40,066 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:42,103 - INFO - 暂无邮件
2025-07-31 07:22:45,225 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:47,282 - INFO - 暂无邮件
2025-07-31 07:22:49,856 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:22:50,431 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:52,483 - INFO - 暂无邮件
2025-07-31 07:22:55,571 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:22:57,617 - INFO - 暂无邮件
2025-07-31 07:23:00,710 - INFO - 🔄 点击刷新邮件按钮 (总计第27次，代理切换计数第27次) - 当前邮箱: <EMAIL>
2025-07-31 07:23:02,746 - INFO - 发现 1 封邮件
2025-07-31 07:23:02,746 - INFO - 收到邮件！当前总刷新次数: 27
2025-07-31 07:23:02,758 - INFO - 滚动到邮件位置
2025-07-31 07:23:03,784 - INFO - 通过JavaScript点击邮件
2025-07-31 07:23:03,785 - INFO - 点击邮件
2025-07-31 07:23:08,804 - INFO - 第1次尝试查找iframe...
2025-07-31 07:23:08,865 - INFO - 成功找到iframe
2025-07-31 07:23:08,876 - INFO - 开始提取验证码...
2025-07-31 07:23:08,876 - INFO - 方法2成功提取验证码: 816257
2025-07-31 07:23:08,876 - INFO - 提取到验证码: 816257
2025-07-31 07:23:08,876 - INFO - 验证码已保存到文件: 816257
2025-07-31 07:23:08,885 - INFO - 验证码已复制到剪贴板: 816257
2025-07-31 07:23:08,892 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:23:08,984 - INFO - 成功点击返回按钮
2025-07-31 07:23:10,989 - INFO - 结果已保存: <EMAIL> -> 816257
2025-07-31 07:23:10,989 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:23:10,995 - INFO - 所有计数器已重置为0
2025-07-31 07:23:10,996 - INFO - 第 3 个邮箱完成，成功获取邮件和验证码 (3/10)
2025-07-31 07:23:10,997 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:23:10,998 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:23:15,876 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:23:41,150 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:24:11,296 - INFO - ⏳ 等待注册完成中... 已等待 60s / 300s
2025-07-31 07:24:41,416 - INFO - ⏳ 等待注册完成中... 已等待 90s / 300s
2025-07-31 07:25:11,555 - INFO - ⏳ 等待注册完成中... 已等待 121s / 300s
2025-07-31 07:25:41,675 - INFO - ⏳ 等待注册完成中... 已等待 151s / 300s
2025-07-31 07:26:11,808 - INFO - ⏳ 等待注册完成中... 已等待 181s / 300s
2025-07-31 07:26:41,919 - INFO - ⏳ 等待注册完成中... 已等待 211s / 300s
2025-07-31 07:28:07,744 - INFO - 用户中断程序
2025-07-31 07:28:07,747 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/1c6fe2ff73fd63dffe81f6d85afab6f4
2025-07-31 07:28:25,169 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:28:26,266 - INFO - 浏览器启动成功
2025-07-31 07:28:27,706 - INFO - 正在加载 tempmail.la...
2025-07-31 07:28:27,774 - INFO - tempmail.la 页面加载完成
2025-07-31 07:28:27,888 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:28:28,944 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:28:28,945 - INFO - 开始监控新邮箱请求...
2025-07-31 07:28:28,945 - INFO - 开始监控注册状态...
2025-07-31 07:28:28,945 - INFO - 启动时换一次新邮箱...
2025-07-31 07:28:28,946 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:28:28,946 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:28:28,946 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:28:29,092 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:28:29,092 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:28:29,144 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:28:29,144 - INFO - 正在获取邮箱地址...
2025-07-31 07:28:31,219 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:28:31,219 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:28:31,227 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:28:31,236 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:28:31,236 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:28:31,237 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:28:31,237 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:28:31,237 - INFO - 📍 主循环开始 - 第 1/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:28:31,238 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:28:31,238 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:28:31,239 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:28:31,364 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:33,454 - INFO - 暂无邮件
2025-07-31 07:28:36,525 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:38,573 - INFO - 暂无邮件
2025-07-31 07:28:41,705 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:43,774 - INFO - 暂无邮件
2025-07-31 07:28:46,892 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:48,965 - INFO - 暂无邮件
2025-07-31 07:28:52,099 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:54,144 - INFO - 暂无邮件
2025-07-31 07:28:57,259 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:28:59,315 - INFO - 暂无邮件
2025-07-31 07:29:02,474 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:04,556 - INFO - 暂无邮件
2025-07-31 07:29:07,660 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:09,705 - INFO - 暂无邮件
2025-07-31 07:29:12,771 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:14,796 - INFO - 暂无邮件
2025-07-31 07:29:17,864 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:17,864 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:29:17,870 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:29:17,875 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:29:17,876 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:29:17,876 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:29:19,904 - INFO - 暂无邮件
2025-07-31 07:29:19,904 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:29:22,975 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:25,008 - INFO - 暂无邮件
2025-07-31 07:29:28,076 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:30,107 - INFO - 暂无邮件
2025-07-31 07:29:33,174 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:35,194 - INFO - 暂无邮件
2025-07-31 07:29:38,263 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:40,297 - INFO - 暂无邮件
2025-07-31 07:29:43,375 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:45,395 - INFO - 暂无邮件
2025-07-31 07:29:48,459 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:50,482 - INFO - 暂无邮件
2025-07-31 07:29:53,549 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:29:55,578 - INFO - 暂无邮件
2025-07-31 07:29:58,643 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:00,674 - INFO - 暂无邮件
2025-07-31 07:30:03,745 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:05,774 - INFO - 暂无邮件
2025-07-31 07:30:09,451 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:09,451 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:30:09,455 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:30:09,459 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:30:09,459 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:30:09,459 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:30:11,490 - INFO - 暂无邮件
2025-07-31 07:30:11,491 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:30:14,561 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:16,592 - INFO - 暂无邮件
2025-07-31 07:30:19,651 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:21,678 - INFO - 暂无邮件
2025-07-31 07:30:24,740 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:26,770 - INFO - 暂无邮件
2025-07-31 07:30:29,829 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:31,864 - INFO - 暂无邮件
2025-07-31 07:30:34,922 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:36,953 - INFO - 暂无邮件
2025-07-31 07:30:40,021 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:42,041 - INFO - 暂无邮件
2025-07-31 07:30:45,108 - INFO - 🔄 点击刷新邮件按钮 (总计第27次，代理切换计数第27次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:47,137 - INFO - 暂无邮件
2025-07-31 07:30:50,207 - INFO - 🔄 点击刷新邮件按钮 (总计第28次，代理切换计数第28次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:52,240 - INFO - 暂无邮件
2025-07-31 07:30:55,297 - INFO - 🔄 点击刷新邮件按钮 (总计第29次，代理切换计数第29次) - 当前邮箱: <EMAIL>
2025-07-31 07:30:57,327 - INFO - 暂无邮件
2025-07-31 07:31:00,390 - INFO - 🔄 点击刷新邮件按钮 (总计第30次，代理切换计数第30次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:00,391 - INFO - 已刷新30次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:31:00,395 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:31:00,404 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:31:00,404 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:31:00,405 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:31:02,426 - INFO - 暂无邮件
2025-07-31 07:31:02,426 - INFO - 已刷新 30 次，继续等待邮件...
2025-07-31 07:31:05,492 - INFO - 🔄 点击刷新邮件按钮 (总计第31次，代理切换计数第31次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:07,540 - INFO - 暂无邮件
2025-07-31 07:31:10,600 - INFO - 🔄 点击刷新邮件按钮 (总计第32次，代理切换计数第32次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:12,624 - INFO - 暂无邮件
2025-07-31 07:31:15,686 - INFO - 🔄 点击刷新邮件按钮 (总计第33次，代理切换计数第33次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:17,722 - INFO - 暂无邮件
2025-07-31 07:31:19,970 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:20,564 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:20,793 - INFO - 🔄 点击刷新邮件按钮 (总计第34次，代理切换计数第34次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:22,814 - INFO - 暂无邮件
2025-07-31 07:31:25,873 - INFO - 🔄 点击刷新邮件按钮 (总计第35次，代理切换计数第35次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:27,900 - INFO - 暂无邮件
2025-07-31 07:31:30,969 - INFO - 🔄 点击刷新邮件按钮 (总计第36次，代理切换计数第36次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:33,003 - INFO - 暂无邮件
2025-07-31 07:31:36,080 - INFO - 🔄 点击刷新邮件按钮 (总计第37次，代理切换计数第37次) - 当前邮箱: <EMAIL>
2025-07-31 07:31:38,109 - INFO - 发现 1 封邮件
2025-07-31 07:31:38,110 - INFO - 收到邮件！当前总刷新次数: 37
2025-07-31 07:31:38,119 - INFO - 滚动到邮件位置
2025-07-31 07:31:39,139 - INFO - 通过JavaScript点击邮件
2025-07-31 07:31:39,139 - INFO - 点击邮件
2025-07-31 07:31:42,807 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:44,162 - INFO - 第1次尝试查找iframe...
2025-07-31 07:31:44,185 - INFO - 成功找到iframe
2025-07-31 07:31:44,193 - INFO - 开始提取验证码...
2025-07-31 07:31:44,193 - INFO - 方法2成功提取验证码: 315169
2025-07-31 07:31:44,193 - INFO - 提取到验证码: 315169
2025-07-31 07:31:44,197 - INFO - 验证码已保存到文件: 315169
2025-07-31 07:31:44,201 - INFO - 验证码已复制到剪贴板: 315169
2025-07-31 07:31:44,204 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:31:44,273 - INFO - 成功点击返回按钮
2025-07-31 07:31:44,852 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:31:46,284 - INFO - 结果已保存: <EMAIL> -> 315169
2025-07-31 07:31:46,284 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:31:46,285 - INFO - 所有计数器已重置为0
2025-07-31 07:31:46,286 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/50)
2025-07-31 07:31:46,286 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:31:46,286 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:32:16,379 - INFO - ⏳ 等待注册完成中... 已等待 30s / 300s
2025-07-31 07:32:42,608 - INFO - 用户中断程序
2025-07-31 07:32:42,611 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/7d3bc410da9cdd9b3147ab9dfcd53742
2025-07-31 07:33:33,388 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:33:34,541 - INFO - 浏览器启动成功
2025-07-31 07:33:36,155 - INFO - 正在加载 tempmail.la...
2025-07-31 07:33:36,243 - INFO - tempmail.la 页面加载完成
2025-07-31 07:33:36,346 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:33:37,494 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:33:37,495 - INFO - 开始监控新邮箱请求...
2025-07-31 07:33:37,495 - INFO - 开始监控注册状态...
2025-07-31 07:33:37,495 - INFO - 启动时换一次新邮箱...
2025-07-31 07:33:37,496 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:33:37,496 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:33:37,496 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:33:37,561 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:33:37,562 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:33:37,591 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:33:37,591 - INFO - 正在获取邮箱地址...
2025-07-31 07:33:39,644 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:33:39,645 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:33:39,650 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:33:39,654 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:33:39,654 - INFO - 📍 主循环开始 - 第 1/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:33:39,654 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:33:39,658 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:33:39,658 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:33:39,735 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:41,772 - INFO - 暂无邮件
2025-07-31 07:33:44,853 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:46,889 - INFO - 暂无邮件
2025-07-31 07:33:49,945 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:52,013 - INFO - 暂无邮件
2025-07-31 07:33:55,136 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:33:57,183 - INFO - 暂无邮件
2025-07-31 07:34:00,283 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:02,305 - INFO - 暂无邮件
2025-07-31 07:34:05,454 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:07,504 - INFO - 暂无邮件
2025-07-31 07:34:10,591 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:12,616 - INFO - 暂无邮件
2025-07-31 07:34:15,673 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:17,705 - INFO - 暂无邮件
2025-07-31 07:34:20,775 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:22,796 - INFO - 暂无邮件
2025-07-31 07:34:25,859 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:25,859 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:34:25,865 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:34:25,869 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:34:25,869 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:34:25,870 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:34:27,896 - INFO - 暂无邮件
2025-07-31 07:34:27,897 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:34:30,957 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:32,983 - INFO - 暂无邮件
2025-07-31 07:34:36,055 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:38,088 - INFO - 暂无邮件
2025-07-31 07:34:41,153 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:43,179 - INFO - 暂无邮件
2025-07-31 07:34:46,251 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:48,278 - INFO - 暂无邮件
2025-07-31 07:34:51,355 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:53,384 - INFO - 暂无邮件
2025-07-31 07:34:56,464 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:34:58,494 - INFO - 暂无邮件
2025-07-31 07:35:01,560 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:03,595 - INFO - 暂无邮件
2025-07-31 07:35:06,669 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:08,702 - INFO - 暂无邮件
2025-07-31 07:35:11,784 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:13,472 - INFO - 用户中断等待
2025-07-31 07:35:13,473 - WARNING - 第 1 个邮箱处理失败或被中断
2025-07-31 07:35:13,473 - INFO - 🎉 自动化完成！共成功处理 0 个邮箱，目标: 50 个
2025-07-31 07:35:26,061 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:35:27,172 - INFO - 浏览器启动成功
2025-07-31 07:35:28,339 - INFO - 正在加载 tempmail.la...
2025-07-31 07:35:28,472 - INFO - tempmail.la 页面加载完成
2025-07-31 07:35:28,550 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:35:29,604 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:35:29,604 - INFO - 开始监控新邮箱请求...
2025-07-31 07:35:29,604 - INFO - 开始监控注册状态...
2025-07-31 07:35:29,604 - INFO - 启动时换一次新邮箱...
2025-07-31 07:35:29,604 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:35:29,608 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:35:29,608 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:35:29,669 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:35:29,669 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:35:29,705 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:35:29,705 - INFO - 正在获取邮箱地址...
2025-07-31 07:35:31,806 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:35:31,807 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:35:31,810 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:35:31,818 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:35:31,818 - INFO - 📍 主循环开始 - 第 1/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:35:31,818 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:35:31,892 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:33,937 - INFO - 暂无邮件
2025-07-31 07:35:37,002 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:39,062 - INFO - 暂无邮件
2025-07-31 07:35:42,167 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:44,230 - INFO - 暂无邮件
2025-07-31 07:35:47,328 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:49,418 - INFO - 暂无邮件
2025-07-31 07:35:52,489 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:54,515 - INFO - 暂无邮件
2025-07-31 07:35:57,591 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:35:59,628 - INFO - 暂无邮件
2025-07-31 07:36:02,686 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:04,730 - INFO - 暂无邮件
2025-07-31 07:36:07,874 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:09,936 - INFO - 暂无邮件
2025-07-31 07:36:12,997 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:15,021 - INFO - 暂无邮件
2025-07-31 07:36:18,088 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:18,089 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:36:18,097 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:36:18,106 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:36:18,106 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:36:18,106 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:36:20,137 - INFO - 暂无邮件
2025-07-31 07:36:20,137 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:36:23,204 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:25,222 - INFO - 暂无邮件
2025-07-31 07:36:28,287 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:30,314 - INFO - 暂无邮件
2025-07-31 07:36:33,385 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:35,446 - INFO - 暂无邮件
2025-07-31 07:36:38,600 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:40,629 - INFO - 暂无邮件
2025-07-31 07:36:43,749 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:45,792 - INFO - 暂无邮件
2025-07-31 07:36:48,855 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:50,887 - INFO - 暂无邮件
2025-07-31 07:36:53,963 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:36:56,006 - INFO - 暂无邮件
2025-07-31 07:36:59,148 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:01,217 - INFO - 暂无邮件
2025-07-31 07:37:04,379 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:06,456 - INFO - 暂无邮件
2025-07-31 07:37:09,578 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:09,579 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:37:09,586 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:37:09,595 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:37:09,595 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:37:09,596 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:37:11,636 - INFO - 暂无邮件
2025-07-31 07:37:11,637 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:37:14,721 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:16,745 - INFO - 暂无邮件
2025-07-31 07:37:20,205 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:22,234 - INFO - 暂无邮件
2025-07-31 07:37:25,308 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:27,345 - INFO - 暂无邮件
2025-07-31 07:37:30,414 - INFO - 🔄 点击刷新邮件按钮 (总计第24次，代理切换计数第24次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:32,452 - INFO - 暂无邮件
2025-07-31 07:37:35,548 - INFO - 🔄 点击刷新邮件按钮 (总计第25次，代理切换计数第25次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:37,573 - INFO - 暂无邮件
2025-07-31 07:37:40,647 - INFO - 🔄 点击刷新邮件按钮 (总计第26次，代理切换计数第26次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:42,684 - INFO - 暂无邮件
2025-07-31 07:37:45,804 - INFO - 🔄 点击刷新邮件按钮 (总计第27次，代理切换计数第27次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:47,880 - INFO - 暂无邮件
2025-07-31 07:37:50,999 - INFO - 🔄 点击刷新邮件按钮 (总计第28次，代理切换计数第28次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:53,078 - INFO - 暂无邮件
2025-07-31 07:37:56,233 - INFO - 🔄 点击刷新邮件按钮 (总计第29次，代理切换计数第29次) - 当前邮箱: <EMAIL>
2025-07-31 07:37:58,287 - INFO - 暂无邮件
2025-07-31 07:38:01,417 - INFO - 🔄 点击刷新邮件按钮 (总计第30次，代理切换计数第30次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:01,417 - INFO - 已刷新30次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:38:01,422 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:38:01,429 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:38:01,429 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:38:01,430 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:38:03,453 - INFO - 暂无邮件
2025-07-31 07:38:03,453 - INFO - 已刷新 30 次，继续等待邮件...
2025-07-31 07:38:06,565 - INFO - 🔄 点击刷新邮件按钮 (总计第31次，代理切换计数第31次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:08,596 - INFO - 暂无邮件
2025-07-31 07:38:11,686 - INFO - 🔄 点击刷新邮件按钮 (总计第32次，代理切换计数第32次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:13,726 - INFO - 暂无邮件
2025-07-31 07:38:16,815 - INFO - 🔄 点击刷新邮件按钮 (总计第33次，代理切换计数第33次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:18,844 - INFO - 暂无邮件
2025-07-31 07:38:21,919 - INFO - 🔄 点击刷新邮件按钮 (总计第34次，代理切换计数第34次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:23,986 - INFO - 暂无邮件
2025-07-31 07:38:27,068 - INFO - 🔄 点击刷新邮件按钮 (总计第35次，代理切换计数第35次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:29,128 - INFO - 暂无邮件
2025-07-31 07:38:32,228 - INFO - 🔄 点击刷新邮件按钮 (总计第36次，代理切换计数第36次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:34,264 - INFO - 暂无邮件
2025-07-31 07:38:37,341 - INFO - 🔄 点击刷新邮件按钮 (总计第37次，代理切换计数第37次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:39,393 - INFO - 暂无邮件
2025-07-31 07:38:42,465 - INFO - 🔄 点击刷新邮件按钮 (总计第38次，代理切换计数第38次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:44,498 - INFO - 暂无邮件
2025-07-31 07:38:47,594 - INFO - 🔄 点击刷新邮件按钮 (总计第39次，代理切换计数第39次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:49,659 - INFO - 暂无邮件
2025-07-31 07:38:52,870 - INFO - 🔄 点击刷新邮件按钮 (总计第40次，代理切换计数第40次) - 当前邮箱: <EMAIL>
2025-07-31 07:38:52,871 - INFO - 已刷新40次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:38:52,880 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:38:52,893 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:38:52,893 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:38:52,894 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:38:54,939 - INFO - 暂无邮件
2025-07-31 07:38:54,940 - INFO - 已刷新 40 次，继续等待邮件...
2025-07-31 07:38:58,117 - INFO - 🔄 点击刷新邮件按钮 (总计第41次，代理切换计数第41次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:00,168 - INFO - 暂无邮件
2025-07-31 07:39:03,319 - INFO - 🔄 点击刷新邮件按钮 (总计第42次，代理切换计数第42次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:05,407 - INFO - 暂无邮件
2025-07-31 07:39:06,866 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:39:08,535 - INFO - 🔄 点击刷新邮件按钮 (总计第43次，代理切换计数第43次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:10,593 - INFO - 暂无邮件
2025-07-31 07:39:13,677 - INFO - 🔄 点击刷新邮件按钮 (总计第44次，代理切换计数第44次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:15,713 - INFO - 暂无邮件
2025-07-31 07:39:18,892 - INFO - 🔄 点击刷新邮件按钮 (总计第45次，代理切换计数第45次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:20,900 - INFO - 发现 1 封邮件
2025-07-31 07:39:20,901 - INFO - 收到邮件！当前总刷新次数: 45
2025-07-31 07:39:20,908 - INFO - 滚动到邮件位置
2025-07-31 07:39:21,926 - INFO - 通过JavaScript点击邮件
2025-07-31 07:39:21,926 - INFO - 点击邮件
2025-07-31 07:39:26,944 - INFO - 第1次尝试查找iframe...
2025-07-31 07:39:26,994 - INFO - 成功找到iframe
2025-07-31 07:39:27,010 - INFO - 开始提取验证码...
2025-07-31 07:39:27,010 - INFO - 方法2成功提取验证码: 717873
2025-07-31 07:39:27,010 - INFO - 提取到验证码: 717873
2025-07-31 07:39:27,014 - INFO - 验证码已保存到文件: 717873
2025-07-31 07:39:27,027 - INFO - 验证码已复制到剪贴板: 717873
2025-07-31 07:39:27,034 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:39:27,108 - INFO - 成功点击返回按钮
2025-07-31 07:39:29,113 - INFO - 结果已保存: <EMAIL> -> 717873
2025-07-31 07:39:29,113 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:39:29,113 - INFO - 所有计数器已重置为0
2025-07-31 07:39:29,114 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/50)
2025-07-31 07:39:29,114 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:39:29,114 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:39:41,174 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:39:41,174 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:39:41,175 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:39:41,176 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:39:41,203 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:39:41,203 - INFO - 已清空验证码文件
2025-07-31 07:39:41,257 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:39:44,270 - INFO - 正在获取邮箱地址...
2025-07-31 07:39:46,346 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:39:46,350 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:39:46,356 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:39:46,366 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:39:46,367 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:39:46,368 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:39:46,369 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:39:46,372 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:39:46,374 - INFO - 正在获取邮箱地址...
2025-07-31 07:39:48,464 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:39:48,465 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:39:48,468 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:39:48,471 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:39:48,471 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:39:48,471 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:39:48,471 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:39:48,471 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:39:48,471 - INFO - 🔄 开始第 2/50 个邮箱的邮件等待...
2025-07-31 07:39:48,475 - INFO - 📍 主循环开始 - 第 2/50 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:39:48,475 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:39:48,475 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:39:48,475 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:39:48,541 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:50,612 - INFO - 暂无邮件
2025-07-31 07:39:53,694 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:39:55,747 - INFO - 暂无邮件
2025-07-31 07:39:58,827 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:00,849 - INFO - 暂无邮件
2025-07-31 07:40:03,915 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:05,939 - INFO - 暂无邮件
2025-07-31 07:40:09,000 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:11,028 - INFO - 暂无邮件
2025-07-31 07:40:14,096 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:16,127 - INFO - 暂无邮件
2025-07-31 07:40:19,193 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:21,233 - INFO - 暂无邮件
2025-07-31 07:40:24,810 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:26,833 - INFO - 暂无邮件
2025-07-31 07:40:29,250 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/51dc99a7536d3dc202747430e56ced2f/title
2025-07-31 07:40:29,908 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:31,968 - INFO - 暂无邮件
2025-07-31 07:40:35,060 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:35,060 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:40:35,065 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:40:35,072 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:40:35,072 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:40:35,073 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:40:37,131 - INFO - 暂无邮件
2025-07-31 07:40:37,132 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:40:40,260 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:42,335 - INFO - 暂无邮件
2025-07-31 07:40:45,448 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:47,483 - INFO - 暂无邮件
2025-07-31 07:40:50,550 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:40:51,054 - INFO - 用户中断等待
2025-07-31 07:40:51,054 - WARNING - 第 2 个邮箱处理失败或被中断
2025-07-31 07:40:51,056 - INFO - 🎉 自动化完成！共成功处理 1 个邮箱，目标: 50 个
2025-07-31 07:42:41,407 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:42:42,505 - INFO - 浏览器启动成功
2025-07-31 07:42:43,685 - INFO - 正在加载 tempmail.la...
2025-07-31 07:42:43,813 - INFO - tempmail.la 页面加载完成
2025-07-31 07:42:43,913 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:42:44,950 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:42:44,950 - INFO - 开始监控新邮箱请求...
2025-07-31 07:42:44,954 - INFO - 开始监控注册状态...
2025-07-31 07:42:44,954 - INFO - 启动时换一次新邮箱...
2025-07-31 07:42:44,954 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:42:44,954 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:42:44,954 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:42:45,018 - ERROR - ❌ 所有方法都无法找到换邮箱按钮
2025-07-31 07:42:45,019 - INFO - 🔍 当前页面源码片段:
2025-07-31 07:42:45,052 - INFO - 页面中包含'换邮箱'文本
2025-07-31 07:42:45,052 - INFO - 正在获取邮箱地址...
2025-07-31 07:42:47,136 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:42:47,136 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:42:47,143 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:42:47,148 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:42:47,148 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:42:47,149 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:42:47,150 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:42:47,151 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:42:47,153 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:42:47,154 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:42:47,154 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:42:47,225 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:42:49,260 - INFO - 暂无邮件
2025-07-31 07:42:52,336 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:42:54,371 - INFO - 暂无邮件
2025-07-31 07:42:57,483 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:42:59,519 - INFO - 暂无邮件
2025-07-31 07:43:02,688 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:04,758 - INFO - 暂无邮件
2025-07-31 07:43:07,873 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:09,960 - INFO - 暂无邮件
2025-07-31 07:43:13,046 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:15,126 - INFO - 暂无邮件
2025-07-31 07:43:18,309 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:20,363 - INFO - 暂无邮件
2025-07-31 07:43:23,436 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:25,479 - INFO - 暂无邮件
2025-07-31 07:43:28,609 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:30,658 - INFO - 暂无邮件
2025-07-31 07:43:33,730 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:33,730 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:43:33,736 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:43:33,741 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:43:33,741 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:43:33,742 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:43:35,817 - INFO - 暂无邮件
2025-07-31 07:43:35,817 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:43:38,880 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:40,904 - INFO - 暂无邮件
2025-07-31 07:43:43,960 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:45,979 - INFO - 暂无邮件
2025-07-31 07:43:49,045 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:51,071 - INFO - 暂无邮件
2025-07-31 07:43:54,196 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:43:56,254 - INFO - 暂无邮件
2025-07-31 07:43:59,322 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:01,375 - INFO - 暂无邮件
2025-07-31 07:44:04,496 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:06,547 - INFO - 暂无邮件
2025-07-31 07:44:09,655 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:11,732 - INFO - 暂无邮件
2025-07-31 07:44:15,426 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:17,478 - INFO - 暂无邮件
2025-07-31 07:44:19,253 - WARNING - Connection pool is full, discarding connection: localhost. Connection pool size: 1
2025-07-31 07:44:20,544 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:22,624 - INFO - 暂无邮件
2025-07-31 07:44:25,803 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:25,803 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:44:25,817 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:44:25,828 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:44:25,828 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:44:25,830 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:44:27,865 - INFO - 暂无邮件
2025-07-31 07:44:27,865 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:44:30,991 - INFO - 🔄 点击刷新邮件按钮 (总计第21次，代理切换计数第21次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:33,065 - INFO - 暂无邮件
2025-07-31 07:44:36,244 - INFO - 🔄 点击刷新邮件按钮 (总计第22次，代理切换计数第22次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:38,274 - INFO - 暂无邮件
2025-07-31 07:44:41,342 - INFO - 🔄 点击刷新邮件按钮 (总计第23次，代理切换计数第23次) - 当前邮箱: <EMAIL>
2025-07-31 07:44:43,365 - INFO - 发现 1 封邮件
2025-07-31 07:44:43,369 - INFO - 收到邮件！当前总刷新次数: 23
2025-07-31 07:44:43,383 - INFO - 滚动到邮件位置
2025-07-31 07:44:44,403 - INFO - 通过JavaScript点击邮件
2025-07-31 07:44:44,404 - INFO - 点击邮件
2025-07-31 07:44:49,426 - INFO - 第1次尝试查找iframe...
2025-07-31 07:44:49,462 - INFO - 成功找到iframe
2025-07-31 07:44:49,473 - INFO - 开始提取验证码...
2025-07-31 07:44:49,473 - INFO - 方法2成功提取验证码: 817884
2025-07-31 07:44:49,473 - INFO - 提取到验证码: 817884
2025-07-31 07:44:49,477 - INFO - 验证码已保存到文件: 817884
2025-07-31 07:44:49,480 - INFO - 验证码已复制到剪贴板: 817884
2025-07-31 07:44:49,484 - INFO - 第1次尝试查找返回按钮...
2025-07-31 07:44:49,561 - INFO - 成功点击返回按钮
2025-07-31 07:44:51,570 - INFO - 结果已保存: <EMAIL> -> 817884
2025-07-31 07:44:51,571 - INFO - 收到邮件，重置所有计数器
2025-07-31 07:44:51,573 - INFO - 所有计数器已重置为0
2025-07-31 07:44:51,574 - INFO - 第 1 个邮箱完成，成功获取邮件和验证码 (1/10)
2025-07-31 07:44:51,575 - INFO - ✅ 获取到验证码，等待注册完成后再切换邮箱...
2025-07-31 07:44:51,576 - INFO - ⏳ 等待注册完成中... 已等待 0s / 300s
2025-07-31 07:45:03,627 - INFO - ✅ 检测到注册完成，现在可以切换邮箱
2025-07-31 07:45:03,627 - INFO - ✅ 成功删除注册完成标记文件
2025-07-31 07:45:03,627 - INFO - 🚀 开始切换到下一个邮箱...
2025-07-31 07:45:03,627 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:45:03,690 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:45:03,690 - INFO - 已清空验证码文件
2025-07-31 07:45:03,758 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:45:06,761 - INFO - 正在获取邮箱地址...
2025-07-31 07:45:08,869 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:45:08,869 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:45:08,872 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:45:08,880 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:45:08,880 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:45:08,884 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:45:08,884 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:45:08,884 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:45:08,884 - INFO - 正在获取邮箱地址...
2025-07-31 07:45:10,967 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:45:10,968 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:45:10,971 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:45:10,974 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:45:10,974 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:45:10,975 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:45:10,975 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:45:10,975 - INFO - ✅ 已切换到新邮箱: <EMAIL>
2025-07-31 07:45:10,976 - INFO - 🔄 开始第 2/10 个邮箱的邮件等待...
2025-07-31 07:45:10,976 - INFO - 📍 主循环开始 - 第 2/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:45:10,976 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:45:10,976 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:45:10,977 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:45:11,041 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:13,098 - INFO - 暂无邮件
2025-07-31 07:45:16,214 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:18,232 - INFO - 暂无邮件
2025-07-31 07:45:21,307 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:23,339 - INFO - 暂无邮件
2025-07-31 07:45:26,404 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:28,426 - INFO - 暂无邮件
2025-07-31 07:45:31,493 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:33,513 - INFO - 暂无邮件
2025-07-31 07:45:36,577 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:38,599 - INFO - 暂无邮件
2025-07-31 07:45:41,669 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:43,705 - INFO - 暂无邮件
2025-07-31 07:45:46,784 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:48,803 - INFO - 暂无邮件
2025-07-31 07:45:51,883 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:53,916 - INFO - 暂无邮件
2025-07-31 07:45:56,984 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:45:56,984 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:45:56,989 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:45:56,993 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:45:56,994 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:45:56,994 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:45:59,032 - INFO - 暂无邮件
2025-07-31 07:45:59,032 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:46:02,102 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:04,123 - INFO - 暂无邮件
2025-07-31 07:46:07,201 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:09,223 - INFO - 暂无邮件
2025-07-31 07:46:12,288 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:14,320 - INFO - 暂无邮件
2025-07-31 07:46:17,386 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:19,422 - INFO - 暂无邮件
2025-07-31 07:46:22,506 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:24,537 - INFO - 暂无邮件
2025-07-31 07:46:27,620 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:29,675 - INFO - 暂无邮件
2025-07-31 07:46:32,812 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:34,837 - INFO - 暂无邮件
2025-07-31 07:46:37,985 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:40,036 - INFO - 暂无邮件
2025-07-31 07:46:43,140 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:45,174 - INFO - 暂无邮件
2025-07-31 07:46:48,262 - INFO - 🔄 点击刷新邮件按钮 (总计第20次，代理切换计数第20次) - 当前邮箱: <EMAIL>
2025-07-31 07:46:48,262 - INFO - 已刷新20次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:46:48,270 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:46:48,274 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:46:48,274 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:46:48,274 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:46:50,320 - INFO - 暂无邮件
2025-07-31 07:46:50,320 - INFO - 已刷新 20 次，继续等待邮件...
2025-07-31 07:46:52,736 - INFO - 用户中断等待
2025-07-31 07:46:52,737 - WARNING - 第 2 个邮箱处理失败或被中断
2025-07-31 07:46:52,738 - INFO - 🎉 自动化完成！共成功处理 1 个邮箱，目标: 10 个
2025-07-31 07:46:56,183 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)': /session/5c1bce1e5cf43855a0eb0dc55f703257/url
2025-07-31 07:48:31,588 - INFO - 启用无头浏览器模式（后台运行）
2025-07-31 07:48:33,105 - INFO - 浏览器启动成功
2025-07-31 07:48:34,686 - INFO - 正在加载 tempmail.la...
2025-07-31 07:48:34,807 - INFO - tempmail.la 页面加载完成
2025-07-31 07:48:34,875 - INFO - 点击创建临时邮箱按钮
2025-07-31 07:48:36,413 - INFO - 检测到临时邮箱创建成功弹窗
2025-07-31 07:48:36,414 - INFO - 开始监控新邮箱请求...
2025-07-31 07:48:36,414 - INFO - 开始监控注册状态...
2025-07-31 07:48:36,414 - INFO - 启动时换一次新邮箱...
2025-07-31 07:48:36,414 - INFO - 检测到注册脚本获取了新邮箱: <EMAIL>
2025-07-31 07:48:36,414 - INFO - 🔄 正在切换邮箱...
2025-07-31 07:48:36,415 - INFO - 等待注册完成后再切换邮箱...
2025-07-31 07:48:36,464 - INFO - ✅ 通过方法2找到换邮箱按钮
2025-07-31 07:48:36,465 - INFO - 已清空验证码文件
2025-07-31 07:48:36,558 - INFO - ✅ 成功点击换邮箱按钮
2025-07-31 07:48:39,572 - INFO - 正在获取邮箱地址...
2025-07-31 07:48:41,636 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:48:41,636 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:48:41,644 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:48:41,654 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:48:41,654 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:48:41,656 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:48:41,657 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:48:41,657 - INFO - ✅ 新邮箱地址已生成: <EMAIL>
2025-07-31 07:48:41,657 - INFO - 正在获取邮箱地址...
2025-07-31 07:48:43,709 - INFO - 成功获取邮箱地址: <EMAIL>
2025-07-31 07:48:43,714 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:48:43,714 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:48:43,721 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:48:43,721 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:48:43,722 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:48:43,722 - INFO - 邮箱地址已写入文件并复制到剪贴板: <EMAIL> (刷新计数: 0)
2025-07-31 07:48:43,722 - INFO - 启动时已换新邮箱: <EMAIL>
2025-07-31 07:48:43,723 - INFO - 📍 主循环开始 - 第 1/10 个邮箱处理，当前邮箱: <EMAIL>
2025-07-31 07:48:43,723 - INFO - 🔄 开始等待邮件: <EMAIL>
2025-07-31 07:48:43,723 - INFO - 📧 开始持续刷新等待邮件: <EMAIL>
2025-07-31 07:48:43,723 - INFO - 🔄 当前刷新计数: 0, 代理切换计数: 0
2025-07-31 07:48:43,797 - INFO - 🔄 点击刷新邮件按钮 (总计第1次，代理切换计数第1次) - 当前邮箱: <EMAIL>
2025-07-31 07:48:45,825 - INFO - 暂无邮件
2025-07-31 07:48:48,992 - INFO - 🔄 点击刷新邮件按钮 (总计第2次，代理切换计数第2次) - 当前邮箱: <EMAIL>
2025-07-31 07:48:51,063 - INFO - 暂无邮件
2025-07-31 07:48:54,204 - INFO - 🔄 点击刷新邮件按钮 (总计第3次，代理切换计数第3次) - 当前邮箱: <EMAIL>
2025-07-31 07:48:56,238 - INFO - 暂无邮件
2025-07-31 07:48:59,401 - INFO - 🔄 点击刷新邮件按钮 (总计第4次，代理切换计数第4次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:01,423 - INFO - 暂无邮件
2025-07-31 07:49:04,524 - INFO - 🔄 点击刷新邮件按钮 (总计第5次，代理切换计数第5次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:06,586 - INFO - 暂无邮件
2025-07-31 07:49:09,654 - INFO - 🔄 点击刷新邮件按钮 (总计第6次，代理切换计数第6次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:11,728 - INFO - 暂无邮件
2025-07-31 07:49:14,800 - INFO - 🔄 点击刷新邮件按钮 (总计第7次，代理切换计数第7次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:16,828 - INFO - 暂无邮件
2025-07-31 07:49:19,898 - INFO - 🔄 点击刷新邮件按钮 (总计第8次，代理切换计数第8次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:21,922 - INFO - 暂无邮件
2025-07-31 07:49:24,981 - INFO - 🔄 点击刷新邮件按钮 (总计第9次，代理切换计数第9次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:27,004 - INFO - 暂无邮件
2025-07-31 07:49:30,075 - INFO - 🔄 点击刷新邮件按钮 (总计第10次，代理切换计数第10次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:30,075 - INFO - 已刷新10次，重新复制邮箱地址到剪贴板并更新文件
2025-07-31 07:49:30,081 - INFO - 方法1: 通过pyperclip复制邮箱地址: <EMAIL>
2025-07-31 07:49:30,086 - INFO - 方法2: 通过浏览器Clipboard API复制邮箱地址: <EMAIL>
2025-07-31 07:49:30,086 - INFO - ✅ 邮箱地址已成功复制到剪切板: <EMAIL>
2025-07-31 07:49:30,087 - INFO - 邮箱地址已写入文件: <EMAIL>
2025-07-31 07:49:32,114 - INFO - 暂无邮件
2025-07-31 07:49:32,115 - INFO - 已刷新 10 次，继续等待邮件...
2025-07-31 07:49:35,186 - INFO - 🔄 点击刷新邮件按钮 (总计第11次，代理切换计数第11次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:37,220 - INFO - 暂无邮件
2025-07-31 07:49:40,295 - INFO - 🔄 点击刷新邮件按钮 (总计第12次，代理切换计数第12次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:42,329 - INFO - 暂无邮件
2025-07-31 07:49:45,401 - INFO - 🔄 点击刷新邮件按钮 (总计第13次，代理切换计数第13次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:47,425 - INFO - 暂无邮件
2025-07-31 07:49:50,489 - INFO - 🔄 点击刷新邮件按钮 (总计第14次，代理切换计数第14次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:52,519 - INFO - 暂无邮件
2025-07-31 07:49:55,642 - INFO - 🔄 点击刷新邮件按钮 (总计第15次，代理切换计数第15次) - 当前邮箱: <EMAIL>
2025-07-31 07:49:57,711 - INFO - 暂无邮件
2025-07-31 07:50:00,788 - INFO - 🔄 点击刷新邮件按钮 (总计第16次，代理切换计数第16次) - 当前邮箱: <EMAIL>
2025-07-31 07:50:02,852 - INFO - 暂无邮件
2025-07-31 07:50:05,985 - INFO - 🔄 点击刷新邮件按钮 (总计第17次，代理切换计数第17次) - 当前邮箱: <EMAIL>
2025-07-31 07:50:08,020 - INFO - 暂无邮件
2025-07-31 07:50:11,119 - INFO - 🔄 点击刷新邮件按钮 (总计第18次，代理切换计数第18次) - 当前邮箱: <EMAIL>
2025-07-31 07:50:13,160 - INFO - 暂无邮件
2025-07-31 07:50:16,231 - INFO - 🔄 点击刷新邮件按钮 (总计第19次，代理切换计数第19次) - 当前邮箱: <EMAIL>
2025-07-31 07:50:18,266 - INFO - 暂无邮件
2025-07-31 07:50:20,793 - INFO - 用户中断等待
2025-07-31 07:50:20,793 - WARNING - 第 1 个邮箱处理失败或被中断
2025-07-31 07:50:20,794 - INFO - 🎉 自动化完成！共成功处理 0 个邮箱，目标: 10 个
