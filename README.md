# 临时邮箱自动化脚本

这个脚本可以自动化操作 tmpmails.com 网站，获取临时邮箱地址，等待邮件，并提取其中的链接。

## 功能特点

- 自动打开浏览器访问 https://tmpmails.com/zh
- 获取临时邮箱地址并自动复制到剪贴板
- 持续刷新直到收到邮件（不限时间）
- 自动点击邮件并提取其中的 app.bfl.ai 链接
- 智能关闭邮件窗口避免界面冲突
- 保存邮箱地址和链接到文件
- 自动生成新邮箱地址并重复循环

## 安装依赖

1. 确保已安装 Python 3.7+
2. 安装 Chrome 浏览器
3. 安装依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

运行脚本：

```bash
python email_automation.py
```

程序会提示你输入：
- 循环次数（默认10次）

注意：程序会持续刷新直到收到邮件，不再有时间限制。

## 输出文件

脚本会生成以下文件：

- `email_results.json` - JSON格式的结果文件
- `email_results.txt` - 文本格式的结果文件
- `email_automation.log` - 运行日志

## 注意事项

1. 需要安装 Chrome 浏览器
2. 确保网络连接正常
3. 可以随时按 Ctrl+C 停止程序
4. 脚本会自动处理页面加载等待时间

## 结果格式

每个结果包含：
- 时间戳
- 邮箱地址
- 提取的链接

示例：
```
2024-01-01T12:00:00 | <EMAIL> | https://app.bfl.ai/auth/v1/verify?token=...
```
