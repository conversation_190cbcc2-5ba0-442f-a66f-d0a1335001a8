// ==UserScript==
// @name         自动注册脚本
// @namespace    http://tampermonkey.net/
// @version      3.4
// @description  自动填写注册表单，后台静默运行，等待剪贴板邮箱更新
// <AUTHOR>
// @match        https://auth.bfl.ai/register*
// @match        https://auth.bfl.ai/register
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置区域 - 可以修改这些值
    const CONFIG = {
        password: 'MySecurePassword123!',  // 请修改为你想要的密码
        confirmPassword: 'MySecurePassword123!',  // 确认密码（应该与上面相同）
        autoSubmit: true,  // 是否自动提交表单
        autoCheckTerms: true,  // 是否自动勾选条款复选框
        autoRefreshOnSuccess: true,  // 是否在注册成功后自动刷新页面
        autoFillEmail: true,  // 是否自动填写剪贴板中的邮箱
        delay: 1500  // 填写间隔时间（毫秒）
    };

    // 全局变量
    let lastClipboardContent = '';
    let lastEmailFilled = '';
    let registrationSuccessDetected = false;
    let waitingForNewEmail = false;
    let responseTimer = null;  // 响应超时计时器
    let registrationEmailSentDetected = false;  // 是否检测到注册邮件发送成功提示
    let alertDetected = false;  // 是否检测到任何提示框
    let isSubmitting = false;  // 防止重复提交的标志
    let lastSubmitTime = 0;  // 上次提交时间
    
    console.log('🚀 开始自动填写密码...');
    console.log('💡 邮箱输入已优化为后台兼容模式，支持窗口切换！');
    
    // 等待页面加载完成
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素未找到: ${selector}`));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }
    
    // 模拟真实用户输入 - 逐字符输入（后台兼容版本）
    function simulateTyping(element, text) {
        return new Promise((resolve) => {
            let index = 0;
            element.value = '';

            // 尝试聚焦，但不依赖于它
            try {
                element.focus();
            } catch (e) {
                console.log('💡 无法聚焦元素，使用后台输入模式');
            }

            function typeNextChar() {
                if (index < text.length) {
                    const char = text[index];
                    element.value += char;

                    // 触发各种事件
                    element.dispatchEvent(new KeyboardEvent('keydown', {
                        bubbles: true,
                        key: char,
                        code: `Key${char.toUpperCase()}`,
                        keyCode: char.charCodeAt(0)
                    }));

                    element.dispatchEvent(new KeyboardEvent('keypress', {
                        bubbles: true,
                        key: char,
                        charCode: char.charCodeAt(0)
                    }));

                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new InputEvent('input', {
                        bubbles: true,
                        inputType: 'insertText',
                        data: char
                    }));

                    element.dispatchEvent(new KeyboardEvent('keyup', {
                        bubbles: true,
                        key: char,
                        code: `Key${char.toUpperCase()}`,
                        keyCode: char.charCodeAt(0)
                    }));

                    index++;
                    setTimeout(typeNextChar, 50); // 50ms间隔模拟真实输入
                } else {
                    // 输入完成后触发change和blur事件
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    setTimeout(() => {
                        element.dispatchEvent(new Event('blur', { bubbles: true }));
                        resolve();
                    }, 100);
                }
            }

            typeNextChar();
        });
    }

    // 后台兼容的直接输入方法
    function directInput(element, value) {
        return new Promise((resolve) => {
            console.log(`🔧 使用直接输入模式: ${element.name || element.id}`);

            // 清空现有内容
            element.value = '';

            // 使用多种方法设置值
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
            nativeInputValueSetter.call(element, value);

            // 触发所有必要的事件
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new InputEvent('input', {
                bubbles: true,
                inputType: 'insertText',
                data: value
            }));

            // React特殊处理
            if (element._valueTracker) {
                element._valueTracker.setValue('');
            }

            setTimeout(() => {
                // 再次确保值被设置
                if (element.value !== value) {
                    element.value = value;
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                }
                resolve();
            }, 100);
        });
    }

    // 启动响应超时计时器
    function startResponseTimer() {
        // 清除之前的计时器
        if (responseTimer) {
            clearTimeout(responseTimer);
        }

        console.log('⏰ 启动30秒响应超时计时器...');

        responseTimer = setTimeout(() => {
            console.log('⏰ 30秒内未收到响应，尝试跳转到注册页面...');

            // 检查当前是否已经在注册页面
            if (window.location.href.includes('auth.bfl.ai/register')) {
                console.log('🔄 当前已在注册页面，刷新页面重试...');
                location.reload();
            } else {
                console.log('🔄 跳转到注册页面...');
                window.location.href = 'https://auth.bfl.ai/register';
            }
        }, 30000); // 30秒
    }

    // 停止响应超时计时器
    function stopResponseTimer() {
        if (responseTimer) {
            console.log('⏰ 收到响应，停止超时计时器');
            clearTimeout(responseTimer);
            responseTimer = null;
        }
    }

    // 填写输入框 - 后台兼容版，支持窗口切换
    async function fillInput(element, value) {
        if (!element) {
            console.error('❌ 元素不存在');
            return false;
        }

        console.log(`🔧 正在填写: ${element.name || element.id}`);

        // 检测是否为邮箱输入框
        const isEmailInput = element.type === 'email' ||
                            element.name === 'email' ||
                            element.id.includes('email');

        if (isEmailInput) {
            console.log('📧 检测到邮箱输入框，使用后台兼容模式');
            // 对于邮箱输入框，使用直接输入方法，不依赖窗口焦点
            await directInput(element, value);
        } else {
            console.log('🔑 检测到密码输入框，使用标准模式');
            // 对于密码等其他输入框，使用标准方法
            try {
                // 尝试滚动到元素位置（如果窗口可见）
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                await new Promise(resolve => setTimeout(resolve, 300));
            } catch (e) {
                console.log('💡 无法滚动到元素，继续使用后台模式');
            }

            // 尝试聚焦和点击（如果窗口可见）
            try {
                element.focus();
                element.click();
                element.dispatchEvent(new Event('focus', { bubbles: true }));
                element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
            } catch (e) {
                console.log('💡 无法聚焦元素，使用后台输入模式');
            }

            // 清空现有内容
            element.value = '';
            try {
                element.select();
            } catch (e) {
                // 忽略选择错误
            }

            // 等待一下
            await new Promise(resolve => setTimeout(resolve, 200));

            // 使用模拟输入
            await simulateTyping(element, value);
        }

        // React特殊处理
        if (element._valueTracker) {
            element._valueTracker.setValue('');
        }

        // 强制设置值（备用方案）
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
        nativeInputValueSetter.call(element, value);
        element.dispatchEvent(new Event('input', { bubbles: true }));

        // 验证是否填写成功
        setTimeout(() => {
            if (element.value === value) {
                console.log(`✅ 成功填写: ${element.name || element.id} = "${value}"`);
            } else {
                console.warn(`⚠️ 填写失败: ${element.name || element.id}, 当前值: "${element.value}"`);
                console.log('🔄 尝试强制设置...');

                // 最后的强制设置
                element.value = value;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));

                setTimeout(() => {
                    console.log(`🔍 最终检查: ${element.name || element.id} = "${element.value}"`);
                }, 100);
            }
        }, 500);

        return true;
    }

    // 勾选复选框
    function checkTermsCheckbox() {
        console.log('📋 正在查找条款复选框...');

        // 多种方式查找复选框
        const checkbox = document.querySelector('#terms') ||
                        document.querySelector('button[role="checkbox"]') ||
                        document.querySelector('input[type="checkbox"]') ||
                        document.querySelector('button[id*="terms"]') ||
                        document.querySelector('button[aria-checked="false"]');

        if (checkbox) {
            console.log('🔍 找到条款复选框');

            // 检查是否已经选中
            const isChecked = checkbox.getAttribute('aria-checked') === 'true' ||
                             checkbox.getAttribute('data-state') === 'checked' ||
                             checkbox.checked === true;

            if (!isChecked) {
                console.log('☑️ 正在勾选条款复选框...');

                // 点击复选框
                checkbox.click();
                checkbox.focus();

                // 触发各种事件
                checkbox.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                checkbox.dispatchEvent(new Event('input', { bubbles: true }));

                // 验证是否成功勾选
                setTimeout(() => {
                    const newState = checkbox.getAttribute('aria-checked') === 'true' ||
                                   checkbox.getAttribute('data-state') === 'checked' ||
                                   checkbox.checked === true;

                    if (newState) {
                        console.log('✅ 成功勾选条款复选框');
                    } else {
                        console.warn('⚠️ 条款复选框可能未成功勾选');
                    }
                }, 200);

                return true;
            } else {
                console.log('✅ 条款复选框已经勾选');
                return true;
            }
        } else {
            console.warn('⚠️ 未找到条款复选框');
            return false;
        }
    }

    // 点击注册按钮
    async function clickRegisterButton() {
        // 防止重复提交检查
        const currentTime = Date.now();
        if (isSubmitting) {
            console.log('⚠️ 正在提交中，跳过重复点击...');
            return false;
        }

        if (currentTime - lastSubmitTime < 5000) {  // 5秒内不允许重复提交
            console.log('⚠️ 距离上次提交不足5秒，跳过重复点击...');
            return false;
        }

        console.log('🔍 正在查找注册按钮...');

        // 多种方式查找注册按钮
        const registerButton = document.querySelector('button[type="submit"]') ||
                              document.querySelector('button:contains("Register")') ||
                              document.querySelector('button:contains("注册")') ||
                              document.querySelector('button[class*="bg-primary"]') ||
                              document.querySelector('input[type="submit"]') ||
                              document.querySelector('button[class*="submit"]');

        if (registerButton) {
            console.log('🔍 找到注册按钮');

            // 检查按钮是否可用
            const isDisabled = registerButton.disabled ||
                              registerButton.getAttribute('disabled') !== null ||
                              registerButton.classList.contains('disabled');

            if (isDisabled) {
                console.warn('⚠️ 注册按钮当前不可用，等待1秒后重试...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                return await clickRegisterButton(); // 递归重试
            }

            // 设置提交状态
            isSubmitting = true;
            lastSubmitTime = currentTime;
            console.log('🚀 正在点击注册按钮...');

            // 滚动到按钮位置
            registerButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await new Promise(resolve => setTimeout(resolve, 300));

            // 聚焦按钮
            registerButton.focus();

            // 点击按钮（只使用一种方式避免重复点击）
            registerButton.click();

            console.log('✅ 注册按钮已点击，等待提交结果...');

            // 验证是否成功提交 - 延长等待时间并改进检测逻辑
            setTimeout(() => {
                // 检查是否出现了提醒框（成功或错误）
                const alertBox = document.querySelector('div[role="alert"]') ||
                               document.querySelector('.alert') ||
                               document.querySelector('[class*="alert"]');

                // 检查是否有加载状态
                const loadingElement = document.querySelector('.loading') ||
                                     document.querySelector('[class*="loading"]') ||
                                     document.querySelector('[class*="spinner"]');

                // 检查按钮状态是否改变
                const submitBtn = document.querySelector('button[type="submit"]');
                const buttonDisabled = submitBtn && (submitBtn.disabled || submitBtn.textContent.includes('...'));

                if (alertBox || loadingElement || buttonDisabled) {
                    console.log('✅ 表单提交成功，正在处理中...');
                } else {
                    console.log('🔍 表单已提交，等待服务器响应...');

                    // 再等待3秒检查
                    setTimeout(() => {
                        const laterAlert = document.querySelector('div[role="alert"]');
                        if (laterAlert) {
                            console.log('✅ 检测到服务器响应！');
                        } else {
                            console.warn('⚠️ 长时间未收到响应，可能需要检查网络或手动确认');
                        }
                    }, 3000);
                }

                // 5秒后重置提交状态，允许下次提交
                setTimeout(() => {
                    isSubmitting = false;
                    console.log('🔄 重置提交状态，允许下次提交');
                }, 5000);
            }, 2000);

            return true;
        } else {
            console.error('❌ 未找到注册按钮');

            // 重置提交状态
            isSubmitting = false;

            // 调试：显示页面上所有按钮
            const allButtons = document.querySelectorAll('button');
            console.log('🔍 页面上的所有按钮:', allButtons.length);
            allButtons.forEach((btn, index) => {
                console.log(`按钮 ${index + 1}:`, {
                    text: btn.textContent.trim(),
                    type: btn.type,
                    className: btn.className,
                    disabled: btn.disabled
                });
            });

            return false;
        }
    }

    // 检测注册成功提醒和错误处理
    function checkRegistrationSuccess() {
        console.log('🔍 开始监控注册成功提醒和错误提醒...');
        console.log('💡 400错误和ReCaptcha问题会自动刷新页面');
        console.log('💡 500错误等服务器问题会继续等待，不刷新页面');

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    // 查找提醒框
                    const alertElements = document.querySelectorAll('div[role="status"], li[role="status"], div[class*="toast"], li[class*="toast"]') ||
                                        document.querySelectorAll('div[role="alert"], .alert, [class*="alert"]');

                    alertElements.forEach((alertElement) => {
                        if (alertElement) {
                            const alertText = alertElement.textContent || alertElement.innerText;

                            // 首先检查是否包含注册邮件发送成功的消息
                            if (alertText.includes('Registration email sent') ||
                                alertText.includes('please check your inbox') ||
                                alertText.includes('confirm your email') ||
                                alertText.includes('proceed to login')) {

                                console.log('✅ 检测到注册邮件发送成功提示!');
                                console.log('📧 提醒内容:', alertText);
                                console.log('🛑 停止所有自动刷新行为，等待新邮箱复制...');
                                console.log('💡 请复制新的邮箱地址，脚本将在检测到新邮箱后自动刷新页面');

                                // 设置标志，停止所有自动刷新
                                registrationEmailSentDetected = true;
                                waitingForNewEmail = true;

                                // 停止观察
                                observer.disconnect();
                                return;
                            }

                            // 如果已经检测到注册邮件发送成功，不再处理任何错误刷新
                            if (registrationEmailSentDetected) {
                                console.log('💡 已检测到注册成功，忽略后续错误提示，等待新邮箱复制...');
                                return;
                            }

                            // 检查是否包含400错误、429错误或reCAPTCHA问题（延迟刷新，等待可能的成功提示）
                            if (alertText.includes('400') ||
                                alertText.includes('429') ||
                                alertText.includes('per 1 hour') ||
                                alertText.includes('ReCaptcha detected suspicious activity') ||
                                alertText.includes('suspicious activity') ||
                                alertText.includes('reCAPTCHA not loaded') ||
                                alertText.includes('Please try again later')) {

                                console.log('❌ 检测到400/429错误或ReCaptcha问题!');
                                console.log('📧 错误内容:', alertText);
                                console.log('⏳ 等待10秒，检查是否会出现成功提示...');

                                // 等待10秒，检查是否会出现成功提示
                                setTimeout(() => {
                                    // 再次检查是否有成功提示
                                    const successAlert = document.querySelector('div[role="alert"]');
                                    if (successAlert) {
                                        const successText = successAlert.textContent || successAlert.innerText;
                                        if (successText.includes('Registration email sent') ||
                                            successText.includes('please check your inbox')) {
                                            console.log('✅ 等待期间检测到成功提示，停止刷新!');
                                            registrationEmailSentDetected = true;
                                            waitingForNewEmail = true;
                                            return;
                                        }
                                    }

                                    // 如果没有成功提示，才刷新页面
                                    if (!registrationEmailSentDetected) {
                                        console.log('🔄 等待期间未检测到成功提示，现在刷新页面...');
                                        location.reload();
                                    }
                                }, 10000); // 等待10秒

                                // 不立即停止观察，继续监控可能的成功提示
                                return;
                            }

                            // 检查是否包含500错误（特殊处理）
                            if (alertText.includes('500')) {
                                console.log('⚠️ 检测到500服务器错误!');
                                console.log('📧 错误内容:', alertText);
                                console.log('⏳ 等待10秒后重新点击注册按钮...');

                                // 重置提交状态，允许重试
                                isSubmitting = false;

                                // 等待10秒后重新点击注册按钮
                                setTimeout(async () => {
                                    console.log('🔄 10秒已过，尝试重新点击注册按钮...');

                                    // 检查是否还在提交状态，如果是则跳过
                                    if (isSubmitting) {
                                        console.log('💡 检测到正在提交中，跳过500错误重试');
                                        return;
                                    }

                                    const success = await clickRegisterButton();

                                    if (success) {
                                        console.log('✅ 已重新点击注册按钮，等待响应...');

                                        // 再等待10秒检查是否还是500错误
                                        setTimeout(() => {
                                            const errorAlert = document.querySelector('div[role="alert"]');
                                            if (errorAlert) {
                                                const errorText = errorAlert.textContent || errorAlert.innerText;
                                                if (errorText.includes('500')) {
                                                    console.log('❌ 重试后仍然是500错误，等待10秒后刷新页面...');
                                                    setTimeout(() => {
                                                        console.log('🔄 正在刷新页面...');
                                                        location.reload();
                                                    }, 10000); // 再等待10秒后刷新
                                                } else if (errorText.includes('Registration email sent') ||
                                                          errorText.includes('please check your inbox')) {
                                                    console.log('✅ 重试成功！检测到注册邮件发送成功');
                                                    registrationEmailSentDetected = true;
                                                    waitingForNewEmail = true;
                                                } else {
                                                    console.log('💡 重试后收到其他响应，继续监控...');
                                                }
                                            } else {
                                                console.log('💡 重试后暂无响应，继续等待...');
                                            }
                                        }, 10000); // 等待10秒检查重试结果
                                    } else {
                                        console.log('❌ 重新点击注册按钮失败，等待10秒后刷新页面...');
                                        setTimeout(() => {
                                            console.log('🔄 正在刷新页面...');
                                            location.reload();
                                        }, 10000);
                                    }
                                }, 10000); // 等待10秒后重试

                                // 不停止观察，继续监控
                                return;
                            }

                            // 检查是否包含其他错误信息（不刷新页面，继续等待）
                            if (alertText.includes('Registration failed') ||
                                alertText.includes('error') ||
                                alertText.includes('failed')) {

                                console.log('⚠️ 检测到其他服务器错误，继续等待...');
                                console.log('📧 错误内容:', alertText);
                                console.log('💡 不刷新页面，等待服务器恢复或手动处理');

                                // 不停止观察，继续监控
                                return;
                            }
                        }
                    });
                }
            });
        });

        // 开始观察DOM变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 也检查当前页面是否已经有提醒
        setTimeout(() => {
            const existingAlert = document.querySelector('div[role="alert"]');
            if (existingAlert) {
                const alertText = existingAlert.textContent || existingAlert.innerText;
                if (alertText.includes('Registration email sent') ||
                    alertText.includes('please check your inbox')) {
                    console.log('✅ 页面已存在注册成功提醒!');
                    if (CONFIG.autoRefreshOnSuccess) {
                        console.log('🔄 即将刷新页面...');
                        setTimeout(() => location.reload(), 2000);
                    }
                }
            }
        }, 1000);
    }

    // 检测剪贴板中的邮箱并自动填写
    async function checkClipboardForEmail() {
        if (!CONFIG.autoFillEmail) return;

        try {
            // 读取剪贴板内容
            const clipboardText = await navigator.clipboard.readText();

            // 检查是否是新的内容
            if (clipboardText === lastClipboardContent) {
                return;
            }

            // 更新最后的剪贴板内容
            lastClipboardContent = clipboardText;

            // 检查是否是邮箱格式，支持任何邮箱域名
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            // 任何符合邮箱格式的地址都被接受
            if (emailRegex.test(clipboardText)) {
                // 检查是否是新的邮箱（避免重复填写）
                if (clipboardText !== lastEmailFilled) {
                    console.log('📧 检测到新的邮箱地址:', clipboardText);

                    // 如果已经有邮箱填写过，说明这是新的邮箱
                    if (lastEmailFilled !== '') {
                        console.log('🔄 检测到新邮箱，立即刷新页面开始新的注册...');

                        // 重置状态
                        registrationSuccessDetected = false;
                        waitingForNewEmail = false;
                        registrationEmailSentDetected = false;  // 重置注册邮件发送检测状态
                        lastEmailFilled = '';

                        // 立即刷新页面
                        location.reload();
                        return;
                    }

                    // 如果检测到注册邮件发送成功，但还没有填写过邮箱，说明这是第一次填写
                    if (registrationEmailSentDetected && lastEmailFilled === '') {
                        console.log('� 注册成功后检测到新邮箱，立即刷新页面开始新的注册...');

                        // 重置状态
                        registrationSuccessDetected = false;
                        waitingForNewEmail = false;
                        registrationEmailSentDetected = false;
                        lastEmailFilled = '';

                        // 立即刷新页面
                        location.reload();
                        return;
                    }

                    // 查找邮箱输入框
                    const emailInput = document.querySelector('input[name="email"]') ||
                                      document.querySelector('input[type="email"]') ||
                                      document.querySelector('input[id*="email"]');

                    if (emailInput) {
                        console.log('📝 正在填写邮箱...');

                        // 填写邮箱前检测其他表单是否已填写
                        const formsAlreadyFilled = checkOtherFormsStatus();

                        await fillInput(emailInput, clipboardText);
                        lastEmailFilled = clipboardText;
                        console.log('✅ 邮箱填写完成!');

                        // 根据其他表单状态决定后续操作
                        if (formsAlreadyFilled) {
                            console.log('🔍 检测到其他表单已填写，补充邮箱后直接点击注册按钮');
                            // 等待一下确保邮箱填写完成
                            setTimeout(async () => {
                                // 检查是否正在提交中
                                if (!isSubmitting) {
                                    await clickRegisterButton();
                                } else {
                                    console.log('💡 检测到正在提交中，跳过剪贴板触发的提交');
                                }
                            }, 1000);
                        } else {
                            console.log('🔍 检测到其他表单未填写，邮箱填写完成，等待用户手动操作或自动填写流程');
                        }
                    } else {
                        console.log('⚠️ 未找到邮箱输入框');
                    }
                } else {
                    console.log('📧 邮箱已填写过，跳过:', clipboardText);
                }
            } else {
                // 静默等待期间不输出非邮箱内容日志
                if (!waitingForNewEmail) {
                    console.log('📋 剪贴板内容不是邮箱格式:', clipboardText.substring(0, 50));
                }
            }

        } catch (error) {
            // 静默处理剪贴板权限错误
            if (error.name !== 'NotAllowedError') {
                console.log('📋 剪贴板读取失败:', error.message);
            }
        }
    }

    // 启动剪贴板监控
    function startClipboardMonitoring() {
        if (!CONFIG.autoFillEmail) return;

        console.log('📋 开始监控剪贴板邮箱地址...');

        // 每2秒检查一次剪贴板
        setInterval(checkClipboardForEmail, 2000);

        // 页面获得焦点时也检查一次
        window.addEventListener('focus', () => {
            setTimeout(checkClipboardForEmail, 500);
        });

        // 立即检查一次，然后定期检查
        setTimeout(checkClipboardForEmail, 500);
        setTimeout(checkClipboardForEmail, 1500);
        setTimeout(checkClipboardForEmail, 3000);
    }

    // 检测其他表单是否已填写
    function checkOtherFormsStatus() {
        console.log('🔍 检测其他表单填写状态...');

        // 检查密码输入框
        const passwordInput = document.querySelector('input[name="password"]') ||
                             document.querySelector('input[type="password"]:first-of-type') ||
                             document.querySelector('input[id*="password"]:not([name*="confirm"])');

        // 检查确认密码输入框
        const confirmPasswordInput = document.querySelector('input[name="confirmPassword"]') ||
                                    document.querySelector('input[name="confirm_password"]') ||
                                    document.querySelector('input[type="password"]:last-of-type') ||
                                    document.querySelector('input[id*="confirm"]');

        // 检查条款复选框
        const termsCheckbox = document.querySelector('#terms') ||
                             document.querySelector('button[role="checkbox"]') ||
                             document.querySelector('input[type="checkbox"]') ||
                             document.querySelector('button[id*="terms"]') ||
                             document.querySelector('button[aria-checked="false"]') ||
                             document.querySelector('button[aria-checked="true"]');

        // 检查密码是否已填写
        const passwordFilled = passwordInput && passwordInput.value && passwordInput.value.trim() !== '';

        // 检查确认密码是否已填写
        const confirmPasswordFilled = confirmPasswordInput && confirmPasswordInput.value && confirmPasswordInput.value.trim() !== '';

        // 检查条款是否已勾选
        const termsChecked = termsCheckbox && (
            termsCheckbox.getAttribute('aria-checked') === 'true' ||
            termsCheckbox.getAttribute('data-state') === 'checked' ||
            termsCheckbox.checked === true
        );

        console.log('📋 表单状态检测结果:');
        console.log('- 密码已填写:', passwordFilled);
        console.log('- 确认密码已填写:', confirmPasswordFilled);
        console.log('- 条款已勾选:', termsChecked);

        // 如果密码和确认密码都已填写，且条款已勾选，则认为其他表单已完成
        const otherFormsCompleted = passwordFilled && confirmPasswordFilled && termsChecked;

        console.log('📝 其他表单完成状态:', otherFormsCompleted);

        return otherFormsCompleted;
    }

    // 检查页面内容是否加载完成
    function checkPageContent() {
        console.log('🔍 检查页面内容是否完整加载...');

        // 如果已经检测到注册邮件发送成功，不进行页面内容检查
        if (registrationEmailSentDetected) {
            console.log('💡 已检测到注册成功，跳过页面内容检查');
            return true;
        }

        // 检查是否有基本的表单元素
        const hasEmailInput = document.querySelector('input[name="email"]') ||
                             document.querySelector('input[type="email"]') ||
                             document.querySelector('input[id*="email"]');

        const hasPasswordInput = document.querySelector('input[name="password"]') ||
                                document.querySelector('input[type="password"]');

        const hasRegisterButton = document.querySelector('button[type="submit"]') ||
                                 document.querySelector('button:contains("Register")') ||
                                 document.querySelector('button:contains("注册")');

        // 检查页面是否有基本内容
        const hasContent = document.body.textContent.trim().length > 100;

        if (!hasEmailInput || !hasPasswordInput || !hasRegisterButton || !hasContent) {
            console.log('❌ 页面内容不完整或为空，缺少必要元素:');
            console.log('- 邮箱输入框:', !!hasEmailInput);
            console.log('- 密码输入框:', !!hasPasswordInput);
            console.log('- 注册按钮:', !!hasRegisterButton);
            console.log('- 页面内容:', hasContent);
            console.log('🔄 3秒后刷新页面重新加载...');

            setTimeout(() => {
                console.log('🔄 正在刷新页面...');
                location.reload();
            }, 3000);

            return false;
        }

        console.log('✅ 页面内容检查通过，所有必要元素都存在');
        return true;
    }

    // 主要执行函数
    async function autoFillPasswords() {
        try {
            // 检查是否在正确的页面
            if (!window.location.href.includes('auth.bfl.ai/register')) {
                console.warn('⚠️ 当前不在注册页面，请先导航到 https://auth.bfl.ai/register');
                return;
            }

            // 检查页面内容是否完整
            if (!checkPageContent()) {
                return; // 页面内容不完整，等待刷新
            }

            // 首先尝试填写剪贴板中的邮箱
            console.log('📧 正在检查剪贴板中的邮箱...');

            // 多次尝试读取剪贴板，因为页面刚刷新时可能需要时间
            let clipboardText = '';
            let emailFilled = false;

            for (let attempt = 0; attempt < 3; attempt++) {
                try {
                    clipboardText = await navigator.clipboard.readText();
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    const isTmpMail = clipboardText.includes('@tmpmails.com');

                    if (emailRegex.test(clipboardText) && isTmpMail) {
                        console.log('📧 检测到剪贴板中的邮箱:', clipboardText);

                        // 查找邮箱输入框
                        const emailInput = document.querySelector('input[name="email"]') ||
                                          document.querySelector('input[type="email"]') ||
                                          document.querySelector('input[id*="email"]');

                        if (emailInput && !emailInput.value) {
                            console.log('📝 正在填写邮箱...');

                            // 填写邮箱前检测其他表单是否已填写
                            const formsAlreadyFilled = checkOtherFormsStatus();

                            await fillInput(emailInput, clipboardText);
                            lastEmailFilled = clipboardText;
                            lastClipboardContent = clipboardText; // 同步更新剪贴板内容记录
                            console.log('✅ 邮箱填写完成!');
                            emailFilled = true;

                            // 根据其他表单状态决定后续操作
                            if (formsAlreadyFilled) {
                                console.log('🔍 检测到其他表单已填写，补充邮箱后直接点击注册按钮');
                                // 等待一下确保邮箱填写完成
                                await new Promise(resolve => setTimeout(resolve, 1000));
                                // 检查是否正在提交中
                                if (!isSubmitting) {
                                    // 直接点击注册按钮
                                    await clickRegisterButton();
                                } else {
                                    console.log('💡 检测到正在提交中，跳过主函数触发的提交');
                                }
                                return; // 直接返回，不继续执行后续的密码填写
                            } else {
                                console.log('🔍 检测到其他表单未填写，将继续填写所有表单');
                                // 等待一下再继续填写密码
                                await new Promise(resolve => setTimeout(resolve, 1000));
                                break;
                            }
                        } else if (emailInput && emailInput.value) {
                            console.log('📧 邮箱输入框已有内容，跳过填写');
                            emailFilled = true;
                            break;
                        } else {
                            console.log('⚠️ 未找到邮箱输入框');
                        }
                    } else {
                        console.log(`📧 剪贴板中没有有效的tmpmails.com邮箱 (尝试 ${attempt + 1}/3)`);
                        if (attempt < 2) {
                            // 等待一下再重试，给剪贴板更新时间
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    }
                } catch (error) {
                    console.log(`📋 读取剪贴板失败 (尝试 ${attempt + 1}/3):`, error.message);
                    if (attempt < 2) {
                        // 等待一下再重试
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            if (!emailFilled) {
                console.log('⚠️ 未能从剪贴板填写邮箱，将只填写密码');
            }

            console.log('📍 正在查找密码输入框...');

            // 多种方式查找密码输入框
            let passwordInput = document.querySelector('input[name="password"]') ||
                               document.querySelector('input[type="password"]:first-of-type') ||
                               document.querySelector('input[id*="password"]:not([name*="confirm"])');

            if (!passwordInput) {
                passwordInput = await waitForElement('input[name="password"], input[type="password"]:first-of-type');
            }
            console.log('🔍 找到密码输入框');

            // 多种方式查找确认密码输入框
            let confirmPasswordInput = document.querySelector('input[name="confirmPassword"]') ||
                                      document.querySelector('input[name="confirm_password"]') ||
                                      document.querySelector('input[type="password"]:last-of-type') ||
                                      document.querySelector('input[id*="confirm"]');

            if (!confirmPasswordInput) {
                confirmPasswordInput = await waitForElement('input[name="confirmPassword"], input[name="confirm_password"], input[type="password"]:last-of-type');
            }
            console.log('🔍 找到确认密码输入框');
            
            // 填写密码
            console.log('📝 开始填写密码...');
            await fillInput(passwordInput, CONFIG.password);

            // 等待一段时间后填写确认密码
            await new Promise(resolve => setTimeout(resolve, CONFIG.delay));
            await fillInput(confirmPasswordInput, CONFIG.confirmPassword);
            console.log('✅ 密码填写完成！');

            // 如果启用自动勾选条款
            if (CONFIG.autoCheckTerms) {
                await new Promise(resolve => setTimeout(resolve, 500));
                checkTermsCheckbox();
            }

            // 如果启用自动提交
            if (CONFIG.autoSubmit) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                // 检查是否正在提交中
                if (!isSubmitting) {
                    await clickRegisterButton();
                } else {
                    console.log('💡 检测到正在提交中，跳过自动提交');
                }
            }
            
        } catch (error) {
            console.error('❌ 执行出错:', error.message);
            
            // 备用方案：通过ID查找
            console.log('🔄 尝试备用方案...');
            try {
                const passwordById = document.querySelector('input[id*="form-item"][name="password"]');
                const confirmById = document.querySelector('input[id*="form-item"][name="confirmPassword"]');

                if (passwordById && confirmById) {
                    await fillInput(passwordById, CONFIG.password);
                    await new Promise(resolve => setTimeout(resolve, CONFIG.delay));
                    await fillInput(confirmById, CONFIG.confirmPassword);
                    console.log('✅ 备用方案成功！');
                } else {
                    console.error('❌ 备用方案也失败了，请检查页面结构');
                }
            } catch (backupError) {
                console.error('❌ 备用方案失败:', backupError.message);
            }
        }
    }
    
    // 添加手动触发按钮到页面
    function addControlPanel() {
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 12px;
        `;
        
        panel.innerHTML = `
            <div>自动填写工具</div>
            <button id="autoFillBtn" style="margin-top: 5px; padding: 5px 10px;">填写密码</button>
            <button id="debugBtn" style="margin-top: 5px; padding: 5px 10px; margin-left: 5px;">调试</button>
            <button id="removePanel" style="margin-top: 5px; padding: 5px 10px; margin-left: 5px;">关闭</button>
        `;
        
        document.body.appendChild(panel);
        
        document.getElementById('autoFillBtn').onclick = autoFillPasswords;
        document.getElementById('debugBtn').onclick = () => {
            console.log('🔍 调试信息：');
            console.log('当前页面URL:', window.location.href);

            const allPasswordInputs = document.querySelectorAll('input[type="password"]');
            console.log('找到的密码输入框数量:', allPasswordInputs.length);

            allPasswordInputs.forEach((input, index) => {
                console.log(`密码输入框 ${index + 1}:`, {
                    name: input.name,
                    id: input.id,
                    className: input.className,
                    placeholder: input.placeholder
                });
            });

            const allInputs = document.querySelectorAll('input');
            console.log('页面所有输入框:', allInputs.length);

            // 查找复选框
            const termsCheckbox = document.querySelector('#terms') ||
                                 document.querySelector('button[role="checkbox"]');
            if (termsCheckbox) {
                console.log('条款复选框:', {
                    id: termsCheckbox.id,
                    role: termsCheckbox.getAttribute('role'),
                    ariaChecked: termsCheckbox.getAttribute('aria-checked'),
                    dataState: termsCheckbox.getAttribute('data-state'),
                    className: termsCheckbox.className
                });
            } else {
                console.log('⚠️ 未找到条款复选框');
            }
        };
        document.getElementById('removePanel').onclick = () => panel.remove();
    }
    
    // 启动脚本
    console.log('🎯 BFL.AI 自动注册脚本已加载');
    console.log('💡 Tampermonkey 用户脚本使用说明：');
    console.log('1. 脚本会在 https://auth.bfl.ai/register 页面自动运行');
    console.log('2. 请在脚本中修改 CONFIG.password 为你想要的密码');
    console.log('3. 复制邮箱地址，脚本会自动完成整个注册流程');
    console.log('4. 注册成功后，复制新邮箱地址即可自动刷新开始下一轮');
    console.log('5. 真正的一键操作：复制邮箱 → 自动注册 → 复制新邮箱 → 自动刷新');
    console.log('6. 🔇 脚本后台静默运行，不会干扰其他窗口操作');
    console.log('7. ✨ 邮箱输入已优化：支持窗口切换，无需保持当前窗口激活');

    // 添加控制面板
    addControlPanel();

    // 启动注册成功监控
    checkRegistrationSuccess();

    // 启动剪贴板监控
    startClipboardMonitoring();

    // 页面加载完成后先检查内容，再执行自动填写
    setTimeout(() => {
        // 先检查页面内容是否完整
        if (checkPageContent()) {
            // 页面内容完整，等待一下再执行自动填写，给剪贴板更新时间
            setTimeout(() => {
                autoFillPasswords();
            }, 1500); // 额外等待1.5秒，确保剪贴板有时间更新
        }
        // 如果页面内容不完整，checkPageContent() 会自动刷新页面
    }, 2000);
    
})();
