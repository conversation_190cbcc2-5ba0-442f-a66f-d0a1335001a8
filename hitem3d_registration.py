import time
import os
import threading
import random
import string
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hitem3d_registration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Hitem3DRegistrationAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.current_email = ""
        self.current_verification_code = ""
        self.last_email_content = ""
        self.last_verification_code = ""
        self.registration_tab = None
        self.monitoring_email_file = False
        self.monitoring_verification_code_file = False
        self.email_file = "current_email.txt"
        self.verification_code_file = "verification_code.txt"
        self.request_file = "request_new_email.txt"
        self.switch_proxy_file = "switch_proxy_request.txt"  # 代理切换请求文件
        self.monitoring_proxy_switch = False  # 是否正在监控代理切换请求
        self.reset_count_file = "reset_refresh_count.txt"  # 重置刷新计数通知文件
        self.accounts_file = "hitem3d_accounts.json"  # 保存账号信息的文件
        
        # 配置参数
        self.password = "MySecurePassword123!"
        self.confirm_password = "MySecurePassword123!"
        self.nickname = self.generate_random_nickname()
        self.auto_submit = True
        self.auto_check_terms = True
        self.auto_refresh_on_success = True  # 是否在注册成功后自动刷新页面

    def generate_random_nickname(self):
        """生成随机模拟真实用户的昵称"""
        # 常见英文名列表
        first_names = [
            "Alex", "Jordan", "Taylor", "Casey", "Morgan", "Riley", "Quinn", "Avery", "Blake", "Cameron",
            "Drew", "Emery", "Finley", "Gray", "Harper", "Indigo", "Jamie", "Kendall", "Logan", "Mason",
            "Noah", "Oliver", "Parker", "Quinn", "River", "Sage", "Tyler", "Unity", "Vale", "Winter",
            "Xander", "Yuki", "Zane", "Aria", "Bella", "Chloe", "Diana", "Emma", "Fiona", "Grace",
            "Hannah", "Iris", "Jade", "Kate", "Lily", "Maya", "Nina", "Olivia", "Paige", "Ruby",
            "Sophia", "Tara", "Uma", "Violet", "Willow", "Xena", "Yara", "Zara", "Amber", "Brook",
            "Crystal", "Dawn", "Eve", "Faith", "Hope", "Ivy", "Joy", "Kara", "Luna", "Mira",
            "Nova", "Opal", "Pearl", "Rain", "Star", "Terra", "Uma", "Vega", "Wren", "Xia"
        ]
        
        # 常见姓氏列表
        last_names = [
            "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez",
            "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas", "Taylor", "Moore", "Jackson", "Martin",
            "Lee", "Perez", "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson",
            "Walker", "Young", "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
            "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell", "Carter", "Roberts",
            "Collins", "Stewart", "Morris", "Rogers", "Reed", "Cook", "Morgan", "Bell", "Murphy", "Bailey",
            "Cooper", "Richardson", "Cox", "Howard", "Ward", "Torres", "Peterson", "Gray", "Ramirez", "James",
            "Watson", "Brooks", "Kelly", "Sanders", "Price", "Bennett", "Wood", "Barnes", "Ross", "Henderson",
            "Coleman", "Jenkins", "Perry", "Powell", "Long", "Patterson", "Hughes", "Flores", "Washington", "Butler"
        ]
        
        # 数字后缀
        numbers = ["", str(random.randint(1, 999)), str(random.randint(10, 99)), str(random.randint(100, 999))]
        
        # 特殊字符
        special_chars = ["", "_", ".", "-", ""]
        
        # 生成昵称的几种模式
        patterns = [
            # 名+姓
            lambda: f"{random.choice(first_names)}{random.choice(last_names)}",
            # 名+数字
            lambda: f"{random.choice(first_names)}{random.choice(numbers)}",
            # 名+特殊字符+数字
            lambda: f"{random.choice(first_names)}{random.choice(special_chars)}{random.choice(numbers)}",
            # 名+姓+数字
            lambda: f"{random.choice(first_names)}{random.choice(last_names)}{random.choice(numbers)}",
            # 名+特殊字符+姓
            lambda: f"{random.choice(first_names)}{random.choice(special_chars)}{random.choice(last_names)}",
            # 名+姓+特殊字符+数字
            lambda: f"{random.choice(first_names)}{random.choice(last_names)}{random.choice(special_chars)}{random.choice(numbers)}",
            # 纯名
            lambda: f"{random.choice(first_names)}",
            # 名+名
            lambda: f"{random.choice(first_names)}{random.choice(first_names)}",
            # 名+小写姓
            lambda: f"{random.choice(first_names)}{random.choice(last_names).lower()}",
            # 小写名+姓
            lambda: f"{random.choice(first_names).lower()}{random.choice(last_names)}"
        ]
        
        # 随机选择一种模式生成昵称
        nickname = random.choice(patterns)()
        
        # 确保昵称长度在3-20个字符之间
        if len(nickname) < 3:
            nickname += str(random.randint(10, 99))
        elif len(nickname) > 20:
            nickname = nickname[:20]
        
        return nickname

    def setup_browser(self):
        """设置并启动浏览器"""
        # 直接使用Selenium启动浏览器，使用Profile 1
        chrome_options = Options()
        chrome_options.binary_location = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"

        # 创建临时用户数据目录来避免冲突
        import os
        import tempfile
        # 使用环境变量中的时间戳创建唯一目录，避免多进程冲突
        suffix = os.environ.get('CHROME_TEMP_SUFFIX', str(int(time.time() * 1000)))
        temp_user_data_dir = os.path.join(tempfile.gettempdir(), f"chrome_selenium_profile3_{suffix}")

        # 如果临时目录存在，先删除
        if os.path.exists(temp_user_data_dir):
            import shutil
            try:
                shutil.rmtree(temp_user_data_dir)
            except:
                pass

        # 复制Profile 1的数据到临时目录
        original_profile_dir = os.path.expanduser("C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1")
        temp_profile_dir = os.path.join(temp_user_data_dir, "Profile 1")

        if os.path.exists(original_profile_dir):
            print("正在复制Profile 1数据...")
            import shutil
            try:
                shutil.copytree(original_profile_dir, temp_profile_dir)
                print("Profile 1数据复制完成")
            except Exception as e:
                print(f"复制Profile数据失败: {e}")
                print("将使用默认配置启动...")

        chrome_options.add_argument(f"--user-data-dir={temp_user_data_dir}")
        chrome_options.add_argument("--profile-directory=Profile 1")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")

        # 确保扩展能够加载
        chrome_options.add_argument("--enable-extensions")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-translate")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 地理位置和语言伪装设置
        chrome_options.add_argument("--lang=en-US")  # 设置语言为英语
        chrome_options.add_argument("--disable-geolocation")  # 禁用地理位置
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI")  # 禁用翻译UI
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")

        # WebRTC设置 - 防止IP泄露
        chrome_options.add_argument("--force-webrtc-ip-handling-policy=disable_non_proxied_udp")
        chrome_options.add_argument("--enforce-webrtc-ip-permission-check")

        # DNS设置 - 防止DNS泄露
        chrome_options.add_argument("--dns-prefetch-disable")
        chrome_options.add_argument("--disable-dns-prefetch")

        # 时区设置为美国东部时间
        chrome_options.add_argument("--timezone=America/New_York")

        # 强制设置Accept-Language头
        chrome_options.add_argument("--accept-lang=en-US,en;q=0.9")

        # 添加用户代理，使其看起来更像美国的正常浏览器
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

        # 添加反检测参数（保留扩展功能）
        chrome_options.add_argument("--disable-client-side-phishing-detection")
        chrome_options.add_argument("--disable-component-update")
        chrome_options.add_argument("--disable-domain-reliability")
        chrome_options.add_argument("--disable-hang-monitor")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-prompt-on-repost")
        chrome_options.add_argument("--metrics-recording-only")
        chrome_options.add_argument("--safebrowsing-disable-auto-update")
        chrome_options.add_argument("--password-store=basic")
        chrome_options.add_argument("--use-mock-keychain")

        # 剪切板权限相关参数 - 强制启用
        chrome_options.add_argument("--enable-clipboard-api")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--enable-experimental-web-platform-features")
        chrome_options.add_argument("--autoplay-policy=no-user-gesture-required")
        chrome_options.add_argument("--disable-web-security")  # 禁用网络安全限制
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI,BlinkGenPropertyTrees")
        chrome_options.add_argument("--enable-features=ClipboardAPI")  # 强制启用剪贴板API
        chrome_options.add_argument("--allow-running-insecure-content")  # 允许不安全内容
        chrome_options.add_argument("--disable-site-isolation-trials")  # 禁用站点隔离
        chrome_options.add_argument("--disable-permissions-api")  # 禁用权限API检查

        # 设置首选语言和权限
        prefs = {
            "intl.accept_languages": "en-US,en",
            "profile.default_content_setting_values.geolocation": 2,  # 阻止地理位置请求
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 1,
            # 剪切板权限设置 - 自动授权所有网站
            "profile.default_content_setting_values.clipboard": 1,  # 1=允许, 2=阻止
            "profile.content_settings.exceptions.clipboard": {
                "https://dash.proxy302.com,*": {
                    "setting": 1
                },
                "[*.]proxy302.com,*": {
                    "setting": 1
                },
                "*": {
                    "setting": 1
                }
            },
            # 强制允许剪贴板访问
            "profile.managed_default_content_settings.clipboard": 1,
            # 其他权限设置
            "profile.default_content_setting_values.notifications": 2,  # 阻止通知
            "profile.default_content_setting_values.media_stream": 2,  # 阻止摄像头/麦克风
        }
        chrome_options.add_experimental_option("prefs", prefs)

        print("正在启动浏览器...")
        print(f"临时目录: {temp_user_data_dir}")
        print(f"进程ID: {os.getpid()}")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("浏览器启动成功！")

            # 打开一个自定义的启动页面
            startup_html = """
            <html>
            <head>
                <title>Hitem3D注册自动化工具 - 已启动</title>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        margin: 0;
                        padding: 40px;
                        min-height: 100vh;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                    }
                    .container {
                        background: rgba(255, 255, 255, 0.1);
                        backdrop-filter: blur(10px);
                        border-radius: 20px;
                        padding: 40px;
                        text-align: center;
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                        max-width: 600px;
                    }
                    h1 {
                        font-size: 2.5em;
                        margin-bottom: 20px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                    }
                    .status {
                        font-size: 1.2em;
                        margin: 20px 0;
                        padding: 15px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                    }
                    .info {
                        font-size: 1em;
                        margin: 10px 0;
                        opacity: 0.9;
                    }
                    .emoji {
                        font-size: 1.5em;
                        margin-right: 10px;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1><span class="emoji">🚀</span>Hitem3D注册自动化工具</h1>
                    <div class="status">
                        <span class="emoji">✅</span>浏览器已成功启动
                    </div>
                    <div class="info">
                        <span class="emoji">🔧</span>正在初始化设置...
                    </div>
                    <div class="info">
                        <span class="emoji">🌐</span>配置地理位置伪装
                    </div>
                    <div class="info">
                        <span class="emoji">📋</span>设置剪贴板权限
                    </div>
                    <div class="info">
                        <span class="emoji">🛡️</span>应用反检测措施
                    </div>
                    <div class="status" style="margin-top: 30px;">
                        <span class="emoji">⏳</span>准备访问代理页面...
                    </div>
                </div>
            </body>
            </html>
            """
            self.driver.get("data:text/html," + startup_html.replace('\n', '').replace('  ', ''))
            time.sleep(2)  # 等待页面加载和用户查看

            # 首先设置剪切板权限
            self.setup_clipboard_permissions()

            # 隐藏自动化特征并伪装地理位置信息
            self.driver.execute_script("""
                // 隐藏webdriver特征
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});

                // 删除自动化相关属性
                delete navigator.__proto__.webdriver;

                // 伪装Chrome对象
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });

                // 伪装权限API并自动授权剪切板
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => {
                    if (parameters.name === 'notifications') {
                        return Promise.resolve({ state: Notification.permission });
                    } else if (parameters.name === 'clipboard-read' || parameters.name === 'clipboard-write') {
                        // 自动授权剪切板权限
                        return Promise.resolve({ state: 'granted' });
                    }
                    return originalQuery(parameters);
                };

                // 确保剪切板API可用
                if (navigator.clipboard) {
                    console.log('剪切板API已可用');
                } else {
                    console.log('剪切板API不可用，尝试启用');
                }

                // 强制伪装地理位置和语言
                Object.defineProperty(navigator, 'language', {
                    get: () => 'en-US',
                    configurable: false,
                    enumerable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: false,
                    enumerable: true
                });

                // 伪装时区
                Date.prototype.getTimezoneOffset = function() { return 300; }; // UTC-5 (美国东部时间)

                // 伪装硬件信息
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 4
                });

                // 伪装内存信息
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8
                });

                // 全局拦截所有网络请求
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                    // 强制修改reCAPTCHA语言
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('全局拦截reCAPTCHA请求:', url);
                    }
                    const result = originalOpen.apply(this, [method, url, async, user, password]);
                    this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                    return result;
                };

                // 全局拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(input, init) {
                    let url = typeof input === 'string' ? input : input.url;
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('全局拦截fetch reCAPTCHA请求:', url);
                        if (typeof input === 'string') {
                            input = url;
                        } else {
                            input = new Request(url, input);
                        }
                    }
                    init = init || {};
                    init.headers = init.headers || {};
                    init.headers['Accept-Language'] = 'en-US,en;q=0.9';
                    return originalFetch(input, init);
                };

                // 禁用WebRTC
                if (window.RTCPeerConnection) {
                    window.RTCPeerConnection = undefined;
                }
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = undefined;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = undefined;
                }

                // 伪装屏幕信息（美国常见分辨率）
                Object.defineProperty(screen, 'width', {get: () => 1920});
                Object.defineProperty(screen, 'height', {get: () => 1080});
                Object.defineProperty(screen, 'availWidth', {get: () => 1920});
                Object.defineProperty(screen, 'availHeight', {get: () => 1040});

                // 移除自动化检测标记
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                console.log('全局地理位置和reCAPTCHA伪装设置完成');
            """)
            return True
        except Exception as e:
            print(f"浏览器启动失败: {e}")
            return False

    def setup_clipboard_permissions(self):
        """设置剪切板权限，确保自动授权"""
        try:
            print("正在设置剪切板权限...")
            self.driver.execute_script("""
                // 立即设置剪切板权限
                if (navigator.permissions && navigator.permissions.query) {
                    // 重写权限查询方法，自动授权剪切板
                    const originalQuery = navigator.permissions.query;
                    navigator.permissions.query = function(parameters) {
                        if (parameters.name === 'clipboard-read' || parameters.name === 'clipboard-write') {
                            console.log('自动授权剪切板权限:', parameters.name);
                            return Promise.resolve({
                                state: 'granted',
                                onchange: null
                            });
                        }
                        return originalQuery.call(this, parameters);
                    };
                }

                // 确保剪切板API可用
                if (navigator.clipboard) {
                    console.log('✅ 剪切板API已可用');

                    // 测试剪切板权限
                    navigator.permissions.query({name: 'clipboard-read'}).then(result => {
                        console.log('剪切板读取权限状态:', result.state);
                    }).catch(err => {
                        console.log('剪切板权限查询失败:', err);
                    });

                    navigator.permissions.query({name: 'clipboard-write'}).then(result => {
                        console.log('剪切板写入权限状态:', result.state);
                    }).catch(err => {
                        console.log('剪切板权限查询失败:', err);
                    });
                } else {
                    console.log('❌ 剪切板API不可用');
                }

                console.log('剪切板权限设置完成');
            """)
            print("✅ 剪切板权限设置完成")
            return True
        except Exception as e:
            print(f"设置剪切板权限失败: {e}")
            return False

    def apply_geolocation_spoofing(self):
        """在每个页面加载后应用地理位置伪装"""
        try:
            self.driver.execute_script("""
                // 重新应用地理位置伪装（防止页面刷新后失效）
                Object.defineProperty(navigator, 'language', {
                    get: () => 'en-US',
                    configurable: false,
                    enumerable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                    configurable: false,
                    enumerable: true
                });

                // 强制伪装Accept-Language
                if (window.XMLHttpRequest) {
                    const originalOpen = XMLHttpRequest.prototype.open;
                    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                        const result = originalOpen.apply(this, arguments);
                        this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');
                        return result;
                    };
                }

                // 伪装fetch请求的Accept-Language
                if (window.fetch) {
                    const originalFetch = window.fetch;
                    window.fetch = function(input, init) {
                        init = init || {};
                        init.headers = init.headers || {};
                        if (!init.headers['Accept-Language']) {
                            init.headers['Accept-Language'] = 'en-US,en;q=0.9';
                        }
                        return originalFetch(input, init);
                    };
                }

                // 伪装时区
                Date.prototype.getTimezoneOffset = function() { return 300; }; // UTC-5

                // 伪装Intl对象
                if (window.Intl && window.Intl.DateTimeFormat) {
                    const originalResolvedOptions = Intl.DateTimeFormat.prototype.resolvedOptions;
                    Intl.DateTimeFormat.prototype.resolvedOptions = function() {
                        const options = originalResolvedOptions.call(this);
                        options.locale = 'en-US';
                        options.timeZone = 'America/New_York';
                        return options;
                    };
                }

                // 禁用地理位置API
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition = function(success, error) {
                        if (error) {
                            error({code: 1, message: 'User denied Geolocation'});
                        }
                    };
                    navigator.geolocation.watchPosition = function(success, error) {
                        if (error) {
                            error({code: 1, message: 'User denied Geolocation'});
                        }
                    };
                }

                // 禁用WebRTC
                if (window.RTCPeerConnection) {
                    window.RTCPeerConnection = undefined;
                }
                if (window.webkitRTCPeerConnection) {
                    window.webkitRTCPeerConnection = undefined;
                }
                if (window.mozRTCPeerConnection) {
                    window.mozRTCPeerConnection = undefined;
                }

                console.log('地理位置伪装已应用');
            """)
        except Exception as e:
            print(f"应用地理位置伪装失败: {e}")

    def setup_recaptcha_interception(self):
        """设置reCAPTCHA请求拦截，强制使用英文版本"""
        try:
            self.driver.execute_script("""
                // 拦截所有网络请求，特别是reCAPTCHA相关的
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                    // 如果是reCAPTCHA请求，强制修改为英文版本
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('拦截并修改reCAPTCHA请求为英文版本:', url);
                    }

                    const result = originalOpen.apply(this, [method, url, async, user, password]);

                    // 设置请求头
                    this.setRequestHeader('Accept-Language', 'en-US,en;q=0.9');

                    return result;
                };

                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(input, init) {
                    let url = typeof input === 'string' ? input : input.url;

                    // 修改reCAPTCHA URL
                    if (url.includes('recaptcha') && url.includes('zh_cn')) {
                        url = url.replace(/zh_cn/g, 'en');
                        console.log('拦截并修改fetch reCAPTCHA请求为英文版本:', url);
                        if (typeof input === 'string') {
                            input = url;
                        } else {
                            input = new Request(url, input);
                        }
                    }

                    init = init || {};
                    init.headers = init.headers || {};
                    init.headers['Accept-Language'] = 'en-US,en;q=0.9';

                    return originalFetch(input, init);
                };

                console.log('reCAPTCHA请求拦截器已设置');
            """)
        except Exception as e:
            print(f"设置reCAPTCHA拦截失败: {e}")

    def notify_reset_proxy_switch_count(self):
        """通知email_automation.py重置代理切换计数"""
        try:
            with open(self.reset_count_file, 'w', encoding='utf-8') as f:
                f.write("reset_refresh_count")
            print("已通知email_automation.py重置代理切换计数")
        except Exception as e:
            print(f"通知重置代理切换计数失败: {e}")

    def check_proxy_generation_success(self, max_wait_seconds=10):
        """检测代理生成成功弹窗"""
        try:
            print("正在检测代理生成成功弹窗...")

            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找成功提示弹窗
                    success_elements = self.driver.find_elements(By.CSS_SELECTOR, '.arco-message-success')

                    for element in success_elements:
                        try:
                            # 检查弹窗内容
                            element_text = element.text.strip()
                            print(f"检测到弹窗内容: {element_text}")

                            # 检查是否包含代理生成成功的关键词
                            success_keywords = [
                                "Proxy generated successfully",
                                "proxy generated successfully",
                                "generation successful",
                                "Generated successfully"
                            ]

                            if any(keyword in element_text for keyword in success_keywords):
                                print(f"✅ 检测到代理生成成功弹窗: {element_text}")
                                return True

                        except Exception as text_error:
                            print(f"读取弹窗文本失败: {text_error}")
                            continue

                    # 也检查其他可能的成功提示元素
                    other_success_selectors = [
                        '.arco-message.arco-message-success',
                        'div[class*="arco-message-success"]',
                        '.arco-message-content'
                    ]

                    for selector in other_success_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                element_text = element.text.strip()
                                if element_text and "Proxy generated successfully" in element_text:
                                    print(f"✅ 通过备用选择器检测到成功弹窗: {element_text}")
                                    return True
                        except:
                            continue

                except Exception as check_error:
                    print(f"检查弹窗时出错: {check_error}")

                time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未检测到代理生成成功弹窗")
            return False

        except Exception as e:
            print(f"检测代理生成成功弹窗时出错: {e}")
            return False

    def check_edit_success_message(self, max_wait_seconds=5):
        """检测编辑成功提示消息"""
        try:
            print("正在检测编辑成功提示...")

            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找编辑成功提示
                    success_selectors = [
                        '.arco-message-success',
                        'div[class*="arco-message-success"]',
                        '.arco-message-content'
                    ]

                    for selector in success_selectors:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in elements:
                                element_text = element.text.strip()
                                if element_text and "Edit Success" in element_text:
                                    print(f"✅ 检测到编辑成功提示: {element_text}")
                                    return True
                        except:
                            continue

                    # 也尝试通过XPath查找
                    try:
                        success_message = self.driver.find_element(By.XPATH, "//span[contains(text(), 'Edit Success')]")
                        if success_message:
                            print(f"✅ 通过XPath检测到编辑成功提示: {success_message.text}")
                            return True
                    except:
                        pass

                except Exception as check_error:
                    print(f"检查编辑成功提示时出错: {check_error}")

                time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未检测到编辑成功提示")
            return False

        except Exception as e:
            print(f"检测编辑成功提示时出错: {e}")
            return False

    def open_proxy_page(self):
        """打开代理页面并先选择代理，然后再打开新标签"""
        try:
            print("正在访问代理页面...")
            self.driver.get("https://dash.proxy302.com/quick-access")
            time.sleep(5)  # 增加等待时间，确保页面完全加载
            print(f"代理页面加载完成，当前URL: {self.driver.current_url}")

            # 应用地理位置伪装和reCAPTCHA拦截
            self.apply_geolocation_spoofing()
            self.setup_recaptcha_interception()

            # 等待表格加载完成
            print("等待表格内容加载...")
            time.sleep(3)

            # 检查表格是否加载
            try:
                self.driver.find_element(By.CSS_SELECTOR, '.arco-table')
                print("表格已加载")
            except:
                print("表格未找到，但继续尝试操作")

            # 先选择代理
            print("🔄 开始选择代理流程...")
            if self.select_proxy_first():
                print("✅ 代理选择完成，现在点击打开按钮...")
                # 代理选择完成后，再点击打开按钮
                return self.click_open_button()
            else:
                print("❌ 代理选择失败")
                return False

        except TimeoutException:
            print("代理页面操作超时")
            return False

        except Exception as e:
            print(f"访问代理页面失败: {e}")
            return False

    def select_proxy_first(self):
        """在页面加载后立即选择代理"""
        try:
            print("正在执行代理选择流程...")

            # 查找"选择代理"按钮
            print("正在查找选择代理按钮...")
            select_proxy_button = None

            try:
                # 查找包含"Select Proxy"文本的按钮
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "Select Proxy" in button.text:
                        select_proxy_button = button
                        print("找到选择代理按钮")
                        break

                if not select_proxy_button:
                    # 尝试通过XPath查找
                    select_proxy_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Select Proxy')]")
                    print("通过XPath找到选择代理按钮")

            except Exception as e:
                print(f"查找选择代理按钮失败: {e}")
                return False

            if select_proxy_button:
                # 点击选择代理按钮
                print("点击选择代理按钮...")
                select_proxy_button.click()
                time.sleep(3)

                # 等待弹窗出现
                try:
                    modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                    print("代理选择弹窗已打开")

                    # 查找"Rotating IP Pay by Traffic"标签页
                    print("正在查找轮换IP按流量扣费标签...")
                    rotating_ip_tab = None

                    try:
                        # 查找包含"Rotating IP Pay by Traffic"的标签
                        tab_titles = modal_container.find_elements(By.CSS_SELECTOR, '.arco-tabs-tab-title')
                        for tab in tab_titles:
                            if "Rotating IP Pay by Traffic" in tab.text:
                                rotating_ip_tab = tab
                                print("找到轮换IP按流量扣费标签")
                                break

                    except Exception as e:
                        print(f"查找轮换IP标签失败: {e}")

                    if rotating_ip_tab:
                        # 点击轮换IP标签
                        print("点击轮换IP按流量扣费标签...")
                        rotating_ip_tab.click()
                        time.sleep(2)

                        # 先选择国家，然后点击生成按钮
                        print("正在选择国家...")
                        if self.select_country_canada():
                            print("✅ 已选择Canada (CA)")
                        else:
                            print("⚠ 选择国家失败，继续生成...")

                        # 点击生成按钮
                        print("正在点击生成按钮...")
                        generate_button = None

                        try:
                            # 重新获取弹窗容器
                            modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')

                            # 查找包含"Generate Now"文本的按钮
                            buttons = modal_container.find_elements(By.TAG_NAME, "button")
                            for button in buttons:
                                if "Generate Now" in button.text:
                                    generate_button = button
                                    print("找到生成按钮")
                                    break

                            if not generate_button:
                                # 尝试通过XPath查找
                                generate_button = modal_container.find_element(By.XPATH, ".//button[contains(text(), 'Generate Now')]")
                                print("通过XPath找到生成按钮")

                        except Exception as e:
                            print(f"查找生成按钮失败: {e}")
                            print("跳过生成，直接选择现有代理...")

                        if generate_button:
                            # 检查按钮是否可点击
                            if generate_button.is_enabled() and generate_button.is_displayed():
                                # 滚动到按钮位置
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", generate_button)
                                time.sleep(1)

                                # 点击生成按钮
                                try:
                                    generate_button.click()
                                    print("✅ 已点击生成按钮")

                                    # 等待代理生成成功
                                    print("等待代理生成完成...")
                                    if self.check_proxy_generation_success(max_wait_seconds=30):
                                        print("✅ 代理生成成功")
                                    else:
                                        print("⚠ 代理生成可能失败，继续选择代理...")

                                except Exception as click_error:
                                    print(f"点击生成按钮失败: {click_error}")
                                    print("继续选择现有代理...")
                            else:
                                print("生成按钮不可点击，选择现有代理...")
                        else:
                            print("未找到生成按钮，选择现有代理...")

                        # 等待一下确保代理列表更新
                        time.sleep(3)

                        # 选择代理
                        if self.select_proxy_in_modal():
                            print("✅ 代理选择完成")
                            return True
                        else:
                            print("新方法失败，尝试原方法...")
                            if self.select_first_proxy():
                                print("✅ 通过原方法成功选择代理")
                                return True
                            else:
                                print("❌ 代理选择失败")
                                return False
                    else:
                        print("❌ 未找到轮换IP按流量扣费标签")
                        return False

                except Exception as e:
                    print(f"处理代理选择弹窗失败: {e}")
                    return False
            else:
                print("❌ 未找到选择代理按钮")
                return False

        except Exception as e:
            print(f"选择代理流程失败: {e}")
            return False

    def select_country_canada(self):
        """点击选择器激活搜索模式，然后输入Canada (CA)"""
        try:
            print("正在查找国家选择器...")

            # 查找国家选择器容器
            country_selector = None

            try:
                modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')

                # 查找arco-select容器
                selector_selectors = [
                    '.arco-select-view-single.arco-select',
                    '.arco-select.custom-form-control',
                    '.arco-select-view-single',
                    '.arco-select'
                ]

                for selector in selector_selectors:
                    try:
                        country_selector = modal_container.find_element(By.CSS_SELECTOR, selector)
                        print(f"通过选择器 '{selector}' 找到国家选择器")
                        break
                    except:
                        continue

            except Exception as e:
                print(f"查找国家选择器失败: {e}")
                return False

            if country_selector:
                # 首先检查当前显示的值
                try:
                    # 查找显示当前值的元素
                    value_display = country_selector.find_element(By.CSS_SELECTOR, '.arco-select-view-value')
                    current_text = value_display.text.strip()

                    print(f"当前选中值: '{current_text}'")

                    # 检查是否已经是Canada
                    if "Canada (CA)" in current_text:
                        print("✅ 检测到已经选中Canada (CA)，无需重新选择")
                        return True
                    else:
                        print(f"当前选中的不是Canada，需要重新选择。当前值: {current_text}")

                except Exception as check_error:
                    print(f"检查当前值时出错: {check_error}，继续选择流程")

                # 滚动到选择器位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", country_selector)
                time.sleep(1)

                # 点击选择器激活搜索模式
                try:
                    country_selector.click()
                    print("已点击国家选择器，激活搜索模式")
                    time.sleep(1)

                    # 现在查找输入框（应该已经激活）
                    country_input = None
                    input_selectors = [
                        '.arco-select-view-input',
                        'input.arco-select-view-input',
                        'input[placeholder*="Random"]',
                        'input[placeholder*="Canada"]'
                    ]

                    for selector in input_selectors:
                        try:
                            country_input = modal_container.find_element(By.CSS_SELECTOR, selector)
                            print(f"通过选择器 '{selector}' 找到激活的输入框")
                            break
                        except:
                            continue

                    if country_input:
                        # 清空输入框并输入Canada
                        try:
                            # 先清空
                            country_input.clear()
                            print("已清空输入框")
                            time.sleep(0.5)

                            # 输入Canada (CA)
                            country_input.send_keys("Canada (CA)")
                            print("✅ 已输入Canada (CA)")
                            time.sleep(1)

                            # 等待一下让搜索结果出现，然后按回车确认
                            time.sleep(1)
                            country_input.send_keys("\n")  # 按回车键确认选择
                            print("已按回车确认选择")
                            time.sleep(1)

                            return True

                        except Exception as input_error:
                            print(f"输入Canada失败: {input_error}")
                            return False
                    else:
                        print("❌ 激活搜索模式后未找到输入框")
                        return False

                except Exception as click_error:
                    print(f"点击选择器失败: {click_error}")
                    return False
            else:
                print("❌ 未找到国家选择器")
                return False

        except Exception as e:
            print(f"选择Canada国家时出错: {e}")
            return False

    def select_proxy_in_modal(self, max_wait_seconds=15):
        """在弹窗中选择代理的新方法，直接在select-proxy-modal中查找"""
        try:
            print("正在使用新方法查找弹窗中的选择代理按钮...")

            start_time = time.time()
            check_interval = 1

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找特定的代理选择弹窗容器
                    modal_container = None
                    try:
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                        print("✓ 找到代理选择弹窗容器 (.select-proxy-modal)")
                    except:
                        # 如果找不到特定容器，尝试通用的弹窗容器
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container')
                        print("✓ 找到通用弹窗容器 (.arco-modal-container)")

                    # 等待弹窗内容加载（很短的时间）
                    time.sleep(1)

                    # 多种方式在弹窗容器中查找选择代理按钮
                    select_button = None

                    # 方法1：在弹窗容器中使用具体路径查找
                    try:
                        # 在弹窗容器内查找表格第9列的选择代理按钮
                        specific_button = modal_container.find_element(By.XPATH, ".//table/tbody/tr/td[9]/span/span/button[contains(text(),'Select Proxy')]")
                        if specific_button.is_displayed() and specific_button.is_enabled():
                            select_button = specific_button
                            print("✓ 在弹窗容器中通过具体路径找到选择代理按钮")
                    except Exception:
                        print("弹窗容器中具体路径查找失败，尝试其他方法...")

                    # 方法2：在弹窗容器中查找第9列的所有按钮
                    if not select_button:
                        try:
                            ninth_column_buttons = modal_container.find_elements(By.XPATH, ".//table/tbody/tr/td[9]//button")
                            print(f"在弹窗容器的第9列找到 {len(ninth_column_buttons)} 个按钮")

                            for i, btn in enumerate(ninth_column_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  第9列按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查第9列按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找第9列按钮失败，尝试其他方法...")

                    # 方法3：在弹窗容器中查找所有"选择代理"按钮
                    if not select_button:
                        try:
                            all_select_buttons = modal_container.find_elements(By.XPATH, ".//button[contains(text(),'Select Proxy')]")
                            print(f"在弹窗容器中找到 {len(all_select_buttons)} 个选择代理按钮")

                            for i, btn in enumerate(all_select_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  选择代理按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查选择代理按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找所有选择代理按钮失败，尝试其他方法...")

                    # 方法4：在弹窗容器中查找表格中的所有按钮并过滤
                    if not select_button:
                        try:
                            table_buttons = modal_container.find_elements(By.XPATH, ".//table//button")
                            print(f"在弹窗容器的表格中找到 {len(table_buttons)} 个按钮")

                            for i, btn in enumerate(table_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  表格按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_button = btn
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                        break
                                except Exception as btn_error:
                                    print(f"  检查表格按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception:
                            print("在弹窗容器中查找表格按钮失败，尝试其他方法...")

                    if select_button:
                        try:
                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", select_button)
                            time.sleep(1)

                            # 点击按钮
                            select_button.click()
                            print("✅ 已点击选择代理按钮")

                            # 检测编辑成功提示
                            if self.check_edit_success_message():
                                print("✅ 检测到编辑成功提示")
                                return True
                            else:
                                print("❌ 未检测到编辑成功提示，继续等待...")
                                time.sleep(check_interval)
                                continue

                        except Exception as click_error:
                            print(f"点击按钮失败: {click_error}")
                            try:
                                # 尝试JavaScript点击
                                self.driver.execute_script("arguments[0].click();", select_button)
                                print("✅ 已通过JavaScript点击选择代理按钮")

                                if self.check_edit_success_message():
                                    print("✅ 检测到编辑成功提示")
                                    return True
                                else:
                                    print("❌ 未检测到编辑成功提示，继续等待...")
                                    time.sleep(check_interval)
                                    continue
                            except Exception as js_error:
                                print(f"JavaScript点击也失败: {js_error}")
                    else:
                        print("未找到选择代理按钮，继续等待...")
                        time.sleep(check_interval)

                except Exception as e:
                    print(f"查找代理选择弹窗容器时出错: {e}")
                    time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未能成功选择代理")
            return False

        except Exception as e:
            print(f"选择代理时出错: {e}")
            return False

    def select_first_proxy(self, max_wait_seconds=20):
        """选择弹窗表格中第一个代理（原方法，作为备用）"""
        try:
            print("正在使用原方法查找弹窗表格中的第一个选择代理按钮...")

            start_time = time.time()
            check_interval = 1  # 每1秒检查一次

            while time.time() - start_time < max_wait_seconds:
                try:
                    # 查找代理选择弹窗容器
                    modal_container = None
                    try:
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal-container.md-modal.select-proxy-modal')
                        print("✓ 找到代理选择弹窗容器")
                    except:
                        # 如果找不到特定容器，尝试通用的弹窗容器
                        modal_container = self.driver.find_element(By.CSS_SELECTOR, '.arco-modal')
                        print("✓ 找到通用弹窗容器")

                    # 等待弹窗内容加载完成（很短时间）
                    time.sleep(1)

                    # 在弹窗容器中查找选择代理按钮
                    select_buttons = []

                    # 方法1：在弹窗容器中使用具体路径查找
                    try:
                        specific_xpath_button = modal_container.find_element(By.XPATH, ".//table//tbody//tr//td[9]//button[contains(text(),'Select Proxy')]")
                        if specific_xpath_button.is_displayed() and specific_xpath_button.is_enabled():
                            select_buttons.append(specific_xpath_button)
                            print("✓ 在弹窗容器中通过具体XPath找到选择代理按钮")
                    except Exception:
                        print("弹窗容器中具体XPath查找失败，尝试其他方法...")

                    # 方法2：在弹窗容器中查找第9列的按钮
                    if not select_buttons:
                        try:
                            ninth_column_buttons = modal_container.find_elements(By.XPATH, ".//table//tbody//tr//td[9]//button")
                            print(f"在弹窗容器的第9列找到 {len(ninth_column_buttons)} 个按钮")

                            for i, btn in enumerate(ninth_column_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  第9列按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if "Select Proxy" in btn_text and btn_visible and btn_enabled:
                                        select_buttons.append(btn)
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                except Exception as btn_error:
                                    print(f"  检查第9列按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception as e:
                            print(f"在弹窗容器中查找第9列按钮失败: {e}")

                    # 方法3：在弹窗容器中查找所有"选择代理"按钮
                    if not select_buttons:
                        try:
                            all_select_buttons = modal_container.find_elements(By.XPATH, ".//button[contains(text(),'Select Proxy')]")
                            print(f"在弹窗容器中找到 {len(all_select_buttons)} 个选择代理按钮")

                            for i, btn in enumerate(all_select_buttons):
                                try:
                                    btn_text = btn.text.strip()
                                    btn_visible = btn.is_displayed()
                                    btn_enabled = btn.is_enabled()
                                    print(f"  选择代理按钮 {i+1}: 文本='{btn_text}', 可见={btn_visible}, 可用={btn_enabled}")

                                    if btn_visible and btn_enabled:
                                        select_buttons.append(btn)
                                        print(f"  ✓ 找到有效的选择代理按钮: {btn_text}")
                                except Exception as btn_error:
                                    print(f"  检查选择代理按钮 {i+1} 时出错: {btn_error}")
                                    continue
                        except Exception as e:
                            print(f"在弹窗容器中查找所有选择代理按钮失败: {e}")

                    if select_buttons:
                        print(f"总共找到 {len(select_buttons)} 个可用的选择代理按钮")
                        # 点击第一个选择代理按钮（最新生成的代理）
                        first_button = select_buttons[0]

                        try:
                            # 滚动到按钮位置
                            self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", first_button)
                            time.sleep(1)

                            # 尝试点击按钮
                            first_button.click()
                            print("✅ 已点击第一个选择代理按钮")

                            # 检测"编辑成功"提示
                            if self.check_edit_success_message():
                                print("✅ 检测到编辑成功提示")
                                return True
                            else:
                                print("❌ 未检测到编辑成功提示，继续等待...")
                                time.sleep(check_interval)
                                continue

                        except Exception as click_error:
                            print(f"点击按钮失败: {click_error}")
                            try:
                                # 尝试JavaScript点击
                                self.driver.execute_script("arguments[0].click();", first_button)
                                print("✅ 已通过JavaScript点击第一个选择代理按钮")

                                if self.check_edit_success_message():
                                    print("✅ 检测到编辑成功提示")
                                    return True
                                else:
                                    print("❌ 未检测到编辑成功提示，继续等待...")
                                    time.sleep(check_interval)
                                    continue
                            except Exception as js_error:
                                print(f"JavaScript点击也失败: {js_error}")
                    else:
                        print("未找到选择代理按钮，继续等待...")
                        time.sleep(check_interval)

                except Exception as e:
                    print(f"查找代理表格或按钮时出错: {e}")
                    time.sleep(check_interval)

            print(f"❌ 等待{max_wait_seconds}秒后未能成功选择代理")
            return False

        except Exception as e:
            print(f"选择第一个代理时出错: {e}")
            return False

    def click_open_button(self):
        """点击打开按钮的独立方法"""
        max_retries = 3  # 最多重试3次

        for retry in range(max_retries):
            try:
                print(f"正在查找打开按钮... (尝试 {retry + 1}/{max_retries})")

                # 等待加载遮罩层消失
                print("等待页面加载完成...")
                try:
                    # 等待遮罩层消失
                    WebDriverWait(self.driver, 10).until_not(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.arco-spin-mask'))
                    )
                    print("✓ 加载遮罩层已消失")
                except:
                    print("未检测到加载遮罩层或已消失")

                # 额外等待确保页面稳定
                time.sleep(2)

                # 查找所有按钮
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                print(f"页面上找到 {len(buttons)} 个按钮:")

                open_button = None
                for i, btn in enumerate(buttons):
                    btn_text = btn.text.strip()
                    btn_class = btn.get_attribute('class')
                    print(f"  按钮 {i+1}: 文本='{btn_text}', 类名='{btn_class}'")

                    # 检查是否是正确的Open按钮（主要的蓝色按钮）
                    if btn_text == 'Open':
                        # 检查按钮类名，确保是主要按钮（通常包含primary）
                        if 'arco-btn-primary' in btn_class:
                            print(f"找到主要的打开按钮 (按钮 {i+1})，正在点击...")
                            open_button = btn
                            break
                        else:
                            print(f"找到打开按钮但不是主要按钮 (按钮 {i+1})，继续查找...")

                # 如果没找到主要的Open按钮，尝试找任何Open按钮
                if not open_button:
                    for i, btn in enumerate(buttons):
                        btn_text = btn.text.strip()
                        if btn_text == 'Open':
                            print(f"找到备用打开按钮 (按钮 {i+1})，正在点击...")
                            open_button = btn
                            break

                if open_button:
                    # 检查按钮是否可点击
                    if not open_button.is_enabled() or not open_button.is_displayed():
                        print("按钮不可用或不可见，等待后重试...")
                        time.sleep(3)
                        continue

                    # 滚动到按钮位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", open_button)
                    time.sleep(1)

                    # 尝试点击按钮
                    try:
                        open_button.click()
                        print("✅ 成功点击打开按钮")
                        # 点击Open按钮时清空代理切换计数
                        self.notify_reset_proxy_switch_count()
                    except Exception as click_error:
                        print(f"普通点击失败: {click_error}")
                        # 尝试JavaScript点击
                        try:
                            self.driver.execute_script("arguments[0].click();", open_button)
                            print("✅ 通过JavaScript成功点击打开按钮")
                            # 点击Open按钮时清空代理切换计数
                            self.notify_reset_proxy_switch_count()
                        except Exception as js_error:
                            print(f"JavaScript点击也失败: {js_error}")
                            if retry < max_retries - 1:
                                print("等待后重试...")
                                time.sleep(3)
                                continue
                            else:
                                return False

                    # 等待新标签页打开
                    time.sleep(3)

                    # 切换到新打开的标签页
                    if len(self.driver.window_handles) > 1:
                        self.registration_tab = self.driver.window_handles[-1]  # 最新的标签页
                        self.driver.switch_to.window(self.registration_tab)
                        print("已切换到新标签页")
                        time.sleep(2)  # 等待页面加载

                        print("✅ Hitem3D页面已打开")
                        print("💡 脚本已完成Open按钮点击，浏览器将保持打开状态")
                        print("🔔 后续的注册流程请用户根据需要手动操作或编写相关代码")
                        return True
                    else:
                        print("未检测到新标签页")
                        if retry < max_retries - 1:
                            print("等待后重试...")
                            time.sleep(3)
                            continue
                        else:
                            return False

                else:
                    print("未找到打开按钮")
                    if retry < max_retries - 1:
                        print("等待后重试...")
                        time.sleep(3)
                        continue
                    else:
                        return False

            except Exception as e:
                print(f"点击打开按钮失败: {e}")
                if retry < max_retries - 1:
                    print("等待后重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

        print(f"❌ 经过{max_retries}次尝试后仍然失败")
        return False

    def check_page_loaded(self):
        """检查Hitem3D页面是否已经正确加载"""
        try:
            print("正在检查页面加载状态...")
            
            # 等待页面基本元素加载
            time.sleep(3)
            
            # 检查页面标题
            page_title = self.driver.title
            print(f"页面标题: {page_title}")
            
            # 检查是否有基本的页面元素
            try:
                # 查找页面上的按钮数量
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                print(f"页面上找到 {len(buttons)} 个按钮")
                
                # 查找页面上的链接数量
                links = self.driver.find_elements(By.TAG_NAME, "a")
                print(f"页面上找到 {len(links)} 个链接")
                
                # 查找页面上的输入框数量
                inputs = self.driver.find_elements(By.TAG_NAME, "input")
                print(f"页面上找到 {len(inputs)} 个输入框")
                
                # 检查是否有Sign In或Sign up相关的元素
                signin_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'Sign In') or contains(text(), 'Sign in') or contains(text(), 'Sign up') or contains(text(), 'Sign Up')]")
                print(f"页面上找到 {len(signin_elements)} 个包含Sign In或Sign up的元素")

                # 检查当前URL
                current_url = self.driver.current_url
                print(f"当前URL: {current_url}")

                # 如果URL不是Hitem3D相关，可能还在加载中
                if "hitem3d" not in current_url.lower():
                    print("⚠️ 当前URL不是Hitem3D页面")
                    return False

                # 检查页面是否已经加载完成的新逻辑
                # 如果有Sign In或Sign up元素且链接数量>=3，认为页面已加载完成
                if len(signin_elements) > 0 and len(links) >= 3:
                    print("✅ 找到Sign In或Sign up元素且链接数量足够，页面加载完成")
                    return True

                # 如果页面元素太少且没有Sign In或Sign up元素，可能没有正确加载
                if len(buttons) < 2 and len(links) < 3 and len(signin_elements) == 0:
                    print("⚠️ 页面元素数量较少且无Sign In或Sign up元素，可能未完全加载")
                    return False

                # 如果有基本元素但没有达到理想状态，也认为可以继续
                if len(signin_elements) > 0 or (len(buttons) >= 2 and len(links) >= 2):
                    print("✅ 页面基本元素已加载，可以继续")
                    return True

                print("⚠️ 页面加载状态不明确")
                return False
                
            except Exception as e:
                print(f"检查页面元素时出错: {e}")
                return False
                
        except Exception as e:
            print(f"检查页面加载状态时出错: {e}")
            return False

    def handle_hitem3d_signup_flow(self):
        """处理Hitem3D的完整注册流程"""
        try:
            print("🚀 开始处理Hitem3D注册流程...")
            
            # 等待页面加载完成
            time.sleep(5)
            
            # 步骤1: 点击页面头部的Sign In按钮
            print("步骤1: 寻找并点击页面头部的Sign In按钮...")
            if not self.click_header_signup_button():
                print("❌ 无法找到或点击页面头部的Sign In按钮")
                return False

            # 步骤2: 处理登录弹窗，并在其中点击Sign up
            print("步骤2: 处理登录弹窗...")
            if not self.handle_login_modal():
                print("❌ 处理登录弹窗失败")
                return False
            
            # 步骤3: 处理注册表单
            print("步骤3: 处理注册表单...")
            if not self.handle_registration_form():
                print("❌ 处理注册表单失败")
                return False
            
            print("✅ Hitem3D注册流程处理完成！")
            return True
            
        except Exception as e:
            print(f"处理Hitem3D注册流程时出错: {e}")
            return False

    def click_header_signup_button(self):
        """点击页面头部的Sign In或Sign up按钮"""
        try:
            print("正在查找页面头部的Sign In或Sign up按钮...")

            # 等待页面完全加载
            time.sleep(3)

            # 方法1: 根据HTML结构查找Sign In按钮
            signin_button = None
            try:
                # <div data-v-7d392403="" class="header-right"><span data-v-7d392403="" class="sparc-discord"></span><!----><div data-v-f66f491c="" data-v-7d392403="" class="language-container-out"><div data-v-f66f491c="" class="language-container"><div data-v-f66f491c="" class="language">English</div><!----></div></div><button data-v-7d392403="">Sign In</button></div>
                signin_button = self.driver.find_element(
                    By.XPATH, "//div[contains(@class, 'header-right')]//button[text()='Sign In']"
                )
                print("通过方法1找到头部Sign In按钮")
            except:
                pass

            # 方法2: 根据HTML结构查找Sign up按钮
            if not signin_button:
                try:
                    signin_button = self.driver.find_element(
                        By.XPATH, "//div[contains(@class, 'header-right')]//button[text()='Sign up']"
                    )
                    print("通过方法2找到头部Sign up按钮")
                except:
                    pass

            # 方法3: 直接查找包含"Sign In"或"Sign up"文本的按钮
            if not signin_button:
                try:
                    signin_button = self.driver.find_element(
                        By.XPATH, "//button[text()='Sign In' or text()='Sign up']"
                    )
                    print("通过方法3找到Sign In或Sign up按钮")
                except:
                    pass

            # 方法4: 查找包含"Sign In"或"Sign up"的任何按钮
            if not signin_button:
                try:
                    signin_button = self.driver.find_element(
                        By.XPATH, "//button[contains(text(), 'Sign In') or contains(text(), 'Sign up')]"
                    )
                    print("通过方法4找到包含Sign In或Sign up的按钮")
                except:
                    pass

            # 方法5: 查找所有按钮并检查文本
            if not signin_button:
                try:
                    all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    print(f"页面上找到 {len(all_buttons)} 个按钮，检查是否包含Sign In或Sign up...")

                    for i, btn in enumerate(all_buttons):
                        btn_text = btn.text.strip()
                        print(f"按钮 {i+1}: '{btn_text}'")
                        if "Sign In" in btn_text or "Sign up" in btn_text:
                            signin_button = btn
                            print(f"通过方法5在第 {i+1} 个按钮中找到{btn_text}")
                            break
                except Exception as e:
                    print(f"方法5查找失败: {e}")

            if signin_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", signin_button)
                time.sleep(1)

                # 尝试点击按钮
                try:
                    button_text = signin_button.text.strip()
                    signin_button.click()
                    print(f"✅ 成功点击头部{button_text}按钮")
                    time.sleep(3)  # 等待弹窗出现
                    return True
                except Exception as click_error:
                    print(f"普通点击失败: {click_error}")
                    try:
                        button_text = signin_button.text.strip()
                        self.driver.execute_script("arguments[0].click();", signin_button)
                        print(f"✅ 通过JavaScript成功点击头部{button_text}按钮")
                        time.sleep(3)
                        return True
                    except Exception as js_error:
                        print(f"JavaScript点击也失败: {js_error}")
                        return False
            else:
                print("❌ 未找到头部Sign In或Sign up按钮")
                # 保存截图用于调试
                try:
                    screenshot_path = f"no_signin_signup_button_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    print(f"已保存截图: {screenshot_path}")
                except:
                    pass
                return False

        except Exception as e:
            print(f"点击头部Sign In或Sign up按钮失败: {e}")
            return False

    def handle_login_modal(self):
        """处理登录弹窗并点击其中的Sign up"""
        try:
            print("正在处理登录弹窗...")
            
            # 等待弹窗出现
            try:
                # 查找登录弹窗容器
                modal_container = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.login-container'))
                )
                print("✅ 检测到登录弹窗")
            except TimeoutException:
                print("❌ 等待登录弹窗出现超时")
                return False
            
            # 在弹窗中查找Sign up按钮
            try:
                # 根据用户提供的HTML结构：
                # <div data-v-e80a6a36="" class="login-footer"><div data-v-e80a6a36="" class="login-type"><span data-v-e80a6a36="" style="margin-right: 1rem;">Sign up</span> | <span data-v-e80a6a36="" style="margin-left: 1rem;">Forget password</span></div></div>
                
                # 方法1: 根据具体结构查找
                signup_span = None
                try:
                    signup_span = modal_container.find_element(
                        By.XPATH, ".//div[@class='login-footer']//span[text()='Sign up']"
                    )
                    print("通过方法1找到弹窗中的Sign up")
                except:
                    pass
                
                # 方法2: 直接查找包含"Sign up"的span
                if not signup_span:
                    try:
                        signup_span = modal_container.find_element(
                            By.XPATH, ".//span[text()='Sign up']"
                        )
                        print("通过方法2找到弹窗中的Sign up")
                    except:
                        pass
                
                # 方法3: 查找包含"Sign up"的任何可点击元素
                if not signup_span:
                    try:
                        signup_span = modal_container.find_element(
                            By.XPATH, ".//*[contains(text(), 'Sign up')]"
                        )
                        print("通过方法3找到弹窗中的Sign up")
                    except:
                        pass
                
                if signup_span:
                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", signup_span)
                    time.sleep(1)
                    
                    # 尝试点击
                    try:
                        signup_span.click()
                        print("✅ 成功点击弹窗中的Sign up")
                        time.sleep(3)  # 等待新弹窗加载
                        return True
                    except Exception as click_error:
                        print(f"普通点击失败: {click_error}")
                        try:
                            self.driver.execute_script("arguments[0].click();", signup_span)
                            print("✅ 通过JavaScript成功点击弹窗中的Sign up")
                            time.sleep(3)
                            return True
                        except Exception as js_error:
                            print(f"JavaScript点击也失败: {js_error}")
                            return False
                else:
                    print("❌ 在弹窗中未找到Sign up")
                    return False
                    
            except Exception as e:
                print(f"在弹窗中查找Sign up失败: {e}")
                return False
                
        except Exception as e:
            print(f"处理登录弹窗失败: {e}")
            return False

    def handle_registration_form(self):
        """处理注册表单"""
        try:
            print("正在处理注册表单...")
            
            # 等待注册表单加载
            try:
                # 等待注册表单出现（包含Email Code字段的表单）
                self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.login-input-code'))
                )
                print("✅ 检测到注册表单")
            except TimeoutException:
                print("❌ 等待注册表单出现超时")
                return False
            
            # 启动邮箱和验证码文件监控（只有检测到注册表单后才启动）
            print("📡 启动邮箱和验证码文件监控...")
            self.start_file_monitoring()
            print("📧 注册表单已加载，开始监控邮箱和验证码文件...")
            print("请启动 hitem3d_email_automation.py 脚本来获取邮箱和验证码")
            
            # 保持运行，等待文件变化
            try:
                while True:
                    time.sleep(5)  # 主循环只需要保持运行状态
                    # 检查是否所有表单都已填写完成
                    if hasattr(self, '_form_completed') and self._form_completed:
                        print("✅ 表单填写完成，退出监控")
                        break
            except KeyboardInterrupt:
                print("用户中断程序")
                return False
            return True
        except Exception as e:
            print(f"处理注册表单失败: {e}")
            return False

    def start_file_monitoring(self):
        """启动邮箱和验证码文件监控"""
        self.start_email_file_monitoring()
        self.start_verification_code_monitoring()

    def start_email_file_monitoring(self):
        """启动邮箱文件监控"""
        def monitor_email_thread():
            print("开始监控邮箱文件变化...")
            self.monitoring_email_file = True

            while self.monitoring_email_file:
                try:
                    # 检查是否在正确的注册表单页面
                    if not self.is_in_registration_form():
                        time.sleep(2)
                        continue
                    
                    if os.path.exists(self.email_file):
                        with open(self.email_file, 'r', encoding='utf-8') as f:
                            email_content = f.read().strip()

                        if email_content != self.last_email_content and email_content:
                            self.last_email_content = email_content

                            # 检查是否是邮箱格式
                            import re
                            email_regex = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                            is_valid_email = re.match(email_regex, email_content) and '@' in email_content

                            if is_valid_email:
                                if email_content != self.current_email:
                                    print(f"检测到新的邮箱地址: {email_content}")
                                    
                                    # 再次确认在注册表单页面
                                    if self.is_in_registration_form():
                                        # 清空之前的验证码
                                        self.clear_verification_code()
                                        
                                        self.current_email = email_content
                                        # 自动填写邮箱
                                        if self.fill_email(email_content):
                                            print("✅ 邮箱填写完成")
                                            # 自动点击发送验证码按钮
                                            time.sleep(2)
                                            self.click_send_code_button()
                                        else:
                                            print("❌ 邮箱填写失败")
                                    else:
                                        print("⚠️ 不在注册表单页面，跳过邮箱填写")

                    time.sleep(2)

                except Exception as e:
                    print(f"监控邮箱文件时出错: {e}")
                    time.sleep(2)

        email_thread = threading.Thread(target=monitor_email_thread, daemon=True)
        email_thread.start()
        print("邮箱文件监控已启动")

    def is_in_registration_form(self):
        """检查是否在注册表单页面"""
        try:
            # 检查当前页面是否包含注册表单元素
            self.driver.find_element(By.CSS_SELECTOR, '.login-input-code')
            return True
        except:
            return False

    def start_verification_code_monitoring(self):
        """启动验证码文件监控"""
        def monitor_verification_code_thread():
            print("开始监控验证码文件变化...")
            self.monitoring_verification_code_file = True

            while self.monitoring_verification_code_file:
                try:
                    # 检查是否在正确的注册表单页面
                    if not self.is_in_registration_form():
                        time.sleep(2)
                        continue
                    
                    if os.path.exists(self.verification_code_file):
                        with open(self.verification_code_file, 'r', encoding='utf-8') as f:
                            code_content = f.read().strip()

                        if code_content != self.last_verification_code and code_content:
                            self.last_verification_code = code_content

                            # 检查是否是6位数字
                            if code_content.isdigit() and len(code_content) == 6:
                                if code_content != self.current_verification_code:
                                    print(f"检测到新的验证码: {code_content}")
                                    
                                    # 再次确认在注册表单页面
                                    if self.is_in_registration_form():
                                        self.current_verification_code = code_content
                                        # 自动填写验证码
                                        if self.fill_verification_code(code_content):
                                            print("✅ 验证码填写完成")
                                            # 继续填写其他表单字段
                                            time.sleep(2)
                                            self.complete_registration_form()
                                        else:
                                            print("❌ 验证码填写失败")
                                    else:
                                        print("⚠️ 不在注册表单页面，跳过验证码填写")

                    time.sleep(2)

                except Exception as e:
                    print(f"监控验证码文件时出错: {e}")
                    time.sleep(2)

        code_thread = threading.Thread(target=monitor_verification_code_thread, daemon=True)
        code_thread.start()
        print("验证码文件监控已启动")

    def fill_email(self, email):
        """填写邮箱地址"""
        try:
            print(f"正在填写邮箱: {email}")

            # 查找邮箱输入框
            email_input = None
            try:
                email_input = self.driver.find_element(
                    By.CSS_SELECTOR, 'input[name="username"], input[placeholder="Email"]'
                )
                print("找到邮箱输入框")
            except:
                try:
                    # 备用方法：查找第一个text类型的输入框
                    email_input = self.driver.find_element(
                        By.CSS_SELECTOR, 'input[type="text"]'
                    )
                    print("通过备用方法找到邮箱输入框")
                except Exception as e:
                    print(f"无法找到邮箱输入框: {e}")
                    return False

            if email_input:
                # 清空并填写邮箱
                email_input.clear()
                time.sleep(0.5)
                email_input.send_keys(email)
                print(f"✅ 已输入邮箱: {email}")
                return True
            else:
                print("❌ 未找到邮箱输入框")
                return False

        except Exception as e:
            print(f"填写邮箱失败: {e}")
            return False

    def click_send_code_button(self):
        """点击发送验证码按钮，并检测倒计时状态"""
        try:
            print("正在查找并点击发送验证码按钮...")

            # 最多尝试2次点击
            for attempt in range(1, 3):
                print(f"🔄 第{attempt}次尝试点击发送按钮...")

                # 查找发送按钮
                send_button = self.find_send_button()
                if not send_button:
                    print("❌ 未找到发送验证码按钮")
                    return False

                # 点击发送按钮
                if self.click_send_button_element(send_button):
                    print(f"✅ 第{attempt}次点击发送按钮成功")

                    # 等待5秒后检测倒计时状态
                    print("⏳ 等待5秒后检测倒计时状态...")
                    time.sleep(5)

                    # 检测是否变成倒计时状态
                    if self.check_countdown_status():
                        print("✅ 验证码发送成功，按钮已变成倒计时状态")
                        return True
                    else:
                        print(f"⚠️ 第{attempt}次点击后未检测到倒计时状态")
                        if attempt < 2:
                            print("🔄 将进行第2次尝试...")
                            time.sleep(2)
                        else:
                            print("❌ 2次尝试后仍未成功，可能需要手动处理")
                            return False
                else:
                    print(f"❌ 第{attempt}次点击发送按钮失败")
                    if attempt < 2:
                        time.sleep(2)
                    else:
                        return False

            return False

        except Exception as e:
            print(f"点击发送验证码按钮失败: {e}")
            return False

    def find_send_button(self):
        """查找发送验证码按钮"""
        try:
            # 方法1: 根据HTML结构查找
            try:
                send_button = self.driver.find_element(
                    By.CSS_SELECTOR, '.login-btn-email-code'
                )
                print("找到发送验证码按钮")
                return send_button
            except:
                pass

            # 方法2: 备用方法，查找包含"Send"文本的按钮
            try:
                send_button = self.driver.find_element(
                    By.XPATH, "//a[text()='Send'] | //button[text()='Send']"
                )
                print("通过备用方法找到发送按钮")
                return send_button
            except:
                pass

            print("❌ 所有方法都未找到发送按钮")
            return None

        except Exception as e:
            print(f"查找发送按钮时出错: {e}")
            return None

    def click_send_button_element(self, send_button):
        """点击发送按钮元素"""
        try:
            # 尝试普通点击
            try:
                send_button.click()
                print("✅ 普通点击成功")
                return True
            except Exception as click_error:
                print(f"普通点击失败: {click_error}")

                # 尝试JavaScript点击
                try:
                    self.driver.execute_script("arguments[0].click();", send_button)
                    print("✅ JavaScript点击成功")
                    return True
                except Exception as js_error:
                    print(f"JavaScript点击失败: {js_error}")
                    return False

        except Exception as e:
            print(f"点击按钮元素时出错: {e}")
            return False

    def check_countdown_status(self):
        """检测发送按钮是否变成倒计时状态"""
        try:
            print("🔍 检测发送按钮倒计时状态...")

            # 查找发送按钮
            try:
                send_button = self.driver.find_element(
                    By.CSS_SELECTOR, '.login-btn-email-code'
                )

                # 检查按钮文本是否包含数字和's'（倒计时格式如"54s"）
                button_text = send_button.text.strip()
                print(f"按钮当前文本: '{button_text}'")

                # 检查是否是倒计时格式（数字+s）
                import re
                countdown_pattern = r'^\d+s$'
                if re.match(countdown_pattern, button_text):
                    print(f"✅ 检测到倒计时状态: {button_text}")
                    return True

                # 检查按钮是否被禁用
                disabled_attr = send_button.get_attribute('disabled')
                class_attr = send_button.get_attribute('class')

                print(f"按钮disabled属性: {disabled_attr}")
                print(f"按钮class属性: {class_attr}")

                # 如果按钮被禁用且包含disabled class，也认为是倒计时状态
                if disabled_attr == 'true' and 'disabled' in class_attr:
                    print("✅ 按钮已被禁用，可能处于倒计时状态")
                    return True

                print("❌ 未检测到倒计时状态")
                return False

            except Exception as e:
                print(f"查找发送按钮失败: {e}")
                return False

        except Exception as e:
            print(f"检测倒计时状态时出错: {e}")
            return False

    def fill_verification_code(self, code):
        """填写验证码"""
        try:
            print(f"正在填写验证码: {code}")

            # 查找验证码输入框
            code_input = None
            try:
                code_input = self.driver.find_element(
                    By.CSS_SELECTOR, '.login-input-code'
                )
                print("找到验证码输入框")
            except:
                try:
                    # 备用方法：查找placeholder包含"Code"的输入框
                    code_input = self.driver.find_element(
                        By.CSS_SELECTOR, 'input[placeholder*="Code"]'
                    )
                    print("通过备用方法找到验证码输入框")
                except Exception as e:
                    print(f"无法找到验证码输入框: {e}")
                    return False

            if code_input:
                # 清空并填写验证码
                code_input.clear()
                time.sleep(0.5)
                code_input.send_keys(code)
                print(f"✅ 已输入验证码: {code}")
                return True
            else:
                print("❌ 未找到验证码输入框")
                return False

        except Exception as e:
            print(f"填写验证码失败: {e}")
            return False

    def complete_registration_form(self):
        """完成注册表单的其他字段填写"""
        try:
            print("正在完成注册表单的其他字段...")

            # 填写密码
            if not self.fill_passwords():
                print("密码填写失败，但继续其他字段")

            # 填写昵称
            if not self.fill_nickname():
                print("昵称填写失败，但继续其他字段")

            # 提交表单
            if self.submit_registration_form():
                print("✅ 注册表单提交成功！")
                self._form_completed = True
                return True
            else:
                print("❌ 注册表单提交失败")
                return False

        except Exception as e:
            print(f"完成注册表单失败: {e}")
            return False

    def fill_passwords(self):
        """填写密码和确认密码"""
        try:
            print("正在填写密码...")

            # 查找密码输入框
            password_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
            
            if len(password_inputs) >= 2:
                # 第一个是密码，第二个是确认密码
                password_input = password_inputs[0]
                confirm_password_input = password_inputs[1]
                
                # 填写密码
                password_input.clear()
                time.sleep(0.5)
                password_input.send_keys(self.password)
                print("✅ 已输入密码")
                
                # 填写确认密码
                confirm_password_input.clear()
                time.sleep(0.5)
                confirm_password_input.send_keys(self.confirm_password)
                print("✅ 已输入确认密码")
                
                return True
            else:
                print(f"❌ 找到的密码输入框数量不足: {len(password_inputs)}")
                return False

        except Exception as e:
            print(f"填写密码失败: {e}")
            return False

    def fill_nickname(self):
        """填写昵称"""
        try:
            print("正在填写昵称...")

            # 查找昵称输入框
            nickname_input = None
            try:
                nickname_input = self.driver.find_element(
                    By.CSS_SELECTOR, 'input[placeholder="Nickname"]'
                )
                print("找到昵称输入框")
            except Exception as e:
                print(f"无法找到昵称输入框: {e}")
                return False

            if nickname_input:
                # 生成随机昵称
                import random
                import string
                random_suffix = ''.join(random.choices(string.digits, k=4))
                nickname = f"{self.nickname}{random_suffix}"
                
                # 清空并填写昵称
                nickname_input.clear()
                time.sleep(0.5)
                nickname_input.send_keys(nickname)
                print(f"✅ 已输入昵称: {nickname}")
                return True
            else:
                print("❌ 未找到昵称输入框")
                return False

        except Exception as e:
            print(f"填写昵称失败: {e}")
            return False

    def submit_registration_form(self):
        """提交注册表单"""
        try:
            print("正在查找并点击Sign Up按钮...")

            # 查找Sign Up提交按钮
            submit_button = None
            try:
                submit_button = self.driver.find_element(
                    By.CSS_SELECTOR, '.login-btn-sign'
                )
                print("找到Sign Up提交按钮")
            except:
                try:
                    # 备用方法：查找包含"Sign Up"文本的按钮
                    submit_button = self.driver.find_element(
                        By.XPATH, "//a[contains(text(), 'Sign Up')] | //button[contains(text(), 'Sign Up')]"
                    )
                    print("通过备用方法找到Sign Up按钮")
                except Exception as e:
                    print(f"无法找到Sign Up按钮: {e}")
                    return False

            if submit_button:
                # 滚动到按钮位置
                self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                time.sleep(1)

                # 尝试点击按钮
                try:
                    submit_button.click()
                    print("✅ 成功点击Sign Up按钮")
                    
                    # 等待提交结果
                    time.sleep(5)
                    
                    # 检查是否有成功提示或错误信息
                    if self.check_registration_result():
                        print("✅ 注册成功！")
                        return True
                    else:
                        print("⚠️ 注册结果未知或可能失败")
                        return False
                        
                except Exception as click_error:
                    print(f"普通点击失败: {click_error}")
                    try:
                        self.driver.execute_script("arguments[0].click();", submit_button)
                        print("✅ 通过JavaScript成功点击Sign Up按钮")
                        
                        time.sleep(5)
                        
                        if self.check_registration_result():
                            print("✅ 注册成功！")
                            return True
                        else:
                            print("⚠️ 注册结果未知或可能失败")
                            return False
                            
                    except Exception as js_error:
                        print(f"JavaScript点击也失败: {js_error}")
                        return False
            else:
                print("❌ 未找到Sign Up按钮")
                return False

        except Exception as e:
            print(f"提交注册表单失败: {e}")
            return False

    def check_registration_result(self):
        """检查注册结果"""
        try:
            print("正在检查注册结果...")
            
            # 检查是否有成功提示
            success_indicators = [
                "Registration successful",
                "Account created",
                "Welcome",
                "Success",
                "注册成功",
                "账户创建成功"
            ]
            
            # 检查是否有错误提示
            error_indicators = [
                "Error",
                "Failed",
                "Invalid",
                "Already exists",
                "错误",
                "失败",
                "已存在"
            ]
            
            page_text = self.driver.page_source.lower()
            
            # 检查成功指示
            for indicator in success_indicators:
                if indicator.lower() in page_text:
                    print(f"✅ 检测到成功指示: {indicator}")
                    # 注册成功，获取Cookie并保存账号信息
                    self.handle_successful_registration()
                    return True
            
            # 检查错误指示
            for indicator in error_indicators:
                if indicator.lower() in page_text:
                    print(f"⚠️ 检测到错误指示: {indicator}")
                    return False
            
            # 如果没有明确的成功或错误指示，检查页面变化
            current_url = self.driver.current_url
            print(f"当前URL: {current_url}")
            
            # 如果URL发生变化或页面内容发生变化，可能表示注册成功
            # 注册成功，获取Cookie并保存账号信息
            self.handle_successful_registration()
            return True
            
        except Exception as e:
            print(f"检查注册结果时出错: {e}")
            return False

    def clear_verification_code(self):
        """清空验证码文件"""
        try:
            # 清空验证码文件
            with open(self.verification_code_file, 'w', encoding='utf-8') as f:
                f.write("")
            print("已清空验证码文件")
            
            # 重置验证码相关变量
            self.current_verification_code = ""
            self.last_verification_code = ""
            
        except Exception as e:
            print(f"清空验证码文件失败: {e}")

    def get_cookies(self):
        """获取当前页面的所有Cookie"""
        try:
            cookies = self.driver.get_cookies()
            print(f"获取到 {len(cookies)} 个Cookie")

            # 打印所有Cookie的名称，用于调试
            cookie_names = [cookie['name'] for cookie in cookies]
            print(f"Cookie名称列表: {cookie_names}")

            # 特别检查重要的Cookie
            important_cookies = ['JSESSIONID', 'MM_AIGC', 'MM_AIGC_GUEST']
            for cookie_name in important_cookies:
                found = any(cookie['name'] == cookie_name for cookie in cookies)
                print(f"🔍 {cookie_name}: {'✅ 存在' if found else '❌ 不存在'}")

            return cookies
        except Exception as e:
            print(f"获取Cookie失败: {e}")
            return []

    def get_jsessionid_cookie(self):
        """获取JSESSIONID Cookie"""
        try:
            cookies = self.driver.get_cookies()
            for cookie in cookies:
                if cookie['name'] == 'JSESSIONID':
                    print(f"获取到JSESSIONID: {cookie['value']}")
                    return cookie['value']
            print("未找到JSESSIONID Cookie")
            return None
        except Exception as e:
            print(f"获取JSESSIONID失败: {e}")
            return None

    def save_account_info(self, email, password, cookies):
        """保存账号信息到文件"""
        try:
            import json
            from datetime import datetime
            
            # 读取现有账号信息
            accounts = []
            if os.path.exists(self.accounts_file):
                try:
                    with open(self.accounts_file, 'r', encoding='utf-8') as f:
                        accounts = json.load(f)
                except:
                    accounts = []
            
            # 将Cookie转换为简化的格式 {name: value}
            simplified_cookies = {}
            for cookie in cookies:
                simplified_cookies[cookie['name']] = cookie['value']
            
            # 准备新的账号信息
            account_info = {
                'email': email,
                'password': password,
                'cookies': simplified_cookies,
                'created_at': datetime.now().isoformat(),
                'status': 'active'
            }
            
            # 添加到账号列表
            accounts.append(account_info)
            
            # 保存到文件
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 账号信息已保存到 {self.accounts_file}")
            print(f"📧 邮箱: {email}")
            print(f"🔑 密码: {password}")
            print(f"🍪 Cookie数量: {len(simplified_cookies)}")
            
            # 显示主要的Cookie
            if 'JSESSIONID' in simplified_cookies:
                print(f"🔐 JSESSIONID: {simplified_cookies['JSESSIONID']}")
            if 'MM_AIGC_GUEST' in simplified_cookies:
                print(f"👤 MM_AIGC_GUEST: {simplified_cookies['MM_AIGC_GUEST'][:50]}...")
            if 'MM_AIGC' in simplified_cookies:
                print(f"🎫 MM_AIGC: {simplified_cookies['MM_AIGC'][:50]}...")
                print(f"✅ MM_AIGC Cookie获取成功！长度: {len(simplified_cookies['MM_AIGC'])}")
            else:
                print("❌ 警告：未获取到MM_AIGC Cookie！")
                print("🔍 当前所有Cookie:")
                for name, value in simplified_cookies.items():
                    print(f"   - {name}: {value[:30]}...")

            return True
            
        except Exception as e:
            print(f"保存账号信息失败: {e}")
            return False

    def handle_successful_registration(self):
        """处理注册成功后的操作"""
        try:
            print("🎉 注册成功！开始获取账号信息...")

            # 停止文件监控
            self.stop_file_monitoring()

            # 等待页面完全加载并尝试多次获取Cookie
            cookies = self.get_cookies_with_retry()

            # 保存账号信息
            if self.save_account_info(self.current_email, self.password, cookies):
                print("✅ 账号信息保存成功")
            else:
                print("❌ 账号信息保存失败")

            # 创建注册完成标记文件
            self.create_registration_completed_marker()

            print("🎊 注册流程完成！账号信息已保存")

            # 自动清理登录信息并关闭标签页
            self.auto_logout_and_close()

        except Exception as e:
            print(f"处理注册成功操作时出错: {e}")

    def auto_logout_and_close(self):
        """自动退出登录并关闭标签页"""
        try:
            print("🚪 开始自动退出登录并关闭标签页...")

            # 等待一下确保页面稳定
            time.sleep(2)

            # 方法1: 尝试执行退出登录的JavaScript（先执行API调用）
            print("🔓 执行退出登录操作...")
            self.execute_logout_script()

            # 等待退出操作完成
            time.sleep(3)

            # 方法2: 清理登录相关的Cookie和本地存储
            print("🧹 清理登录信息...")
            self.clear_login_data()

            # 方法3: 再次验证并强制清理（暂时注释掉用于测试）
            # print("🔍 验证登录状态清理...")
            # self.verify_and_force_logout()

            # 等待清理完成
            # time.sleep(2)

            # 关闭当前标签页
            print("❌ 关闭当前标签页...")
            self.close_current_tab()

            # 切换回代理页面
            print("🔄 切换回代理页面...")
            self.switch_to_proxy_tab()

            # 开始下一轮注册
            print("🚀 开始下一轮注册...")
            self.restart_registration_cycle()

        except Exception as e:
            print(f"自动退出登录并关闭标签页失败: {e}")
            # 如果出错，至少尝试关闭标签页
            try:
                self.close_current_tab()
                self.switch_to_proxy_tab()
                self.restart_registration_cycle()
            except:
                pass

    def verify_and_force_logout(self):
        """验证并强制退出登录"""
        try:
            # 检查是否还有登录相关的Cookie
            remaining_cookies = self.driver.get_cookies()
            login_cookies = [c for c in remaining_cookies if any(keyword in c['name'].lower()
                           for keyword in ['aigc', 'auth', 'login', 'user', 'session', 'token'])]

            if login_cookies:
                print(f"⚠️ 仍有 {len(login_cookies)} 个登录相关Cookie，强制清理...")
                for cookie in login_cookies:
                    try:
                        self.driver.delete_cookie(cookie['name'])
                        print(f"🗑️ 强制删除: {cookie['name']}")
                    except:
                        pass

            # 强制刷新页面以确保状态更新
            print("🔄 刷新页面确保状态更新...")
            self.driver.refresh()
            time.sleep(3)

            # 再次清理所有存储
            self.driver.execute_script("""
                // 强制清理所有可能的存储
                try {
                    localStorage.clear();
                    sessionStorage.clear();

                    // 清理IndexedDB
                    if (window.indexedDB) {
                        indexedDB.databases().then(databases => {
                            databases.forEach(db => {
                                indexedDB.deleteDatabase(db.name);
                            });
                        }).catch(() => {});
                    }

                    // 清理WebSQL（如果支持）
                    if (window.openDatabase) {
                        try {
                            var db = openDatabase('', '', '', '');
                            db.transaction(function(tx) {
                                tx.executeSql('DROP TABLE IF EXISTS user_data');
                            });
                        } catch(e) {}
                    }

                    console.log('强制清理完成');
                } catch(e) {
                    console.log('强制清理时出错:', e);
                }
            """)

            print("✅ 强制退出登录验证完成")

        except Exception as e:
            print(f"验证并强制退出登录失败: {e}")

    def clear_login_data(self):
        """清理登录相关的数据"""
        try:
            # 1. 清理登录相关的Cookie
            print("🍪 清理登录相关的Cookie...")
            self.clear_login_cookies()

            # 2. 清理本地存储
            print("💾 清理本地存储...")
            self.driver.execute_script("""
                // 清理localStorage
                if (window.localStorage) {
                    localStorage.clear();
                    console.log('localStorage已清理');
                }

                // 清理sessionStorage
                if (window.sessionStorage) {
                    sessionStorage.clear();
                    console.log('sessionStorage已清理');
                }

                // 清理可能的用户状态
                if (window.userState) {
                    window.userState = null;
                }

                if (window.currentUser) {
                    window.currentUser = null;
                }
            """)
            print("✅ 本地存储数据已清理")

        except Exception as e:
            print(f"清理登录数据失败: {e}")

    def clear_login_cookies(self):
        """清理登录相关的Cookie"""
        try:
            # 获取当前所有Cookie
            cookies = self.driver.get_cookies()
            print(f"当前有 {len(cookies)} 个Cookie")

            # 需要清理的登录相关Cookie
            login_cookies_to_clear = [
                'MM_AIGC',           # 主要的登录Cookie
                'MM_AIGC_GUEST',     # 访客Cookie
                'JSESSIONID',        # 会话Cookie
                'auth_token',        # 可能的认证token
                'user_token',        # 用户token
                'session_id',        # 会话ID
                'login_token',       # 登录token
                'access_token',      # 访问token
                'refresh_token'      # 刷新token
            ]

            # 删除登录相关的Cookie
            deleted_count = 0
            for cookie in cookies:
                cookie_name = cookie['name']

                # 检查是否是需要清理的Cookie
                should_delete = False

                # 精确匹配
                if cookie_name in login_cookies_to_clear:
                    should_delete = True

                # 模糊匹配（包含关键词的Cookie）
                login_keywords = ['auth', 'login', 'user', 'session', 'token', 'aigc']
                if any(keyword.lower() in cookie_name.lower() for keyword in login_keywords):
                    should_delete = True

                if should_delete:
                    try:
                        self.driver.delete_cookie(cookie_name)
                        print(f"🗑️ 已删除Cookie: {cookie_name}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"删除Cookie {cookie_name} 失败: {e}")

            print(f"✅ 共删除了 {deleted_count} 个登录相关的Cookie")

            # 验证Cookie是否被删除
            remaining_cookies = self.driver.get_cookies()
            remaining_names = [c['name'] for c in remaining_cookies]
            print(f"剩余Cookie: {remaining_names}")

        except Exception as e:
            print(f"清理登录Cookie失败: {e}")

    def execute_logout_script(self):
        """执行退出登录的JavaScript脚本"""
        try:
            # 执行多种可能的退出登录方法
            self.driver.execute_script("""
                console.log('开始执行退出登录脚本...');

                // 方法1: 尝试调用可能的退出登录函数
                var logoutMethods = ['logout', 'signOut', 'logOut', 'userLogout', 'authLogout'];
                logoutMethods.forEach(function(method) {
                    if (window[method] && typeof window[method] === 'function') {
                        try {
                            window[method]();
                            console.log('调用了window.' + method + '()');
                        } catch(e) {
                            console.log('调用' + method + '失败:', e);
                        }
                    }
                });

                // 方法2: 尝试发送退出登录请求到多个可能的端点
                if (window.fetch) {
                    var logoutUrls = [
                        '/api/auth/logout',
                        '/api/user/logout',
                        '/api/logout',
                        '/logout',
                        '/auth/logout',
                        '/user/logout',
                        '/signout',
                        '/api/auth/signout'
                    ];

                    logoutUrls.forEach(function(url) {
                        fetch(url, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        }).then(function(response) {
                            console.log('退出登录请求到', url, '响应:', response.status);
                        }).catch(function(error) {
                            console.log('退出登录请求到', url, '失败:', error);
                        });

                        // 也尝试GET请求
                        fetch(url, {
                            method: 'GET',
                            credentials: 'include'
                        }).catch(() => {});
                    });

                    console.log('已发送所有退出登录请求');
                }

                // 方法3: 清除可能的认证token
                var tokenKeys = ['authToken', 'accessToken', 'userToken', 'jwt', 'token'];
                tokenKeys.forEach(function(key) {
                    if (window[key]) {
                        window[key] = null;
                        delete window[key];
                    }
                });

                // 方法4: 触发退出登录事件
                var events = ['logout', 'signout', 'userLogout', 'authLogout', 'beforeunload'];
                events.forEach(function(eventName) {
                    try {
                        document.dispatchEvent(new Event(eventName));
                        window.dispatchEvent(new Event(eventName));
                    } catch(e) {}
                });

                // 方法5: 尝试重置用户相关的全局变量
                var userVars = ['currentUser', 'user', 'userInfo', 'userData', 'authData', 'userState'];
                userVars.forEach(function(varName) {
                    if (window[varName]) {
                        window[varName] = null;
                        delete window[varName];
                    }
                });

                console.log('退出登录脚本执行完成');
            """)
            print("✅ 退出登录脚本执行完成")

        except Exception as e:
            print(f"执行退出登录脚本失败: {e}")

    def get_cookies_with_retry(self, max_attempts=5):
        """多次尝试获取Cookie，特别是MM_AIGC"""
        try:
            print("🍪 开始多次尝试获取Cookie...")

            for attempt in range(1, max_attempts + 1):
                print(f"🔄 第{attempt}次尝试获取Cookie...")

                # 等待一段时间让页面完全加载
                wait_time = 3 + (attempt - 1) * 2  # 递增等待时间
                print(f"⏳ 等待 {wait_time} 秒让页面完全加载...")
                time.sleep(wait_time)

                # 刷新页面以确保最新状态
                if attempt > 1:
                    print("🔄 刷新页面以获取最新Cookie...")
                    self.driver.refresh()
                    time.sleep(3)

                # 检查登录状态
                login_status = self.check_login_status()
                print(f"🔍 登录状态检查: {login_status}")

                # 获取Cookie
                cookies = self.get_cookies()

                # 检查是否获取到MM_AIGC
                mm_aigc_found = any(cookie['name'] == 'MM_AIGC' for cookie in cookies)

                if mm_aigc_found:
                    print(f"✅ 第{attempt}次尝试成功获取到MM_AIGC Cookie！")
                    return cookies
                else:
                    print(f"⚠️ 第{attempt}次尝试未获取到MM_AIGC Cookie")

                    # 如果没有获取到，尝试一些可能触发Cookie生成的操作
                    if attempt <= 2:  # 只在前两次尝试中执行
                        self.try_trigger_login_state()

                    # 如果不是最后一次尝试，继续
                    if attempt < max_attempts:
                        print(f"🔄 将在 2 秒后进行第{attempt + 1}次尝试...")
                        time.sleep(2)

            print("⚠️ 多次尝试后仍未获取到MM_AIGC Cookie，尝试触发Cookie生成...")

            # 尝试触发MM_AIGC Cookie的生成
            self.try_trigger_mm_aigc_cookie()

            # 最后一次获取Cookie
            final_cookies = self.get_cookies()
            return final_cookies

        except Exception as e:
            print(f"多次获取Cookie时出错: {e}")
            # 如果出错，至少尝试获取一次基本Cookie
            return self.get_cookies()

    def try_trigger_mm_aigc_cookie(self):
        """尝试触发MM_AIGC Cookie的生成"""
        try:
            print("🎯 尝试触发MM_AIGC Cookie生成...")

            # 方法1: 尝试访问用户相关的API或页面元素
            try:
                # 检查页面是否有用户信息相关的元素
                user_elements = self.driver.find_elements(By.CSS_SELECTOR, '[class*="user"], [class*="avatar"], [class*="profile"]')
                if user_elements:
                    print(f"找到 {len(user_elements)} 个用户相关元素，尝试交互...")
                    for element in user_elements[:3]:  # 只尝试前3个
                        try:
                            self.driver.execute_script("arguments[0].click();", element)
                            time.sleep(1)
                        except:
                            pass
            except Exception as e:
                print(f"尝试用户元素交互失败: {e}")

            # 方法2: 执行JavaScript来尝试触发认证相关的请求
            try:
                self.driver.execute_script("""
                    // 尝试触发可能生成MM_AIGC Cookie的操作
                    if (window.fetch) {
                        // 尝试发送一个用户信息请求
                        fetch('/api/user/info', {
                            method: 'GET',
                            credentials: 'include'
                        }).catch(() => {});

                        // 尝试发送认证检查请求
                        fetch('/api/auth/check', {
                            method: 'GET',
                            credentials: 'include'
                        }).catch(() => {});
                    }

                    // 触发可能的用户状态检查
                    if (window.checkUserStatus) {
                        window.checkUserStatus();
                    }

                    console.log('已尝试触发MM_AIGC Cookie生成');
                """)
                print("✅ 已执行JavaScript触发脚本")
                time.sleep(3)  # 等待请求完成
            except Exception as e:
                print(f"执行JavaScript触发脚本失败: {e}")

            # 方法3: 尝试刷新页面并等待
            try:
                print("🔄 刷新页面并等待Cookie更新...")
                self.driver.refresh()
                time.sleep(5)
            except Exception as e:
                print(f"刷新页面失败: {e}")

        except Exception as e:
            print(f"触发MM_AIGC Cookie生成时出错: {e}")

    def check_login_status(self):
        """检查当前页面的登录状态"""
        try:
            # 检查页面URL
            current_url = self.driver.current_url
            print(f"当前页面URL: {current_url}")

            # 检查页面标题
            page_title = self.driver.title
            print(f"页面标题: {page_title}")

            # 检查是否有登录相关的元素
            login_indicators = [
                "登录", "login", "sign in", "用户", "user", "avatar", "profile",
                "退出", "logout", "sign out", "账户", "account"
            ]

            page_source = self.driver.page_source.lower()
            found_indicators = [indicator for indicator in login_indicators if indicator in page_source]

            if found_indicators:
                print(f"找到登录相关指示器: {found_indicators}")
                return "可能已登录"
            else:
                print("未找到明显的登录指示器")
                return "登录状态不明"

        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return "检查失败"

    def try_trigger_login_state(self):
        """尝试触发登录状态相关的操作"""
        try:
            print("🔄 尝试触发登录状态...")

            # 方法1: 尝试点击可能的用户相关按钮
            user_selectors = [
                '[class*="user"]', '[class*="avatar"]', '[class*="profile"]',
                '[class*="account"]', '[id*="user"]', '[id*="avatar"]'
            ]

            for selector in user_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"找到用户相关元素: {selector}")
                        # 尝试悬浮而不是点击，避免跳转
                        self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('mouseover', {bubbles: true}));", elements[0])
                        time.sleep(1)
                        break
                except:
                    continue

            # 方法2: 执行可能触发认证检查的JavaScript
            self.driver.execute_script("""
                // 尝试触发用户状态检查
                if (window.location.pathname.includes('signup') || window.location.pathname.includes('register')) {
                    // 如果在注册页面，尝试触发注册完成后的状态检查
                    setTimeout(function() {
                        if (window.checkAuthStatus) window.checkAuthStatus();
                        if (window.updateUserInfo) window.updateUserInfo();
                        if (window.refreshUserData) window.refreshUserData();
                    }, 1000);
                }

                // 触发可能的Cookie更新
                document.dispatchEvent(new Event('userStateChange'));
                console.log('已触发登录状态检查');
            """)

            time.sleep(2)
            print("✅ 登录状态触发操作完成")

        except Exception as e:
            print(f"触发登录状态操作失败: {e}")

    def add_avatar_logout_popup(self):
        """添加头像和Log Out弹窗功能"""
        try:
            print("🎭 正在添加头像和Log Out弹窗功能...")

            # 注入JavaScript代码来创建头像和弹窗
            self.driver.execute_script("""
                // 创建头像和弹窗的HTML结构
                function createAvatarLogoutPopup() {
                    // 检查是否已经存在
                    if (document.querySelector('.avatar-account-container')) {
                        console.log('头像弹窗已存在');
                        return;
                    }

                    // 创建头像容器
                    const avatarContainer = document.createElement('div');
                    avatarContainer.className = 'avatar-account-container';
                    avatarContainer.setAttribute('data-v-d979ccb2', '');

                    // 设置容器样式
                    avatarContainer.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 10000;
                        cursor: pointer;
                    `;

                    // 创建头像图片
                    const avatarImg = document.createElement('img');
                    avatarImg.className = 'avatar-account';
                    avatarImg.setAttribute('data-v-d979ccb2', '');
                    avatarImg.src = 'https://static.hitem3d.ai/aigc/web/images/avatar/user-HMQoCE-2.png';
                    avatarImg.alt = '';
                    avatarImg.style.cssText = `
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        border: 2px solid #fff;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                    `;

                    // 创建弹窗
                    const logoutDiv = document.createElement('div');
                    logoutDiv.className = 'logout';
                    logoutDiv.setAttribute('data-v-d979ccb2', '');
                    logoutDiv.style.cssText = `
                        position: absolute;
                        top: 50px;
                        right: 0;
                        background: white;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 10px 15px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                        display: none;
                        white-space: nowrap;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    `;

                    // 创建图标
                    const iconSpan = document.createElement('span');
                    iconSpan.className = 'sparc-logout';
                    iconSpan.setAttribute('data-v-d979ccb2', '');
                    iconSpan.style.cssText = `
                        font-size: 2rem;
                        margin-right: 8px;
                        color: #666;
                    `;
                    iconSpan.textContent = '🚪'; // 使用emoji作为图标

                    // 创建文本
                    const textSpan = document.createElement('span');
                    textSpan.className = 'logout-text';
                    textSpan.setAttribute('data-v-d979ccb2', '');
                    textSpan.style.cssText = `
                        color: #333;
                        font-size: 14px;
                    `;
                    textSpan.textContent = 'Log Out';

                    // 组装弹窗
                    logoutDiv.appendChild(iconSpan);
                    logoutDiv.appendChild(textSpan);

                    // 组装容器
                    avatarContainer.appendChild(avatarImg);
                    avatarContainer.appendChild(logoutDiv);

                    // 添加到页面
                    document.body.appendChild(avatarContainer);

                    // 添加鼠标悬浮事件
                    avatarContainer.addEventListener('mouseenter', function() {
                        logoutDiv.style.display = 'block';
                        console.log('显示Log Out弹窗');
                    });

                    avatarContainer.addEventListener('mouseleave', function() {
                        logoutDiv.style.display = 'none';
                        console.log('隐藏Log Out弹窗');
                    });

                    // 添加点击Log Out事件
                    logoutDiv.addEventListener('click', function(e) {
                        e.stopPropagation();
                        console.log('点击了Log Out按钮');

                        // 设置标记，表示用户点击了Log Out
                        window.logoutClicked = true;

                        // 可以在这里添加退出登录的逻辑
                        alert('正在退出登录...');

                        // 关闭当前标签页
                        setTimeout(function() {
                            window.close();
                        }, 1000);
                    });

                    console.log('✅ 头像和Log Out弹窗已创建');
                }

                // 立即创建
                createAvatarLogoutPopup();

                // 如果页面动态加载，可能需要延迟创建
                setTimeout(createAvatarLogoutPopup, 2000);
            """)

            print("✅ 头像和Log Out弹窗功能已添加")
            return True

        except Exception as e:
            print(f"添加头像和Log Out弹窗功能失败: {e}")
            return False

    def wait_for_logout_action(self):
        """等待用户点击Log Out操作"""
        try:
            print("⏳ 等待用户点击Log Out...")
            print("💡 请将鼠标悬浮在右上角的头像上，然后点击'Log Out'")

            # 轮询检查是否点击了Log Out
            max_wait_time = 300  # 最多等待5分钟
            check_interval = 2   # 每2秒检查一次
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                try:
                    # 检查JavaScript中设置的标记
                    logout_clicked = self.driver.execute_script("return window.logoutClicked || false;")

                    if logout_clicked:
                        print("✅ 检测到用户点击了Log Out")

                        # 等待一下让页面处理退出逻辑
                        time.sleep(2)

                        # 关闭当前标签页
                        self.close_current_tab()

                        # 切换回代理页面继续循环
                        self.switch_to_proxy_tab()

                        # 继续下一轮注册
                        print("🔄 开始下一轮注册...")
                        self.restart_registration_cycle()

                        return True

                    # 检查标签页是否被手动关闭
                    current_handles = self.driver.window_handles
                    if self.registration_tab not in current_handles:
                        print("✅ 检测到注册标签页已被关闭")

                        # 切换回代理页面继续循环
                        self.switch_to_proxy_tab()

                        # 继续下一轮注册
                        print("🔄 开始下一轮注册...")
                        self.restart_registration_cycle()

                        return True

                except Exception as check_error:
                    print(f"检查Log Out状态时出错: {check_error}")

                time.sleep(check_interval)
                elapsed_time += check_interval

                # 每30秒提醒一次
                if elapsed_time % 30 == 0:
                    print(f"⏳ 已等待 {elapsed_time} 秒，请点击右上角头像的Log Out...")

            print("⚠️ 等待超时，自动继续下一轮注册")

            # 超时后自动关闭当前标签并继续
            self.close_current_tab()
            self.switch_to_proxy_tab()
            self.restart_registration_cycle()

            return False

        except Exception as e:
            print(f"等待Log Out操作时出错: {e}")
            return False

    def switch_to_proxy_tab(self):
        """切换到第一个标签页（代理页面）"""
        try:
            print("正在切换到代理页面标签...")

            # 获取所有标签页
            all_tabs = self.driver.window_handles

            if not all_tabs:
                print("❌ 未检测到任何浏览器标签页！")
                return

            # 总是切换到第一个(也是唯一或代理)标签页，确保当前窗口句柄有效
            try:
                self.driver.switch_to.window(all_tabs[0])
                print("✅ 已切换到代理页面标签 (窗口句柄已刷新)")
            except Exception as switch_err:
                print(f"切换窗口句柄失败: {switch_err}")

        except Exception as e:
            print(f"切换到代理页面标签失败: {e}")

    def close_current_tab(self):
        """关闭当前标签页"""
        try:
            print("正在关闭当前标签页...")
            self.driver.close()
            print("✅ 当前标签页已关闭")
        except Exception as e:
            print(f"关闭标签页失败: {e}")

    def continue_cycle_directly(self):
        """直接继续循环，不切换代理"""
        try:
            print("🔄 直接继续循环，不切换代理...")
            
            # 重新开始注册流程
            print("🔄 重新开始注册流程...")
            # 使用线程来避免阻塞主流程
            import threading
            def continue_cycle():
                try:
                    self.restart_registration_cycle()
                except Exception as e:
                    print(f"继续循环时出错: {e}")
            
            thread = threading.Thread(target=continue_cycle)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            print(f"直接继续循环失败: {e}")

    def switch_proxy_and_continue(self):
        """切换代理并继续循环"""
        try:
            print("🔄 开始切换代理并继续循环...")
            
            # 创建代理切换请求文件
            with open(self.switch_proxy_file, 'w', encoding='utf-8') as f:
                f.write(f"Switch proxy request at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("✅ 已创建代理切换请求文件")
            
            # 等待一下让邮箱脚本处理代理切换
            time.sleep(2)
            
            # 重新开始注册流程
            print("🔄 重新开始注册流程...")
            # 使用线程来避免阻塞主流程
            import threading
            def continue_cycle():
                try:
                    self.restart_registration_cycle()
                except Exception as e:
                    print(f"继续循环时出错: {e}")
            
            thread = threading.Thread(target=continue_cycle)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            print(f"切换代理并继续循环失败: {e}")

    def wait_for_page_loaded_with_retry(self, max_retries=3):
        """页面加载检测，失败时自动刷新并重试，最多max_retries次"""
        for attempt in range(1, max_retries + 1):
            print(f"🔍 第{attempt}次检测页面加载状态...")
            if self.check_page_loaded():
                print("✅ 页面加载完成！")
                return True
            else:
                if attempt < max_retries:
                    print(f"🔄 页面未完全加载，正在刷新页面（第{attempt}次）...")
                    self.driver.refresh()
                    time.sleep(8)
                else:
                    print("❌ 多次刷新后页面仍未加载，关闭当前标签并切换代理重新开始")

                    # 关闭当前标签页
                    self.close_current_tab()

                    # 切换回代理页面
                    self.switch_to_proxy_tab()

                    # 切换代理并重新开始注册循环
                    print("🔄 切换代理并重新开始注册...")
                    self.switch_proxy_and_continue()

                    return False
        return False

    def continue_from_proxy_switch(self):
        """直接点击Open按钮继续循环"""
        try:
            print("🔄 直接点击Open按钮继续循环...")
            print(f"🎭 使用昵称: {self.nickname}")
            
            # 确保先点击Open按钮成功
            print("🎯 第一步：确保点击Open按钮...")
            if not self.click_open_button():
                print("❌ Open按钮点击失败，无法继续")
                return False
            
            print("✅ Open按钮点击成功！")
            print("🌐 Hitem3D页面正在加载...")
            
            # 确保在新标签页中
            if len(self.driver.window_handles) > 1:
                # 切换到最新的标签页（Hitem3D页面）
                self.registration_tab = self.driver.window_handles[-1]
                self.driver.switch_to.window(self.registration_tab)
                print("✅ 已切换到Hitem3D页面标签")
            else:
                print("❌ 未检测到新标签页，Open按钮可能未成功打开新页面")
                return False
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 检查页面加载状态，失败时自动刷新重试
            if not self.wait_for_page_loaded_with_retry():
                # 页面加载失败，wait_for_page_loaded_with_retry已经处理了切换代理的逻辑
                print("⚠️ 页面加载失败，已自动切换代理重新开始")
                return False

            print("✅ 页面加载完成，开始注册流程...")

            # 开始处理Hitem3D注册流程
            print("🚀 开始处理Hitem3D注册流程...")
            if self.handle_hitem3d_signup_flow():
                print("✅ Hitem3D注册流程完成！")
                return True
            else:
                print("⚠️ Hitem3D注册流程未完成或出现错误")
                return False

        except Exception as e:
            print(f"直接点击Open按钮继续循环失败: {e}")
            return False

    def start_registration_cycle(self):
        """开始注册循环"""
        try:
            print("🔄 开始注册循环...")
            print(f"🎭 使用昵称: {self.nickname}")
            
            # 第一步：打开代理页面并点击打开按钮
            print("🎯 第一步：打开代理页面并点击Open按钮...")
            if not self.open_proxy_page():
                print("❌ 代理页面操作失败，无法继续")
                return False
            
            print("✅ Open按钮点击完成！")
            print("🌐 Hitem3D页面正在加载...")
            
            # 确保在新标签页中
            if len(self.driver.window_handles) > 1:
                # 切换到最新的标签页（Hitem3D页面）
                self.registration_tab = self.driver.window_handles[-1]
                self.driver.switch_to.window(self.registration_tab)
                print("✅ 已切换到Hitem3D页面标签")
            else:
                print("❌ 未检测到新标签页，Open按钮可能未成功打开新页面")
                return False
            
            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(5)
            
            # 检查页面加载状态，失败时自动刷新重试
            if not self.wait_for_page_loaded_with_retry():
                # 页面加载失败，wait_for_page_loaded_with_retry已经处理了切换代理的逻辑
                print("⚠️ 页面加载失败，已自动切换代理重新开始")
                return False

            print("✅ 页面加载完成，开始注册流程...")

            # 开始处理Hitem3D注册流程
            print("🚀 开始处理Hitem3D注册流程...")
            if self.handle_hitem3d_signup_flow():
                print("✅ Hitem3D注册流程完成！")
                return True
            else:
                print("⚠️ Hitem3D注册流程未完成或出现错误")
                return False
                
        except Exception as e:
            print(f"开始注册循环失败: {e}")
            return False

    def restart_registration_cycle(self):
        """重新开始注册循环"""
        try:
            print("🔄 重新开始注册循环...")
            
            # 重置相关变量
            self.current_email = ""
            self.current_verification_code = ""
            self.last_email_content = ""
            self.last_verification_code = ""
            
            # 生成新的随机昵称
            self.nickname = self.generate_random_nickname()
            print(f"🎭 生成新昵称: {self.nickname}")
            
            # 清空验证码文件
            self.clear_verification_code()
            
            # 直接点击Open按钮继续循环
            return self.continue_from_proxy_switch()
                
        except Exception as e:
            print(f"重新开始注册循环失败: {e}")
            return False

    def create_registration_completed_marker(self):
        """创建注册完成标记文件"""
        try:
            with open("registration_completed.txt", 'w', encoding='utf-8') as f:
                f.write(f"Registration completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("✅ 已创建注册完成标记文件")
        except Exception as e:
            print(f"创建注册完成标记文件失败: {e}")

    def stop_file_monitoring(self):
        """停止文件监控"""
        self.monitoring_email_file = False
        self.monitoring_verification_code_file = False
        print("已停止文件监控")

    def run(self):
        """运行主流程"""
        print("=" * 60)
        print("🚀 hitem3d_registration.py 启动")
        print(f"📍 进程ID: {os.getpid()}")
        print(f"📂 工作目录: {os.getcwd()}")
        print(f"🕒 启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)

        try:
            # 1. 设置浏览器
            print("🔧 开始设置浏览器...")
            if not self.setup_browser():
                print("❌ 浏览器设置失败")
                return False
            print("✅ 浏览器设置成功")

            # 2. 开始循环注册流程
            print("🔄 开始自动循环注册流程...")
            print("💡 脚本将自动循环注册，每次注册成功后会自动切换代理继续")
            print("💡 按 Ctrl+C 可随时停止循环")
            
            # 开始第一次注册循环
            self.start_registration_cycle()

            # 保持脚本运行，等待用户操作
            try:
                print("脚本正在后台运行，按 Ctrl+C 可退出")
                while True:
                    time.sleep(10)  # 每10秒检查一次，保持脚本运行
            except KeyboardInterrupt:
                print("\n用户中断程序")

        except Exception as e:
            print(f"❌ 运行过程中出错: {e}")
            import traceback
            print("详细错误信息:")
            traceback.print_exc()
        finally:
            # 停止文件监控
            self.stop_file_monitoring()
            
            print("\n脚本执行完成，浏览器将保持打开状态")
            print("=" * 50)
            print("✅ Hitem3D注册流程已完成！")
            print("浏览器将保持打开状态，您可以:")
            print("1. 查看注册结果")
            print("2. 手动进行后续操作")
            print("3. 检查账户是否创建成功")
            print("4. 关闭浏览器窗口退出")
            print("=" * 50)

            print("浏览器将保持打开状态，程序继续运行...")

def main():
    """主函数"""
    automation = Hitem3DRegistrationAutomation()
    automation.run()

if __name__ == "__main__":
    main() 