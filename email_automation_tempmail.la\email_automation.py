#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时邮箱自动化脚本
自动获取临时邮箱，等待邮件，提取链接并保存
"""

import time
import re
import json
import pyperclip

import subprocess
import sys
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EmailAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.results = []
        self.email_file = "current_email.txt"
        self.request_file = "request_new_email.txt"
        self.monitoring_requests = False
        self.current_email = ""
        self.refresh_count = 0  # 刷新计数器（用于100次重启）
        self.proxy_switch_count = 0  # 代理切换计数器（用于20次切换代理）
        self.switch_proxy_file = "switch_proxy_request.txt"  # 切换代理请求文件
        self.reset_count_file = "reset_refresh_count.txt"  # 重置刷新计数通知文件
        self.monitoring_process = False  # 进程监控标志
        self.process_monitor_thread = None  # 进程监控线程
        self.monitoring_bfl_login = False  # bfl_login_automation.py进程监控标志
        self.bfl_login_monitor_thread = None  # bfl_login_automation.py进程监控线程
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器"""
        chrome_options = Options()

        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 如果启用无头模式（推荐用于后台运行）
        if headless:
            chrome_options.add_argument('--headless=new')  # 使用新的无头模式
            chrome_options.add_argument('--window-size=1920,1080')  # 设置窗口大小
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            logger.info("启用无头浏览器模式（后台运行）")
        else:
            # 非无头模式，设置为后台运行友好
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            logger.info("启用后台运行优化模式")

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            logger.info("浏览器启动成功")
            return True
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            return False
    
    def open_website(self):
        """打开临时邮箱网站"""
        try:
            self.driver.get("https://tempmail.la/zh-CN")
            logger.info("正在加载 tempmail.la...")
            
            # 等待页面主要元素加载完成而不是固定等待
            try:
                # 等待标题或主要元素出现
                self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//h3[contains(text(), 'Temp Mail')]"))
                )
                logger.info("tempmail.la 页面加载完成")
            except TimeoutException:
                logger.warning("等待页面加载超时，但继续执行")
            
            return True
        except Exception as e:
            logger.error(f"打开网站失败: {e}")
            return False
    
    def create_temp_email(self):
        """点击创建临时邮箱按钮并等待弹窗"""
        try:
            # 查找并点击"创建临时邮箱"按钮
            create_button = None
            
            # 直接通过文本查找
            try:
                create_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '创建临时邮箱')]"
                )
            except:
                pass
                
            # 如果上面方法失败，通过mail图标查找
            if not create_button:
                try:
                    create_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-mail')]]"
                    )
                except:
                    pass
            
            if create_button:
                # 点击按钮
                create_button.click()
                logger.info("点击创建临时邮箱按钮")
                
                # 等待弹窗出现
                try:
                    success_toast = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, "//div[contains(text(), '临时邮箱已创建')]"))
                    )
                    logger.info("检测到临时邮箱创建成功弹窗")
                    return True
                except TimeoutException:
                    logger.info("未检测到临时邮箱创建成功弹窗，但继续执行")
                    return True
            else:
                # 如果找不到按钮，可能已经创建了邮箱
                logger.info("未找到创建临时邮箱按钮，可能已经创建过邮箱")
                return False
                
        except Exception as e:
            logger.error(f"创建临时邮箱失败: {e}")
            return False
    
    def get_email_address(self):
        """获取当前邮箱地址并写入共享文件"""
        try:
            # 等待邮箱地址显示（减少等待时间）
            logger.info("正在获取邮箱地址...")
            time.sleep(2)  # 减少等待时间
            
            email_address = None
            
            # 方法调整：优先使用成功率高的方法3
            # 方法3: 从页面源码中提取邮箱地址（成功率高）
            try:
                page_source = self.driver.page_source
                # 使用正则表达式从页面源码中提取邮箱地址
                import re
                email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
                matches = re.findall(email_pattern, page_source)
                if matches:
                    # 过滤掉不可能的邮箱地址（如包含在HTML属性中的）
                    for match in matches:
                        if not match.endswith('.png') and not match.endswith('.jpg') and '@' in match:
                            email_address = match
                            logger.info(f"成功获取邮箱地址: {email_address}")
                            break
            except Exception as e:
                # 仅在调试级别记录错误
                logger.debug(f"从页面源码提取邮箱地址失败: {e}")
            
            # 如果方法3失败，尝试方法2
            if not email_address:
                try:
                    # 查找可能包含邮箱地址的元素
                    email_elements = self.driver.find_elements(By.XPATH, "//p[contains(@class, 'font-bold') and contains(text(), '@')]")
                    if email_elements:
                        text = email_elements[0].text.strip()
                        if '@' in text:
                            email_address = text
                            logger.info(f"成功获取邮箱地址: {email_address}")
                except Exception as e:
                    logger.debug(f"从页面元素获取邮箱地址失败: {e}")
            
            # 如果方法2失败，尝试方法1
            if not email_address:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, 'input[readonly]')
                    email_address = email_input.get_attribute('value')
                    if email_address:
                        logger.info(f"成功获取邮箱地址: {email_address}")
                except Exception as e:
                    logger.debug(f"从输入框获取邮箱地址失败: {e}")
            
            # 如果所有方法都失败
            if not email_address:
                logger.error("无法获取邮箱地址")
                try:
                    screenshot_path = f"email_error_{int(time.time())}.png"
                    self.driver.save_screenshot(screenshot_path)
                    logger.error(f"已保存页面截图: {screenshot_path}")
                except:
                    pass
                return None
            
            # 保存当前邮箱地址
            self.current_email = email_address

            # 写入共享文件
            self.write_email_to_file(email_address)

            # 尝试使用复制按钮
            copied_with_button = False
            
            # 直接使用CSS选择器（前面日志显示方法3最成功）
            try:
                copy_button = self.driver.find_element(
                    By.CSS_SELECTOR, "button svg.lucide-copy"
                )
                # 点击按钮而不是SVG
                self.driver.execute_script("arguments[0].closest('button').click();", copy_button)
                logger.info("已复制邮箱地址到剪贴板")
                copied_with_button = True
                time.sleep(0.5)
            except Exception as e:
                logger.debug(f"使用CSS选择器定位复制按钮失败: {e}")
                
                # 备用方法：通过XPath
                if not copied_with_button:
                    try:
                        copy_button = self.driver.find_element(
                            By.XPATH, "//div[contains(@class, 'rounded border')]/div[contains(@class, 'flex')]/button"
                        )
                        copy_button.click()
                        logger.info("已复制邮箱地址到剪贴板")
                        copied_with_button = True
                    except Exception as e2:
                        logger.debug(f"使用XPath定位复制按钮失败: {e2}")
            
            # 无论网站复制按钮是否成功，都使用统一的复制方法确保auto.js能收到
            logger.info("使用统一方法复制邮箱地址并更新文件")
            self.copy_email_and_update_file(email_address)
            
            logger.info(f"邮箱地址已写入文件并复制到剪贴板: {email_address} (刷新计数: {self.refresh_count})")
            return email_address
        except Exception as e:
            logger.error(f"获取邮箱地址失败: {e}")
            return None

    def write_email_to_file(self, email_address):
        """将邮箱地址写入共享文件"""
        try:
            with open(self.email_file, 'w', encoding='utf-8') as f:
                f.write(email_address)
            logger.info(f"邮箱地址已写入文件: {email_address}")
        except Exception as e:
            logger.error(f"写入邮箱文件失败: {e}")

    def copy_email_and_update_file(self, email_address):
        """复制邮箱地址到剪切板并更新文件"""
        try:
            # 使用多种方法复制到剪贴板，确保auto.js能收到
            copy_success = False

            # 方法1: 使用pyperclip
            try:
                pyperclip.copy(email_address)
                logger.info(f"方法1: 通过pyperclip复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.warning(f"pyperclip复制失败: {e}")

            # 方法2: 尝试使用JavaScript通过Clipboard API复制
            try:
                copy_script = f"""
                    (async () => {{
                        try {{
                            await navigator.clipboard.writeText('{email_address}');
                            console.log('通过Clipboard API复制成功');
                            return true;
                        }} catch (e) {{
                            console.error('Clipboard API复制失败:', e);
                            return false;
                        }}
                    }})();
                """
                self.driver.execute_script(copy_script)
                logger.info(f"方法2: 通过浏览器Clipboard API复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.debug(f"通过JavaScript复制失败: {e}")

            # 方法3: 创建一个临时输入框并执行复制命令
            try:
                temp_input_script = f"""
                    var tempInput = document.createElement('textarea');
                    tempInput.value = '{email_address}';
                    document.body.appendChild(tempInput);
                    tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild(tempInput);
                    console.log('通过execCommand复制成功');
                    return true;
                """
                self.driver.execute_script(temp_input_script)
                logger.info(f"方法3: 通过execCommand复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.debug(f"通过execCommand复制失败: {e}")

            # 方法4: 再次使用pyperclip确保复制成功
            try:
                import time
                time.sleep(0.1)  # 短暂等待
                pyperclip.copy(email_address)
                logger.info(f"方法4: 再次通过pyperclip复制邮箱地址: {email_address}")
                copy_success = True
            except Exception as e:
                logger.debug(f"第二次pyperclip复制失败: {e}")

            if copy_success:
                logger.info(f"✅ 邮箱地址已成功复制到剪切板: {email_address}")
            else:
                logger.warning(f"⚠️ 所有复制方法都失败了: {email_address}")

            # 更新文件
            self.write_email_to_file(email_address)

            # 更新当前邮箱
            self.current_email = email_address

        except Exception as e:
            logger.error(f"复制邮箱地址并更新文件失败: {e}")

    def monitor_new_email_requests(self):
        """监控新邮箱请求"""
        import threading
        import time

        def monitor_thread():
            logger.info("开始监控新邮箱请求...")
            self.monitoring_requests = True

            while self.monitoring_requests:
                try:
                    if os.path.exists(self.request_file):
                        logger.info("检测到新邮箱请求，生成新邮箱...")
                        # 删除请求文件
                        os.remove(self.request_file)

                        # 生成新邮箱地址
                        if self.generate_new_address():
                            # 获取新邮箱地址并写入文件（get_email_address方法已包含复制到剪贴板）
                            new_email = self.get_email_address()
                            if new_email:
                                self.current_email = new_email  # 更新当前邮箱
                                logger.info(f"新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                                logger.info(f"刷新计数器已重置为0")
                            else:
                                logger.error("生成新邮箱后无法获取地址")
                        else:
                            logger.error("生成新邮箱失败")

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    logger.error(f"监控新邮箱请求时出错: {e}")
                    time.sleep(2)

        # 在后台线程中启动监控
        monitor_thread_obj = threading.Thread(target=monitor_thread, daemon=True)
        monitor_thread_obj.start()

    def stop_monitoring_requests(self):
        """停止监控新邮箱请求"""
        self.monitoring_requests = False
        logger.info("已停止监控新邮箱请求")

    def restart_open_register_new(self):
        """重启open_register_new.py项目"""
        try:
            logger.info("开始重启open_register_new.py项目...")

            # 检查open_register_new.py文件是否存在
            if not os.path.exists('open_register_new.py'):
                logger.error("未找到 open_register_new.py 文件")
                return False

            # 第一步：关闭所有现有的open_register_new.py进程
            self.kill_existing_processes()

            # 等待一段时间确保进程完全关闭
            time.sleep(2)

            # 第二步：启动新的open_register_new.py进程
            logger.info("启动新的open_register_new.py进程...")

            # 尝试多种启动方式
            success = False

            # 方法1：使用CREATE_NEW_CONSOLE标志启动新窗口（Windows）
            if os.name == 'nt':
                try:
                    import subprocess
                    # 创建唯一的时间戳，避免临时目录冲突
                    timestamp = int(time.time() * 1000)
                    env = os.environ.copy()
                    env['CHROME_TEMP_SUFFIX'] = str(timestamp)  # 传递时间戳给子进程

                    process = subprocess.Popen(
                        [sys.executable, 'open_register_new.py'],
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        cwd=os.getcwd(),
                        env=env
                    )
                    logger.info(f"方法1：open_register_new.py已在新控制台启动，进程ID: {process.pid}，时间戳: {timestamp}")
                    time.sleep(8)  # 等待更长时间，让Chrome完全启动

                    if process.poll() is None:
                        logger.info("方法1成功：open_register_new.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法1失败：进程启动后立即结束")
                except Exception as e:
                    logger.warning(f"方法1失败: {e}")

            # 方法2：如果方法1失败，尝试使用start命令（Windows）
            if not success and os.name == 'nt':
                try:
                    timestamp = int(time.time() * 1000)
                    cmd = f'set CHROME_TEMP_SUFFIX={timestamp} && python "{os.path.abspath("open_register_new.py")}"'
                    process = subprocess.Popen(
                        ['start', 'cmd', '/k', cmd],
                        shell=True,
                        cwd=os.getcwd()
                    )
                    logger.info(f"方法2：使用start命令启动open_register_new.py，时间戳: {timestamp}")
                    time.sleep(5)
                    success = True  # start命令通常会成功
                except Exception as e:
                    logger.warning(f"方法2失败: {e}")

            # 方法3：后台启动但保持输出重定向到文件
            if not success:
                try:
                    timestamp = int(time.time() * 1000)
                    log_filename = f'open_register_new_output_{timestamp}.log'
                    env = os.environ.copy()
                    env['CHROME_TEMP_SUFFIX'] = str(timestamp)

                    with open(log_filename, 'w', encoding='utf-8') as log_file:
                        process = subprocess.Popen(
                            [sys.executable, 'open_register_new.py'],
                            stdout=log_file,
                            stderr=subprocess.STDOUT,
                            text=True,
                            cwd=os.getcwd(),
                            env=env
                        )
                    logger.info(f"方法3：open_register_new.py已后台启动，进程ID: {process.pid}，输出重定向到{log_filename}")
                    time.sleep(8)

                    if process.poll() is None:
                        logger.info("方法3成功：open_register_new.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法3失败：进程启动后立即结束")
                        # 读取日志文件查看错误
                        try:
                            with open(log_filename, 'r', encoding='utf-8') as f:
                                output = f.read()
                                if output:
                                    logger.error(f"进程输出: {output}")
                        except:
                            pass
                except Exception as e:
                    logger.warning(f"方法3失败: {e}")

            if success:
                # 等待一段时间后验证进程是否真的在运行
                logger.info("等待10秒后验证进程是否成功启动...")
                time.sleep(10)

                # 进行验证检查
                logger.info("开始验证进程是否成功启动...")
                is_running = self.check_open_register_new_running()

                if is_running:
                    logger.info("✅ open_register_new.py重启成功，进程正在运行")
                    return True
                else:
                    logger.warning("⚠️ 第一次验证失败，等待5秒后再次验证...")
                    time.sleep(5)

                    # 第二次验证
                    is_running_second = self.check_open_register_new_running()
                    if is_running_second:
                        logger.info("✅ 第二次验证成功，open_register_new.py进程正在运行")
                        return True
                    else:
                        logger.warning("⚠️ 两次验证都失败，进程可能启动后立即停止")
                        return False
            else:
                logger.error("所有启动方法都失败了")
                return False

        except Exception as e:
            logger.error(f"重启open_register_new.py失败: {e}")
            return False

    def kill_existing_processes(self):
        """关闭所有现有的open_register_new.py进程"""
        try:
            import psutil
            logger.info("正在查找并关闭现有的open_register_new.py进程...")

            killed_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查进程命令行是否包含open_register_new.py
                    if proc.info['cmdline'] and any('open_register_new.py' in arg for arg in proc.info['cmdline']):
                        logger.info(f"发现open_register_new.py进程 PID: {proc.info['pid']}")
                        proc.terminate()  # 先尝试优雅关闭
                        try:
                            proc.wait(timeout=5)  # 等待5秒
                            logger.info(f"进程 {proc.info['pid']} 已优雅关闭")
                        except psutil.TimeoutExpired:
                            # 如果5秒内没有关闭，强制杀死
                            proc.kill()
                            logger.info(f"进程 {proc.info['pid']} 已强制关闭")
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # 进程可能已经不存在或无权限访问
                    continue

            if killed_count > 0:
                logger.info(f"已关闭 {killed_count} 个open_register_new.py进程")
            else:
                logger.info("未发现运行中的open_register_new.py进程")

        except ImportError:
            # 如果没有psutil库，使用系统命令
            logger.warning("未安装psutil库，使用系统命令关闭进程...")
            self.kill_processes_with_system_command()
        except Exception as e:
            logger.error(f"关闭现有进程时出错: {e}")
            # 尝试使用系统命令作为备选方案
            self.kill_processes_with_system_command()

    def kill_processes_with_system_command(self):
        """使用系统命令关闭open_register_new.py进程"""
        try:
            # Windows系统使用wmic命令精确查找
            if os.name == 'nt':
                logger.info("使用Windows系统命令查找open_register_new.py进程...")

                # 使用wmic命令查找包含open_register_new.py的进程
                result = subprocess.run([
                    'wmic', 'process', 'where',
                    'CommandLine like "%open_register_new.py%"',
                    'get', 'ProcessId,CommandLine', '/format:csv'
                ], capture_output=True, text=True, encoding='gbk')

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    killed_count = 0

                    for line in lines:
                        if line.strip() and 'open_register_new.py' in line:
                            # 解析CSV格式的输出
                            parts = line.split(',')
                            if len(parts) >= 3:
                                try:
                                    pid = parts[2].strip()  # ProcessId通常在第3列
                                    if pid and pid.isdigit():
                                        logger.info(f"发现open_register_new.py进程 PID: {pid}")

                                        # 先尝试优雅关闭
                                        result_terminate = subprocess.run(
                                            ['taskkill', '/PID', pid, '/T'],
                                            capture_output=True, check=False
                                        )

                                        if result_terminate.returncode == 0:
                                            logger.info(f"进程 {pid} 已优雅关闭")
                                        else:
                                            # 强制关闭
                                            subprocess.run(
                                                ['taskkill', '/PID', pid, '/F', '/T'],
                                                capture_output=True, check=False
                                            )
                                            logger.info(f"进程 {pid} 已强制关闭")

                                        killed_count += 1
                                        time.sleep(0.5)  # 等待进程关闭
                                except (ValueError, IndexError):
                                    continue

                    if killed_count > 0:
                        logger.info(f"使用系统命令已关闭 {killed_count} 个open_register_new.py进程")
                    else:
                        logger.info("未发现运行中的open_register_new.py进程")
                else:
                    logger.warning("wmic命令执行失败，尝试备用方法...")
                    # 备用方法：使用tasklist + findstr
                    self.kill_processes_with_tasklist_fallback()

            else:
                # Linux/Mac系统使用pkill命令
                result = subprocess.run(
                    ['pkill', '-f', 'open_register_new.py'],
                    capture_output=True, text=True
                )
                if result.returncode == 0:
                    logger.info("已使用pkill命令关闭open_register_new.py进程")
                else:
                    logger.info("未发现运行中的open_register_new.py进程")

        except Exception as e:
            logger.error(f"使用系统命令关闭进程失败: {e}")
            # 尝试备用方法
            if os.name == 'nt':
                self.kill_processes_with_tasklist_fallback()

    def kill_processes_with_tasklist_fallback(self):
        """Windows系统的备用进程关闭方法"""
        try:
            logger.info("使用备用方法查找open_register_new.py进程...")

            # 获取所有python进程
            result = subprocess.run([
                'tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'
            ], capture_output=True, text=True, encoding='gbk')

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                python_pids = []

                # 提取所有python进程的PID
                for line in lines[1:]:  # 跳过标题行
                    if line.strip():
                        parts = line.split(',')
                        if len(parts) >= 2:
                            pid = parts[1].strip('"')
                            if pid.isdigit():
                                python_pids.append(pid)

                # 检查每个python进程的命令行
                killed_count = 0
                for pid in python_pids:
                    try:
                        # 使用wmic获取进程的命令行
                        cmd_result = subprocess.run([
                            'wmic', 'process', 'where', f'ProcessId={pid}',
                            'get', 'CommandLine', '/format:value'
                        ], capture_output=True, text=True, encoding='gbk')

                        if cmd_result.returncode == 0 and 'open_register_new.py' in cmd_result.stdout:
                            logger.info(f"发现open_register_new.py进程 PID: {pid}")

                            # 关闭进程
                            subprocess.run(['taskkill', '/PID', pid, '/F', '/T'],
                                         capture_output=True, check=False)
                            logger.info(f"已关闭进程 PID: {pid}")
                            killed_count += 1
                            time.sleep(0.5)

                    except Exception:
                        continue

                if killed_count > 0:
                    logger.info(f"备用方法已关闭 {killed_count} 个open_register_new.py进程")
                else:
                    logger.info("备用方法未发现运行中的open_register_new.py进程")

        except Exception as e:
            logger.error(f"备用方法关闭进程失败: {e}")

    def check_open_register_new_running(self):
        """检测open_register_new.py进程是否正在运行"""
        try:
            import psutil
            logger.info("检查open_register_new.py进程是否正在运行...")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查进程命令行是否包含open_register_new.py
                    if proc.info['cmdline'] and any('open_register_new.py' in arg for arg in proc.info['cmdline']):
                        logger.info(f"发现运行中的open_register_new.py进程 PID: {proc.info['pid']}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            logger.info("未发现运行中的open_register_new.py进程")
            return False

        except ImportError:
            # 如果没有psutil库，使用系统命令
            logger.warning("未安装psutil库，使用系统命令检查进程...")
            return self.check_process_with_system_command()
        except Exception as e:
            logger.error(f"检查进程时出错: {e}")
            return False

    def check_process_with_system_command(self):
        """使用系统命令检查open_register_new.py进程是否运行"""
        try:
            if os.name == 'nt':
                # Windows系统使用wmic命令，与关闭进程时使用相同的方法
                result = subprocess.run([
                    'wmic', 'process', 'where',
                    'CommandLine like "%open_register_new.py%"',
                    'get', 'ProcessId,CommandLine', '/format:csv'
                ], capture_output=True, text=True, encoding='gbk')

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    found_processes = []

                    for line in lines:
                        if line.strip() and 'open_register_new.py' in line:
                            # 解析CSV格式的输出
                            parts = line.split(',')
                            if len(parts) >= 3:
                                try:
                                    pid = parts[2].strip()  # ProcessId通常在第3列
                                    if pid and pid.isdigit():
                                        found_processes.append(pid)
                                except (ValueError, IndexError):
                                    continue

                    if found_processes:
                        logger.info(f"通过系统命令发现运行中的open_register_new.py进程: {found_processes}")
                        return True

                logger.info("通过系统命令未发现运行中的open_register_new.py进程")
                return False
            else:
                # Linux/Mac系统使用pgrep命令
                result = subprocess.run(
                    ['pgrep', '-f', 'open_register_new.py'],
                    capture_output=True, text=True
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.info(f"通过系统命令发现运行中的open_register_new.py进程: {pids}")
                    return True
                else:
                    logger.info("通过系统命令未发现运行中的open_register_new.py进程")
                    return False

        except Exception as e:
            logger.error(f"使用系统命令检查进程失败: {e}")
            return False

    def check_bfl_login_automation_running(self):
        """检测bfl_login_automation.py进程是否正在运行"""
        try:
            import psutil
            logger.debug("检查bfl_login_automation.py进程是否正在运行...")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查进程命令行是否包含bfl_login_automation.py
                    if proc.info['cmdline'] and any('bfl_login_automation.py' in arg for arg in proc.info['cmdline']):
                        logger.debug(f"发现运行中的bfl_login_automation.py进程 PID: {proc.info['pid']}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            logger.debug("未发现运行中的bfl_login_automation.py进程")
            return False

        except ImportError:
            # 如果没有psutil库，使用系统命令
            logger.warning("未安装psutil库，使用系统命令检查bfl_login_automation.py进程...")
            return self.check_bfl_login_automation_with_system_command()
        except Exception as e:
            logger.error(f"检查bfl_login_automation.py进程时出错: {e}")
            return False

    def check_bfl_login_automation_with_system_command(self):
        """使用系统命令检查bfl_login_automation.py进程是否运行"""
        try:
            if os.name == 'nt':
                # Windows系统使用wmic命令
                result = subprocess.run([
                    'wmic', 'process', 'where',
                    'CommandLine like "%bfl_login_automation.py%"',
                    'get', 'ProcessId,CommandLine', '/format:csv'
                ], capture_output=True, text=True, encoding='gbk')

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    found_processes = []

                    for line in lines:
                        if line.strip() and 'bfl_login_automation.py' in line:
                            # 解析CSV格式的输出
                            parts = line.split(',')
                            if len(parts) >= 3:
                                try:
                                    pid = parts[2].strip()  # ProcessId通常在第3列
                                    if pid and pid.isdigit():
                                        found_processes.append(pid)
                                except (ValueError, IndexError):
                                    continue

                    if found_processes:
                        logger.info(f"通过系统命令发现运行中的bfl_login_automation.py进程: {found_processes}")
                        return True

                logger.debug("通过系统命令未发现运行中的bfl_login_automation.py进程")
                return False
            else:
                # Linux/Mac系统使用pgrep命令
                result = subprocess.run(
                    ['pgrep', '-f', 'bfl_login_automation.py'],
                    capture_output=True, text=True
                )
                if result.returncode == 0 and result.stdout.strip():
                    pids = result.stdout.strip().split('\n')
                    logger.info(f"通过系统命令发现运行中的bfl_login_automation.py进程: {pids}")
                    return True
                else:
                    logger.debug("通过系统命令未发现运行中的bfl_login_automation.py进程")
                    return False

        except Exception as e:
            logger.error(f"使用系统命令检查bfl_login_automation.py进程失败: {e}")
            return False

    def start_bfl_login_automation(self):
        """启动bfl_login_automation.py进程"""
        try:
            logger.info("开始启动bfl_login_automation.py进程...")

            # 检查bfl_login_automation.py文件是否存在
            if not os.path.exists('bfl_login_automation.py'):
                logger.error("未找到 bfl_login_automation.py 文件")
                return False

            # 启动新的bfl_login_automation.py进程
            success = False

            # 方法1：使用CREATE_NEW_CONSOLE标志启动新窗口（Windows）
            if os.name == 'nt':
                try:
                    import subprocess
                    process = subprocess.Popen(
                        [sys.executable, 'bfl_login_automation.py', '--monitor'],
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        cwd=os.getcwd()
                    )
                    logger.info(f"方法1：bfl_login_automation.py已在新控制台启动，进程ID: {process.pid}")
                    time.sleep(3)  # 等待进程启动

                    if process.poll() is None:
                        logger.info("方法1成功：bfl_login_automation.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法1失败：进程启动后立即结束")
                except Exception as e:
                    logger.warning(f"方法1失败: {e}")

            # 方法2：如果方法1失败，尝试使用start命令（Windows）
            if not success and os.name == 'nt':
                try:
                    cmd = f'python "{os.path.abspath("bfl_login_automation.py")}" --monitor'
                    process = subprocess.Popen(
                        ['start', 'cmd', '/k', cmd],
                        shell=True,
                        cwd=os.getcwd()
                    )
                    logger.info("方法2：使用start命令启动bfl_login_automation.py")
                    time.sleep(3)
                    success = True  # start命令通常会成功
                except Exception as e:
                    logger.warning(f"方法2失败: {e}")

            # 方法3：后台启动
            if not success:
                try:
                    process = subprocess.Popen(
                        [sys.executable, 'bfl_login_automation.py', '--monitor'],
                        cwd=os.getcwd()
                    )
                    logger.info(f"方法3：bfl_login_automation.py已后台启动，进程ID: {process.pid}")
                    time.sleep(3)

                    if process.poll() is None:
                        logger.info("方法3成功：bfl_login_automation.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法3失败：进程启动后立即结束")
                except Exception as e:
                    logger.warning(f"方法3失败: {e}")

            if success:
                # 等待一段时间后验证进程是否真的在运行
                logger.info("等待5秒后验证bfl_login_automation.py进程是否成功启动...")
                time.sleep(5)

                # 进行验证检查
                is_running = self.check_bfl_login_automation_running()

                if is_running:
                    logger.info("✅ bfl_login_automation.py启动成功，进程正在运行")
                    return True
                else:
                    logger.warning("⚠️ 验证失败，bfl_login_automation.py进程可能启动后立即停止")
                    return False
            else:
                logger.error("所有启动方法都失败了")
                return False

        except Exception as e:
            logger.error(f"启动bfl_login_automation.py失败: {e}")
            return False

    def restart_bfl_login_automation(self):
        """重启bfl_login_automation.py进程"""
        try:
            logger.info("开始重启bfl_login_automation.py进程...")

            # 检查bfl_login_automation.py文件是否存在
            if not os.path.exists('bfl_login_automation.py'):
                logger.error("未找到 bfl_login_automation.py 文件")
                return False

            # 第一步：关闭所有现有的bfl_login_automation.py进程
            self.kill_existing_bfl_login_processes()

            # 等待一段时间确保进程完全关闭
            time.sleep(2)

            # 第二步：启动新的bfl_login_automation.py进程
            logger.info("启动新的bfl_login_automation.py进程...")

            # 尝试多种启动方式
            success = False

            # 方法1：使用CREATE_NEW_CONSOLE标志启动新窗口（Windows）
            if os.name == 'nt':
                try:
                    import subprocess
                    process = subprocess.Popen(
                        [sys.executable, 'bfl_login_automation.py', '--monitor'],
                        creationflags=subprocess.CREATE_NEW_CONSOLE,
                        cwd=os.getcwd()
                    )
                    logger.info(f"方法1：bfl_login_automation.py已在新控制台启动，进程ID: {process.pid}")
                    time.sleep(5)  # 等待进程启动

                    if process.poll() is None:
                        logger.info("方法1成功：bfl_login_automation.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法1失败：进程启动后立即结束")
                except Exception as e:
                    logger.warning(f"方法1失败: {e}")

            # 方法2：如果方法1失败，尝试使用start命令（Windows）
            if not success and os.name == 'nt':
                try:
                    cmd = f'python "{os.path.abspath("bfl_login_automation.py")}" --monitor'
                    process = subprocess.Popen(
                        ['start', 'cmd', '/k', cmd],
                        shell=True,
                        cwd=os.getcwd()
                    )
                    logger.info("方法2：使用start命令启动bfl_login_automation.py")
                    time.sleep(3)
                    success = True  # start命令通常会成功
                except Exception as e:
                    logger.warning(f"方法2失败: {e}")

            # 方法3：后台启动但保持输出重定向到文件
            if not success:
                try:
                    log_filename = f'bfl_login_automation_output_{int(time.time())}.log'
                    with open(log_filename, 'w', encoding='utf-8') as log_file:
                        process = subprocess.Popen(
                            [sys.executable, 'bfl_login_automation.py', '--monitor'],
                            stdout=log_file,
                            stderr=subprocess.STDOUT,
                            text=True,
                            cwd=os.getcwd()
                        )
                    logger.info(f"方法3：bfl_login_automation.py已后台启动，进程ID: {process.pid}，输出重定向到{log_filename}")
                    time.sleep(5)

                    if process.poll() is None:
                        logger.info("方法3成功：bfl_login_automation.py进程正在运行")
                        success = True
                    else:
                        logger.warning("方法3失败：进程启动后立即结束")
                except Exception as e:
                    logger.warning(f"方法3失败: {e}")

            if success:
                # 等待一段时间后验证进程是否真的在运行
                logger.info("等待5秒后验证bfl_login_automation.py进程是否成功启动...")
                time.sleep(5)

                # 进行验证检查
                is_running = self.check_bfl_login_automation_running()

                if is_running:
                    logger.info("✅ bfl_login_automation.py重启成功，进程正在运行")
                    return True
                else:
                    logger.warning("⚠️ 验证失败，bfl_login_automation.py进程可能启动后立即停止")
                    return False
            else:
                logger.error("所有启动方法都失败了")
                return False

        except Exception as e:
            logger.error(f"重启bfl_login_automation.py失败: {e}")
            return False

    def start_process_monitoring(self):
        """启动进程监控线程"""
        import threading

        def monitor_process():
            logger.info("开始监控open_register_new.py和bfl_login_automation.py进程状态...")
            self.monitoring_process = True

            while self.monitoring_process:
                try:
                    # 每60秒检查一次进程状态（增加间隔，避免过于频繁）
                    time.sleep(60)

                    if not self.monitoring_process:
                        break

                    # 检查open_register_new.py进程是否在运行
                    logger.debug("开始检查open_register_new.py进程状态...")
                    is_running = self.check_open_register_new_running()

                    if not is_running:
                        logger.warning("⚠️ 检测到open_register_new.py进程已停止，准备自动重启...")

                        # 再次确认进程确实不存在（双重检查）
                        time.sleep(5)
                        is_running_double_check = self.check_open_register_new_running()

                        if not is_running_double_check:
                            logger.warning("双重检查确认：open_register_new.py进程确实已停止")

                            # 尝试重启进程
                            restart_success = False
                            for attempt in range(2):  # 最多尝试2次
                                logger.info(f"第{attempt + 1}次尝试自动重启open_register_new.py")
                                if self.restart_open_register_new():
                                    restart_success = True
                                    logger.info("✅ open_register_new.py自动重启成功")
                                    break
                                else:
                                    if attempt == 0:
                                        logger.warning("第1次自动重启失败，等待5秒后再次尝试...")
                                        time.sleep(5)
                                    else:
                                        logger.error("第2次自动重启也失败")

                            if restart_success:
                                # 进程监控重启成功后也生成新的邮箱地址并复制到剪切板
                                logger.info("进程监控重启成功，开始生成新的邮箱地址...")
                                if self.generate_new_address():
                                    # 获取新邮箱地址并写入文件（get_email_address方法已包含复制到剪贴板）
                                    new_email = self.get_email_address()
                                    if new_email:
                                        self.current_email = new_email  # 更新当前邮箱
                                        logger.info(f"✅ 进程监控重启后新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                                    else:
                                        logger.warning("⚠️ 进程监控重启后生成新邮箱地址失败")
                                else:
                                    logger.warning("⚠️ 进程监控重启后点击生成新地址按钮失败")
                            else:
                                logger.error("❌ open_register_new.py自动重启失败，已尝试2次")
                        else:
                            logger.info("双重检查发现进程实际在运行，可能是临时检测错误")
                    else:
                        logger.debug("open_register_new.py进程运行正常")

                    # 检查bfl_login_automation.py进程是否在运行
                    logger.debug("开始检查bfl_login_automation.py进程状态...")
                    bfl_is_running = self.check_bfl_login_automation_running()

                    if not bfl_is_running:
                        logger.warning("⚠️ 检测到bfl_login_automation.py进程未运行，准备启动...")

                        # 再次确认进程确实不存在（双重检查）
                        time.sleep(3)
                        bfl_is_running_double_check = self.check_bfl_login_automation_running()

                        if not bfl_is_running_double_check:
                            logger.warning("双重检查确认：bfl_login_automation.py进程确实未运行")

                            # 尝试启动进程
                            start_success = False
                            for attempt in range(2):  # 最多尝试2次
                                logger.info(f"第{attempt + 1}次尝试启动bfl_login_automation.py")
                                if self.start_bfl_login_automation():
                                    start_success = True
                                    logger.info("✅ bfl_login_automation.py启动成功")
                                    break
                                else:
                                    if attempt == 0:
                                        logger.warning("第1次启动失败，等待5秒后再次尝试...")
                                        time.sleep(5)
                                    else:
                                        logger.error("第2次启动也失败")

                            if not start_success:
                                logger.error("❌ bfl_login_automation.py启动失败，已尝试2次")
                        else:
                            logger.info("双重检查发现bfl_login_automation.py进程实际在运行，可能是临时检测错误")
                    else:
                        logger.debug("bfl_login_automation.py进程运行正常")

                except Exception as e:
                    logger.error(f"进程监控过程中出错: {e}")
                    time.sleep(10)  # 出错后等待10秒再继续

        # 在后台线程中启动监控
        self.process_monitor_thread = threading.Thread(target=monitor_process, daemon=True)
        self.process_monitor_thread.start()
        logger.info("进程监控线程已启动（监控open_register_new.py和bfl_login_automation.py）")

    def stop_process_monitoring(self):
        """停止进程监控"""
        self.monitoring_process = False
        logger.info("已停止进程监控（open_register_new.py和bfl_login_automation.py）")

    def request_proxy_switch(self):
        """请求open_register_new.py切换代理"""
        try:
            with open(self.switch_proxy_file, 'w', encoding='utf-8') as f:
                f.write("switch_proxy_request")
            logger.info("已发送代理切换请求到open_register_new.py")
        except Exception as e:
            logger.error(f"发送代理切换请求失败: {e}")

    def check_reset_refresh_count(self):
        """检查是否需要重置代理切换计数"""
        try:
            if os.path.exists(self.reset_count_file):
                logger.info("收到重置代理切换计数请求，重置代理切换计数器")
                self.proxy_switch_count = 0
                # 删除通知文件
                os.remove(self.reset_count_file)
                logger.info("代理切换计数器已重置为0")
        except Exception as e:
            logger.error(f"检查重置代理切换计数时出错: {e}")

    def click_refresh_button(self):
        """点击刷新邮件按钮"""
        try:
            # 使用JavaScript强制触发页面活动，确保后台运行时也能正常操作
            try:
                self.driver.execute_script("""
                    // 强制触发页面活动事件
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    // 强制刷新页面内容
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以进行刷新操作")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            # 检查是否需要重置代理切换计数
            self.check_reset_refresh_count()

            # 多种方式查找刷新按钮
            refresh_button = None

            # 方法1: 查找包含刷新邮件文本和refresh-cw图标的按钮
            try:
                refresh_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '刷新邮件') and .//svg[contains(@class, 'lucide-refresh-cw')]]"
                )
                logger.debug("通过方法1找到刷新按钮")
            except:
                pass

            # 方法2: 只查找包含"刷新邮件"文本的按钮
            if not refresh_button:
                try:
                    refresh_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '刷新邮件')]"
                    )
                    logger.debug("通过方法2找到刷新按钮")
                except:
                    pass

            # 方法3: 查找包含"刷新"文本的按钮
            if not refresh_button:
                try:
                    refresh_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '刷新')]"
                    )
                    logger.debug("通过方法3找到刷新按钮")
                except:
                    pass

            # 方法4: 通过refresh-cw图标查找
            if not refresh_button:
                try:
                    refresh_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-refresh-cw')]]"
                    )
                    logger.debug("通过方法4找到刷新按钮")
                except:
                    pass

            if refresh_button:
                # 尝试点击按钮
                try:
                    refresh_button.click()
                    logger.debug("成功点击刷新按钮")
                except Exception as click_error:
                    # 如果普通点击失败，尝试JavaScript点击
                    try:
                        self.driver.execute_script("arguments[0].click();", refresh_button)
                        logger.debug("通过JavaScript成功点击刷新按钮")
                    except Exception as js_error:
                        logger.error(f"点击刷新按钮失败: 普通点击错误={click_error}, JS点击错误={js_error}")
                        return False

                # 增加两个计数器
                self.refresh_count += 1
                self.proxy_switch_count += 1
                logger.info(f"点击刷新邮件按钮 (总计第{self.refresh_count}次，代理切换计数第{self.proxy_switch_count}次)")

                # 每10次刷新复制一遍邮箱地址
                if self.refresh_count % 10 == 0 and self.current_email:
                    logger.info(f"已刷新{self.refresh_count}次，重新复制邮箱地址到剪贴板并更新文件")
                    self.copy_email_and_update_file(self.current_email)

                # 每20次代理切换计数通知open_register_new.py切换代理并生成新邮箱
                if self.proxy_switch_count % 20 == 0 and self.proxy_switch_count > 0:
                    logger.info(f"代理切换计数达到{self.proxy_switch_count}次，通知open_register_new.py切换代理并生成新邮箱")
                    self.request_proxy_switch()

                    # 等待代理切换完成
                    time.sleep(5)

                    # 生成新邮箱并复制
                    logger.info("代理切换后开始生成新邮箱...")
                    if self.generate_new_address():
                        # 获取新邮箱地址（get_email_address方法已包含复制到剪贴板）
                        new_email = self.get_email_address()
                        if new_email:
                            self.current_email = new_email  # 更新当前邮箱
                            logger.info(f"✅ 代理切换后新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                        else:
                            logger.warning("⚠️ 代理切换后生成新邮箱地址失败")
                    else:
                        logger.warning("⚠️ 代理切换后点击生成新地址按钮失败")

                # 每100次总刷新重启open_register_new.py并重置所有计数器
                if self.refresh_count >= 100:
                    logger.info(f"总刷新次数达到{self.refresh_count}次，准备重启open_register_new.py")

                    # 尝试重启，如果失败则再尝试一次
                    restart_success = False
                    for attempt in range(2):  # 最多尝试2次
                        logger.info(f"第{attempt + 1}次尝试重启open_register_new.py")
                        if self.restart_open_register_new():
                            restart_success = True
                            break
                        else:
                            if attempt == 0:  # 第一次失败
                                logger.warning("第1次重启失败，等待5秒后再次尝试...")
                                time.sleep(5)
                            else:  # 第二次也失败
                                logger.error("第2次重启也失败")

                    if restart_success:
                        self.refresh_count = 0  # 重置总计数器
                        self.proxy_switch_count = 0  # 重置代理切换计数器
                        logger.info("✅ open_register_new.py重启成功，所有计数器已重置为0")

                        # 重启成功后生成新的邮箱地址并复制到剪切板
                        logger.info("重启成功，开始生成新的邮箱地址...")
                        if self.generate_new_address():
                            # 获取新邮箱地址（get_email_address方法已包含复制到剪贴板）
                            new_email = self.get_email_address()
                            if new_email:
                                self.current_email = new_email  # 更新当前邮箱
                                logger.info(f"✅ 重启后新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                            else:
                                logger.warning("⚠️ 重启后生成新邮箱地址失败")
                        else:
                            logger.warning("⚠️ 重启后点击生成新地址按钮失败")
                    else:
                        logger.error("❌ open_register_new.py重启失败，已尝试2次")

                time.sleep(2)
                return True
            else:
                logger.error("所有方法都无法找到刷新按钮")
                return False

        except Exception as e:
            logger.error(f"点击刷新按钮失败: {e}")
            return False
    
    def check_for_emails(self):
        """检查是否有新邮件"""
        try:
            # 查找邮件列表项
            email_items = self.driver.find_elements(
                By.XPATH, "//li[contains(@class, 'cursor-pointer')]"
            )
            
            if email_items:
                logger.info(f"发现 {len(email_items)} 封邮件")
                return email_items
            else:
                # 备用方法：尝试寻找收件箱标题下的项目
                try:
                    inbox_div = self.driver.find_element(
                        By.XPATH, "//div[.//h3[contains(text(), '收件箱')]]"
                    )
                    email_items = inbox_div.find_elements(
                        By.XPATH, ".//li[contains(@class, 'cursor-pointer')]"
                    )
                    if email_items:
                        logger.info(f"备用方法发现 {len(email_items)} 封邮件")
                        return email_items
                except:
                    pass
                
                logger.info("暂无邮件")
                return []
        except Exception as e:
            logger.error(f"检查邮件失败: {e}")
            return []
    
    def click_email_and_extract_link(self, email_element):
        """点击邮件并提取链接"""
        try:
            # 先滚动到元素位置确保可见
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", email_element)
                logger.info("滚动到邮件位置")
                time.sleep(1)  # 等待滚动完成
            except Exception as e:
                logger.debug(f"滚动到邮件位置失败: {e}")

            # 尝试多种方式点击邮件
            clicked = False
            
            # 方法1: 使用JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", email_element)
                logger.info("通过JavaScript点击邮件")
                clicked = True
            except Exception as e:
                logger.debug(f"JavaScript点击邮件失败: {e}")
            
            # 方法2: 如果JavaScript点击失败，尝试常规点击
            if not clicked:
                try:
                    # 先确保元素可交互
                    self.wait.until(EC.element_to_be_clickable((By.XPATH, "//li[contains(@class, 'cursor-pointer')]")))
                    email_element.click()
                    logger.info("通过普通方式点击邮件")
                    clicked = True
                except Exception as e:
                    logger.warning(f"普通点击邮件失败: {e}")
            
            # 方法3: 使用坐标点击
            if not clicked:
                try:
                    # 获取元素位置
                    location = email_element.location
                    size = email_element.size
                    center_x = location['x'] + size['width'] // 2
                    center_y = location['y'] + size['height'] // 2
                    
                    # 使用ActionChains点击特定坐标
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(self.driver)
                    actions.move_to_element(email_element).pause(1).click().perform()
                    logger.info(f"通过ActionChains点击邮件坐标({center_x}, {center_y})")
                    clicked = True
                except Exception as e:
                    logger.warning(f"通过坐标点击邮件失败: {e}")
            
            if not clicked:
                logger.error("所有点击方法都失败")
                return None
            
            logger.info("点击邮件")

            # 使用JavaScript强制触发页面活动，确保内容加载（即使在后台）
            try:
                self.driver.execute_script("""
                    // 强制触发页面活动事件
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    // 强制刷新页面内容
                    if (document.hidden) {
                        document.hidden = false;
                    }
                """)
                logger.debug("触发页面活动事件以确保后台加载")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            time.sleep(5)  # 增加等待时间，确保邮件内容完全加载

            # 查找iframe中的链接，增加重试机制
            iframe = None
            for attempt in range(5):  # 增加到5次尝试
                try:
                    logger.info(f"第{attempt + 1}次尝试查找iframe...")

                    # 先尝试强制刷新DOM
                    self.driver.execute_script("document.body.offsetHeight;")

                    iframe = self.wait.until(
                        EC.presence_of_element_located((By.TAG_NAME, 'iframe'))
                    )
                    logger.info("成功找到iframe")
                    break
                except TimeoutException:
                    logger.warning(f"第{attempt + 1}次查找iframe超时")
                    if attempt < 4:  # 不是最后一次尝试
                        # 尝试强制触发页面事件
                        try:
                            self.driver.execute_script("""
                                // 强制触发各种事件
                                window.dispatchEvent(new Event('resize'));
                                document.dispatchEvent(new Event('DOMContentLoaded'));
                                // 尝试滚动页面
                                window.scrollTo(0, 100);
                                window.scrollTo(0, 0);
                            """)
                        except:
                            pass
                        time.sleep(3)  # 等待3秒后重试
                        continue
                    else:
                        logger.warning("所有尝试都失败，未找到iframe")
                        # 尝试点击返回按钮
                        self.click_back_button()
                        return None

            if iframe:
                # 获取iframe的srcdoc内容
                srcdoc = iframe.get_attribute('srcdoc')
                if srcdoc:
                    # 使用正则表达式提取链接
                    link_pattern = r'href="([^"]*app\.bfl\.ai[^"]*)"'
                    matches = re.findall(link_pattern, srcdoc)

                    if matches:
                        link = matches[0].replace('&amp;', '&')  # 解码HTML实体
                        logger.info(f"提取到链接: {link}")

                        # 点击返回按钮
                        self.click_back_button()

                        return link
                    else:
                        # 尝试直接从iframe源码中查找任何URL
                        url_pattern = r'https://app\.bfl\.ai/[^"\s]+'
                        url_matches = re.findall(url_pattern, srcdoc)
                        if url_matches:
                            link = url_matches[0].replace('&amp;', '&')
                            logger.info(f"通过备用方法提取到链接: {link}")

                            # 点击返回按钮
                            self.click_back_button()

                            return link
                        else:
                            logger.warning("未找到app.bfl.ai链接")
                            # 记录iframe内容用于调试
                            debug_content = srcdoc[:200] + "..." if len(srcdoc) > 200 else srcdoc
                            logger.debug(f"iframe内容片段: {debug_content}")

                            # 尝试点击返回按钮
                            self.click_back_button()

                            return None

        except Exception as e:
            logger.error(f"点击邮件并提取链接失败: {e}")
            return None

    def click_back_button(self):
        """点击返回按钮"""
        try:
            # 使用JavaScript强制触发页面活动，确保元素可见（即使在后台）
            try:
                self.driver.execute_script("""
                    // 强制触发页面活动事件
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    // 强制刷新页面内容
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    // 强制重新渲染
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以查找返回按钮")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            # 增加重试机制查找返回按钮
            back_button = None
            for attempt in range(5):  # 增加到5次尝试
                logger.info(f"第{attempt + 1}次尝试查找返回按钮...")

                # 强制刷新DOM
                try:
                    self.driver.execute_script("document.body.offsetHeight;")
                except:
                    pass

                # 方法1: 通过文本和图标查找
                try:
                    back_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '返回') and .//svg[contains(@class, 'lucide-chevron-left')]]"
                    )
                    logger.debug("通过方法1找到返回按钮")
                    break
                except:
                    pass

                # 方法2: 只通过文本查找
                if not back_button:
                    try:
                        back_button = self.driver.find_element(
                            By.XPATH, "//button[contains(., '返回')]"
                        )
                        logger.debug("通过方法2找到返回按钮")
                        break
                    except:
                        pass

                # 方法3: 通过图标查找
                if not back_button:
                    try:
                        back_button = self.driver.find_element(
                            By.XPATH, "//button[.//svg[contains(@class, 'lucide-chevron-left')]]"
                        )
                        logger.debug("通过方法3找到返回按钮")
                        break
                    except:
                        pass

                # 如果没找到，等待后重试
                if not back_button and attempt < 4:
                    logger.warning(f"第{attempt + 1}次未找到返回按钮，等待2秒后重试...")
                    # 尝试触发更多事件
                    try:
                        self.driver.execute_script("""
                            window.dispatchEvent(new Event('resize'));
                            document.dispatchEvent(new Event('DOMContentLoaded'));
                        """)
                    except:
                        pass
                    time.sleep(2)

            if back_button:
                # 尝试点击按钮
                try:
                    back_button.click()
                    logger.info("成功点击返回按钮")
                except Exception as click_error:
                    # 如果普通点击失败，尝试JavaScript点击
                    try:
                        self.driver.execute_script("arguments[0].click();", back_button)
                        logger.info("通过JavaScript成功点击返回按钮")
                    except Exception as js_error:
                        logger.error(f"点击返回按钮失败: 普通点击错误={click_error}, JS点击错误={js_error}")
                        return False

                time.sleep(2)  # 等待返回操作完成
                return True
            else:
                logger.warning("未找到返回按钮")
                return False

        except Exception as e:
            logger.error(f"点击返回按钮失败: {e}")
            return False

    def close_email_window(self):
        """关闭邮件窗口"""
        try:
            # 方法1: 按ESC键
            try:
                from selenium.webdriver.common.keys import Keys
                self.driver.find_element(By.TAG_NAME, "body").send_keys(Keys.ESCAPE)
                logger.info("按ESC键关闭邮件窗口")
                time.sleep(1)  # 减少等待时间
                return True
            except Exception as e:
                logger.warning(f"按ESC键失败: {e}")

            # 方法2: 点击页面空白区域
            try:
                # 点击页面左上角空白区域
                self.driver.execute_script("document.elementFromPoint(100, 100).click();")
                logger.info("点击页面空白区域关闭邮件窗口")
                time.sleep(1)  # 减少等待时间
                return True
            except Exception as e:
                logger.warning(f"点击空白区域失败: {e}")

            logger.warning("所有关闭邮件窗口的方法都失败了")
            return False

        except Exception as e:
            logger.error(f"关闭邮件窗口失败: {e}")
            return False
    
    def generate_new_address(self):
        """生成新的邮箱地址"""
        try:
            # 使用JavaScript强制触发页面活动，确保后台运行时也能正常操作
            try:
                self.driver.execute_script("""
                    // 强制触发页面活动事件
                    document.dispatchEvent(new Event('visibilitychange'));
                    document.dispatchEvent(new Event('focus'));
                    window.dispatchEvent(new Event('focus'));
                    // 强制刷新页面内容
                    if (document.hidden) {
                        document.hidden = false;
                    }
                    document.body.offsetHeight;
                """)
                logger.debug("触发页面活动事件以生成新邮箱")
            except Exception as e:
                logger.debug(f"触发页面活动事件失败: {e}")

            # 查找生成新地址按钮 - 现在变成了"换邮箱"按钮
            new_address_button = None
            
            # 方法1: 查找包含"换邮箱"文本和pen-line图标的按钮
            try:
                new_address_button = self.driver.find_element(
                    By.XPATH, "//button[contains(., '换邮箱') and .//svg[contains(@class, 'lucide-pen-line')]]"
                )
                logger.debug("通过方法1找到换邮箱按钮")
            except:
                pass
                
            # 方法2: 只通过"换邮箱"文本查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.XPATH, "//button[contains(., '换邮箱')]"
                    )
                    logger.debug("通过方法2找到换邮箱按钮")
                except:
                    pass
                    
            # 方法3: 通过pen-line图标查找
            if not new_address_button:
                try:
                    new_address_button = self.driver.find_element(
                        By.XPATH, "//button[.//svg[contains(@class, 'lucide-pen-line')]]"
                    )
                    logger.debug("通过方法3找到换邮箱按钮")
                except:
                    pass
            
            if new_address_button:
                new_address_button.click()
                logger.info("点击换邮箱按钮")
                time.sleep(2)  # 减少等待时间到2秒
                return True
            else:
                logger.error("所有方法都无法找到换邮箱按钮")
                return False

        except Exception as e:
            logger.error(f"生成新地址失败: {e}")
            return False
    
    def save_result(self, email, link):
        """保存结果到文件"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'email': email,
            'link': link
        }
        self.results.append(result)
        
        # 保存到JSON文件
        with open('email_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 也保存到文本文件，方便查看
        with open('email_results.txt', 'a', encoding='utf-8') as f:
            f.write(f"{result['timestamp']} | {email} | {link}\n")
        
        logger.info(f"结果已保存: {email} -> {link}")

    def run_bfl_login_automation(self, email, link):
        """运行BFL登录自动化脚本获取API key"""
        try:
            logger.info(f"开始为邮箱 {email} 获取API key")

            # 检查bfl_login_automation.py文件是否存在
            if not os.path.exists('bfl_login_automation.py'):
                logger.error("未找到 bfl_login_automation.py 文件")
                return False

            # 创建临时的单个邮箱文件
            temp_file = 'temp_single_email.txt'
            with open(temp_file, 'w', encoding='utf-8') as f:
                timestamp = datetime.now().isoformat()
                f.write(f"{timestamp} | {email} | {link}\n")

            logger.info("已创建临时邮箱文件，启动BFL登录脚本")

            # 备份原始的email_results.txt
            if os.path.exists('email_results.txt'):
                os.rename('email_results.txt', 'email_results_backup.txt')

            # 将临时文件重命名为email_results.txt
            os.rename(temp_file, 'email_results.txt')

            # 运行bfl_login_automation.py（后台模式）
            result = subprocess.run([sys.executable, 'bfl_login_automation.py', '--background'],
                                  capture_output=True, text=True, encoding='utf-8')

            # 恢复原始文件
            if os.path.exists('email_results_backup.txt'):
                if os.path.exists('email_results.txt'):
                    os.remove('email_results.txt')
                os.rename('email_results_backup.txt', 'email_results.txt')

            if result.returncode == 0:
                logger.info(f"BFL登录脚本执行成功，邮箱 {email} 的API key获取完成")
                return True
            else:
                logger.error(f"BFL登录脚本执行失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"运行BFL登录脚本失败: {e}")

            # 确保恢复原始文件
            try:
                if os.path.exists('email_results_backup.txt'):
                    if os.path.exists('email_results.txt'):
                        os.remove('email_results.txt')
                    os.rename('email_results_backup.txt', 'email_results.txt')
            except:
                pass

            return False
    
    def wait_for_email_continuously(self, email_address):
        """持续刷新直到获取到邮件"""
        logger.info(f"开始持续刷新等待邮件: {email_address}")

        while True:
            try:
                # 点击刷新
                self.click_refresh_button()

                # 检查是否需要更新邮箱地址（在刷新20次切换代理后）
                if self.current_email != email_address:
                    logger.info(f"检测到邮箱地址已更新: {email_address} -> {self.current_email}")
                    email_address = self.current_email

                # 检查邮件
                emails = self.check_for_emails()

                if emails:
                    logger.info(f"收到邮件！当前总刷新次数: {self.refresh_count}")

                    # 检查邮件数量，如果是2封邮件，需要重新生成邮箱
                    if len(emails) >= 2:
                        logger.info(f"检测到 {len(emails)} 封邮件，将重新生成邮箱地址")

                        # 点击第一封邮件并提取链接
                        link = self.click_email_and_extract_link(emails[0])
                        if link:
                            self.save_result(email_address, link)
                            # 注意：不需要关闭邮件窗口，因为click_email_and_extract_link方法已经包含了点击返回按钮的操作

                            # 收到邮件后重置所有计数器
                            logger.info("收到邮件，重置所有计数器")
                            self.refresh_count = 0  # 重置总计数器
                            self.proxy_switch_count = 0  # 重置代理切换计数器
                            logger.info("所有计数器已重置为0")

                            # 检测到2封邮件时，重新点击生成邮箱按钮
                            logger.info("检测到2封邮件，开始重新生成邮箱地址...")
                            if self.generate_new_address():
                                # 获取新邮箱地址（get_email_address方法已包含复制到剪贴板）
                                new_email = self.get_email_address()
                                if new_email:
                                    self.current_email = new_email  # 更新当前邮箱
                                    # 更新传入的email_address参数，这样下次循环会使用新邮箱
                                    email_address = new_email
                                    logger.info(f"✅ 检测到2封邮件后新邮箱已生成、写入文件并复制到剪贴板: {new_email}")
                                    logger.info("继续使用新邮箱地址等待邮件...")
                                    # 不要return，继续循环等待新邮箱的邮件
                                else:
                                    logger.warning("⚠️ 检测到2封邮件后生成新邮箱地址失败")
                                    return True  # 即使生成新邮箱失败，也返回成功（因为已经获取到链接）
                            else:
                                logger.warning("⚠️ 检测到2封邮件后点击生成新地址按钮失败")
                                return True  # 即使生成新邮箱失败，也返回成功（因为已经获取到链接）
                        else:
                            logger.warning("未能提取到链接，但检测到2封邮件，仍然重新生成邮箱")
                            # 注意：不需要关闭邮件窗口，因为click_email_and_extract_link方法已经包含了点击返回按钮的操作

                            # 检测到2封邮件时，即使没有提取到链接也重新生成邮箱
                            logger.info("检测到2封邮件但未提取到链接，仍然重新生成邮箱地址...")
                            if self.generate_new_address():
                                new_email = self.get_email_address()
                                if new_email:
                                    self.current_email = new_email  # 更新当前邮箱
                                    email_address = new_email
                                    logger.info(f"✅ 检测到2封邮件后新邮箱已生成: {new_email}")
                                    logger.info("继续使用新邮箱地址等待邮件...")
                                    # 继续循环，不要return
                                else:
                                    logger.warning("⚠️ 生成新邮箱地址失败，继续等待")
                            else:
                                logger.warning("⚠️ 点击生成新地址按钮失败，继续等待")
                    else:
                        # 只有1封邮件的情况，按原来的逻辑处理
                        logger.info(f"检测到 {len(emails)} 封邮件，按正常流程处理")
                        link = self.click_email_and_extract_link(emails[0])
                        if link:
                            self.save_result(email_address, link)
                            # 注意：不需要关闭邮件窗口，因为click_email_and_extract_link方法已经包含了点击返回按钮的操作

                            # 收到邮件后重置所有计数器
                            logger.info("收到邮件，重置所有计数器")
                            self.refresh_count = 0  # 重置总计数器
                            self.proxy_switch_count = 0  # 重置代理切换计数器
                            logger.info("所有计数器已重置为0")

                            # 暂时跳过运行BFL登录脚本获取API key
                            logger.info("链接已保存，暂时跳过API key获取")
                            # if self.run_bfl_login_automation(email_address, link):
                            #     logger.info(f"邮箱 {email_address} 的API key获取成功")
                            # else:
                            #     logger.warning(f"邮箱 {email_address} 的API key获取失败")

                            return True
                        else:
                            logger.warning("未能提取到链接，继续等待")
                            # 注意：不需要关闭邮件窗口，因为click_email_and_extract_link方法已经包含了点击返回按钮的操作

                if self.refresh_count % 10 == 0:  # 每10次刷新输出一次状态
                    logger.info(f"已刷新 {self.refresh_count} 次，继续等待邮件...")

                time.sleep(3)  # 每3秒刷新一次

            except KeyboardInterrupt:
                logger.info("用户中断等待")
                return False
            except Exception as e:
                logger.error(f"等待邮件过程中出错: {e}")
                time.sleep(5)  # 出错后等待5秒再继续

    def run_automation(self, max_emails=10, headless=False):
        """运行自动化流程"""
        if not self.setup_driver(headless=headless):
            return False

        if not self.open_website():
            return False

        try:
            # 首次启动时点击创建临时邮箱按钮
            self.create_temp_email()
            
            # 启动新邮箱请求监控
            self.monitor_new_email_requests()

            # 启动进程监控
            self.start_process_monitoring()

            # 获取邮箱地址
            email_address = self.get_email_address()
            if not email_address:
                logger.error("无法获取初始邮箱地址")
                return False

            generated_count = 0  # 已生成的邮箱数量计数器

            while generated_count < max_emails:
                logger.info(f"开始第 {generated_count + 1}/{max_emails} 个邮箱处理，当前邮箱: {email_address}")

                # 持续刷新直到获取到邮件
                if self.wait_for_email_continuously(email_address):
                    generated_count += 1
                    logger.info(f"第 {generated_count} 个邮箱完成，成功获取邮件和链接 ({generated_count}/{max_emails})")

                    # 检查是否已达到目标数量
                    if generated_count >= max_emails:
                        logger.info(f"✅ 已完成目标数量 {max_emails} 个邮箱的处理，停止脚本")
                        break
                else:
                    logger.warning(f"第 {generated_count + 1} 个邮箱处理失败或被中断")
                    break

                # 生成新邮箱地址（如果还没达到目标数量）
                if generated_count < max_emails:
                    if self.generate_new_address():
                        # 立即获取新生成的邮箱地址（已包含复制到剪贴板功能）
                        email_address = self.get_email_address()
                        if not email_address:
                            logger.error("生成新地址后无法获取邮箱地址，停止循环")
                            break
                        self.current_email = email_address  # 更新当前邮箱
                        logger.info(f"已生成新邮箱地址并复制到剪贴板，开始下一轮")
                    else:
                        logger.error("生成新地址失败，停止循环")
                        break

            logger.info(f"🎉 自动化完成！共成功处理 {len(self.results)} 个邮箱，目标: {max_emails} 个")

        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"运行过程中出错: {e}")
        finally:
            # 停止监控
            self.stop_monitoring_requests()
            self.stop_process_monitoring()

            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")

def main():
    """主函数"""
    automation = EmailAutomation()

    print("临时邮箱自动化脚本")
    print("=" * 50)
    print("功能说明：")
    print("- 自动获取临时邮箱地址并写入共享文件")
    print("- 监控来自注册脚本的新邮箱请求")
    print("- 持续刷新直到收到邮件")
    print("- 提取邮件中的链接并保存")
    print("- 检测到2封邮件时自动重新生成邮箱地址")
    print("- 自动调用BFL登录脚本获取API key")
    print("- 自动生成新邮箱地址继续循环")
    print("- 刷新20次无邮件时自动切换代理并生成新邮箱")
    print("- 刷新100次无邮件时自动重启open_register_new.py并重置所有计数器")
    print("- 收到邮件后重置所有计数器")
    print("- 后台监控open_register_new.py进程，自动检测并重启崩溃的进程")
    print("- 完整的端到端自动化流程")
    print("=" * 50)

    # 获取用户输入的邮箱数量
    try:
        max_emails = int(input("请输入要处理的邮箱数量 (默认1000个): ") or "1000")
        if max_emails <= 0:
            max_emails = 1000
            print("输入无效，使用默认值1000个邮箱")
    except ValueError:
        max_emails = 1000
        print("输入无效，使用默认值1000个邮箱")

    # 获取运行模式选择
    print("\n运行模式选择:")
    print("1. 普通模式 (有界面，可以看到浏览器)")
    print("2. 无头模式 (无界面，完全后台运行，推荐)")

    try:
        mode_choice = input("请选择运行模式 (1或2，默认2): ").strip() or "2"
        headless = mode_choice == "2"
        mode_name = "无头模式" if headless else "普通模式"
        print(f"已选择: {mode_name}")
    except:
        headless = True
        print("输入无效，使用默认无头模式")

    print(f"\n将生成 {max_emails} 个邮箱，运行模式: {'无头模式' if headless else '普通模式'}")
    print("每次获取邮箱地址都会自动写入共享文件传递给注册脚本")
    print("刷新20次无邮件时自动切换代理并生成新邮箱，100次无邮件时重启注册脚本")
    print("收到邮件后所有计数器重置，继续下一个邮箱")
    print("获取到链接后会自动调用BFL登录脚本获取API key")
    print("按 Ctrl+C 可随时停止")
    print("=" * 50)

    automation.run_automation(max_emails=max_emails, headless=headless)

if __name__ == "__main__":
    main()
