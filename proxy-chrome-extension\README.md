[EN](#en) &nbsp;&nbsp; [CN](#cn)

<div id='en'></div>

## Changelog:
- 1.1 Fixed conflicts with other Extensions
- 1.2 Launched the function of [general proxies] in Extension
- 1.2.2 Fix proxy settings failed
- 1.2.3 Fix some problems

## Uses proxy in convenient method​
​
Proxy302 focuses on a cheap ,easy-to-use and high-quality proxy network, providing a choice of proxy regions from all over the world.​
​

The extension for Proxy302 is the easiest way to integrate a Proxy302 proxy directly into your Chrome browser.​

​
The Proxy302 extension can help you establish a Chrome browser proxy connection and send concurrent URL requests through millions of residential IPs in just a few steps. Proxy302 High-quality proxies are almost impossible to block or hide when sending requests. Proxy302 extension will improve the efficiency and portability of business use.​

​
To use the Proxy302 extension, you must have an existing Proxy302 account. You can register a Proxy302 account here: [https://proxy302.com](https://proxy302.com)

​
The Proxy302 extension includes the following functionality:​
- Support dynamic and static IP browsing​
- Support setting proxy switch​
- Adjust user proxy​
- Browse from any country​
- Self-defined settings except Proxy's proxy​
- Bypass settings​

​
To learn more about our use of other platforms using the Proxy302 Extension, please visit the following links:[https://dashboard.proxy302.com/extension-doc](https://dashboard.proxy302.com/extension-doc)


### Regarding the use of information:
The Proxy302 extension uses information to provide you with proxy services, and will read your geographic location in compliance with legal requirements. The information used will provide you with continuous service updates, real-time marketing discounts, and data analysis to provide better services.


### Regarding information protection:
Proxy302 does not rent and sell any personal information. In compliance with the law, information may be disclosed to our trusted partners in order to provide you with better service.


### Regarding confidentiality of information:
Proxy302 has internal security tools to prevent information leakage and data loss. We use a range of measures and means to ensure the security of information. However, we cannot guarantee the absolute security of the system. If you are aware of any risk of data leakage, please contact us in time


Contact: <EMAIL>

<br>
<hr>
<br>

<div id='cn'></div>

## 更新日志

- 1.1 解决了与其它扩展冲突问题
- 1.2 上线了【通用代理】的插件功能
- 1.2.2 修复代理设置失败
- 1.2.3 修复了一些问题


## 轻松快捷使用代理
​
Proxy302 主打便宜好用高质量的代理网络， 提供来自世界各地的代理地区选择。​​


Proxy302的扩展是将 Proxy302 代理直接集成到您的Chrome浏览器中的最简单的方式。​
​

Proxy302扩展仅仅需要几步操作就可以帮您建立Chrome浏览器代理连接，并通过百万个住宅IP发送并发URL请求。Proxy302高质量代理发送请求时，几乎不可能被阻止或隐藏。Proxy302扩展会提高业务使用的高效性与便携性。​
​

要使用Proxy302扩展，您必须拥有现有的Proxy302账户。您可以在此注册Proxy302的账户：[https://proxy302.com](https://proxy302.com)
​

Proxy302扩展包括以下功能：​
- 支持动态，静态IP浏览​
- 支持设置代理开关​
- 调整用户代理​
- 从任何国家/地区浏览​
- 自拟定设置除Proxy的代理​
- 白名单设置​
​

要了解更多关于我们使用Proxy302插件使用其他平台的信息，请访问以下链接：[https://dashboard.proxy302.com/extension-doc](https://dashboard.proxy302.com/extension-doc)


### 关于信息使用：
Proxy302扩展通过信息使用来为您提供代理的服务，在遵守法律要求下将会读取您的地理位置。使用的信息将为您持续提供服务的更新，实时的营销折扣，以及数据分析来提供更好的服务。


### 关于信息保护：
Proxy302不会出租和出售任何个人信息。在遵守法律的情况下，可能向我们信任的合作伙伴纰漏信息，以便提供更好的服务给您。


### 关于信息保密：
Proxy302有内部的安全工具来预防信息的泄露和数据的丢失。我们使用一系列措施和手段来确保信息的安全。但我们不能保证系统绝对的安全，如果您意识到有任何数据泄露的风险，请及时与我们联系。

联系方式： <EMAIL>